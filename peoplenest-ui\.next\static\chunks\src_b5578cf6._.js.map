{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        error: \"border-red-500 focus-visible:ring-red-500\",\n        success: \"border-green-500 focus-visible:ring-green-500\",\n      },\n      size: {\n        default: \"h-10\",\n        sm: \"h-9 px-2 text-xs\",\n        lg: \"h-11 px-4\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n  label?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type, \n    variant, \n    size, \n    leftIcon, \n    rightIcon, \n    error, \n    label, \n    helperText,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || React.useId()\n    const hasError = !!error\n    const finalVariant = hasError ? \"error\" : variant\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-foreground mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              inputVariants({ variant: finalVariant, size, className }),\n              leftIcon && \"pl-10\",\n              rightIcon && \"pr-10\"\n            )}\n            ref={ref}\n            id={inputId}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {(error || helperText) && (\n          <p className={cn(\n            \"mt-1 text-xs\",\n            hasError ? \"text-destructive\" : \"text-muted-foreground\"\n          )}>\n            {error || helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,2VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAaF,MAAM,sBAAQ,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAC3B,CAAC,EACC,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,EAAE,EACF,GAAG,OACJ,EAAE;;IACD,MAAM,UAAU,MAAM,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAChC,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,eAAe,WAAW,UAAU;IAE1C,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;4BAAE,SAAS;4BAAc;4BAAM;wBAAU,IACvD,YAAY,SACZ,aAAa;wBAEf,KAAK;wBACL,IAAI;wBACH,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,WAAW,qBAAqB;0BAE/B,SAAS;;;;;;;;;;;;AAKpB;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md\",\n        outlined: \"border-2\",\n        ghost: \"border-transparent shadow-none\",\n      },\n      padding: {\n        none: \"\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {\n  hover?: boolean\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, hover = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        cardVariants({ variant, padding }),\n        hover && \"transition-shadow hover:shadow-md\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { \n  Card, \n  CardHeader, \n  CardFooter, \n  CardTitle, \n  CardDescription, \n  CardContent,\n  cardVariants \n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AASF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aAAa;YAAE;YAAS;QAAQ,IAChC,SAAS,qCACT;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport {\n  Search,\n  Bell,\n  MessageSquare,\n  Settings,\n  User,\n  LogOut,\n  Moon,\n  Sun,\n  Globe,\n  ChevronDown,\n  Plus,\n  Filter,\n  Download,\n  RefreshCw\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { cn } from \"@/lib/utils\"\n\ninterface HeaderProps {\n  title?: string\n  subtitle?: string\n  actions?: React.ReactNode\n  className?: string\n}\n\nexport function Header({ title, subtitle, actions, className }: HeaderProps) {\n  const [showNotifications, setShowNotifications] = useState(false)\n  const [showProfile, setShowProfile] = useState(false)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  const notifications = [\n    {\n      id: 1,\n      title: \"New Employee Onboarding\",\n      message: \"<PERSON> has completed her onboarding checklist\",\n      time: \"2 minutes ago\",\n      unread: true,\n      type: \"success\"\n    },\n    {\n      id: 2,\n      title: \"Leave Request Pending\",\n      message: \"Mike <PERSON> has requested 3 days of annual leave\",\n      time: \"1 hour ago\",\n      unread: true,\n      type: \"warning\"\n    },\n    {\n      id: 3,\n      title: \"Performance Review Due\",\n      message: \"5 performance reviews are due this week\",\n      time: \"3 hours ago\",\n      unread: false,\n      type: \"info\"\n    }\n  ]\n\n  const quickActions = [\n    { name: \"Add Employee\", icon: Plus, action: () => {} },\n    { name: \"Export Data\", icon: Download, action: () => {} },\n    { name: \"Refresh\", icon: RefreshCw, action: () => {} },\n    { name: \"Filter\", icon: Filter, action: () => {} },\n  ]\n\n  return (\n    <header className={cn(\n      \"sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm\",\n      className\n    )}>\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Left Section - Title and Breadcrumb */}\n        <div className=\"flex items-center space-x-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-xl font-semibold text-foreground\">{title}</h1>\n            )}\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-8\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              type=\"text\"\n              placeholder=\"Search employees, documents, or anything...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors\"\n            />\n          </div>\n        </div>\n\n        {/* Right Section - Actions and Profile */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Quick Actions */}\n          {actions && (\n            <div className=\"flex items-center space-x-2 mr-4\">\n              {actions}\n            </div>\n          )}\n\n          {/* Default Quick Actions */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {quickActions.map((action) => (\n              <Button\n                key={action.name}\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={action.action}\n                className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n                title={action.name}\n              >\n                <action.icon className=\"h-4 w-4\" />\n              </Button>\n            ))}\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"h-9 w-9 text-muted-foreground hover:text-foreground relative\"\n            >\n              <Bell className=\"h-4 w-4\" />\n              {notifications.some(n => n.unread) && (\n                <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full\" />\n              )}\n            </Button>\n\n            <AnimatePresence>\n              {showNotifications && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-80 z-50\"\n                >\n                  <Card className=\"shadow-lg border-border\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-border\">\n                        <div className=\"flex items-center justify-between\">\n                          <h3 className=\"font-semibold text-foreground\">Notifications</h3>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {notifications.filter(n => n.unread).length} new\n                          </Badge>\n                        </div>\n                      </div>\n                      <div className=\"max-h-80 overflow-y-auto\">\n                        {notifications.map((notification) => (\n                          <div\n                            key={notification.id}\n                            className={cn(\n                              \"p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors\",\n                              notification.unread && \"bg-primary/5\"\n                            )}\n                          >\n                            <div className=\"flex items-start space-x-3\">\n                              <div className={cn(\n                                \"w-2 h-2 rounded-full mt-2 flex-shrink-0\",\n                                notification.type === \"success\" && \"bg-green-500\",\n                                notification.type === \"warning\" && \"bg-yellow-500\",\n                                notification.type === \"info\" && \"bg-primary\"\n                              )} />\n                              <div className=\"flex-1 min-w-0\">\n                                <p className=\"text-sm font-medium text-foreground\">\n                                  {notification.title}\n                                </p>\n                                <p className=\"text-sm text-muted-foreground mt-1\">\n                                  {notification.message}\n                                </p>\n                                <p className=\"text-xs text-muted-foreground/70 mt-2\">\n                                  {notification.time}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                      <div className=\"p-3 border-t border-border\">\n                        <Button variant=\"ghost\" className=\"w-full text-sm\">\n                          View all notifications\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Messages */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n          >\n            <MessageSquare className=\"h-4 w-4\" />\n          </Button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              onClick={() => setShowProfile(!showProfile)}\n              className=\"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80\"\n            >\n              <Avatar className=\"h-7 w-7\">\n                <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                <AvatarFallback>JD</AvatarFallback>\n              </Avatar>\n              <ChevronDown className=\"h-3 w-3\" />\n            </Button>\n\n            <AnimatePresence>\n              {showProfile && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-56 z-50\"\n                >\n                  <Card className=\"shadow-lg border-gray-200\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-gray-200\">\n                        <div className=\"flex items-center space-x-3\">\n                          <Avatar className=\"h-10 w-10\">\n                            <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                            <AvatarFallback>JD</AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"font-medium text-foreground\">John Doe</p>\n                            <p className=\"text-sm text-muted-foreground\">HR Manager</p>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"py-2\">\n                        {[\n                          { icon: User, label: \"Profile\", href: \"/dashboard\" },\n                          { icon: Settings, label: \"Settings\", href: \"/dashboard\" },\n                          { icon: Moon, label: \"Dark Mode\", href: \"#\" },\n                          { icon: Globe, label: \"Language\", href: \"#\" },\n                        ].map((item) => (\n                          <button\n                            key={item.label}\n                            className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors\"\n                          >\n                            <item.icon className=\"h-4 w-4\" />\n                            <span>{item.label}</span>\n                          </button>\n                        ))}\n                        <hr className=\"my-2\" />\n                        <button className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors\">\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign out</span>\n                        </button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;AAkCO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAe;;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAe,MAAM,6MAAA,CAAA,WAAQ;YAAE,QAAQ,KAAO;QAAE;QACxD;YAAE,MAAM;YAAW,MAAM,mNAAA,CAAA,YAAS;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAU,MAAM,yMAAA,CAAA,SAAM;YAAE,QAAQ,KAAO;QAAE;KAClD;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,kFACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BACE,uBACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;4BAExD,0BACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;8BAMpD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;wBAEZ,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,OAAO,MAAM;oCACtB,WAAU;oCACV,OAAO,OAAO,IAAI;8CAElB,cAAA,6LAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;mCAPlB,OAAO,IAAI;;;;;;;;;;sCAatB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,mBAC/B,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAIpB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAgC;;;;;;8EAC9C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAClC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;wEAAC;;;;;;;;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;gEAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA,aAAa,MAAM,IAAI;0EAGzB,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2CACA,aAAa,IAAI,KAAK,aAAa,gBACnC,aAAa,IAAI,KAAK,aAAa,iBACnC,aAAa,IAAI,KAAK,UAAU;;;;;;sFAElC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FACV,aAAa,KAAK;;;;;;8FAErB,6LAAC;oFAAE,WAAU;8FACV,aAAa,OAAO;;;;;;8FAEvB,6LAAC;oFAAE,WAAU;8FACV,aAAa,IAAI;;;;;;;;;;;;;;;;;;+DArBnB,aAAa,EAAE;;;;;;;;;;kEA4B1B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYjE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAI3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAoB,KAAI;;;;;;8DACzC,6LAAC,qIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;sDAElB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAGzB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,6LAAC,qIAAA,CAAA,cAAW;4EAAC,KAAI;4EAAoB,KAAI;;;;;;sFACzC,6LAAC,qIAAA,CAAA,iBAAc;sFAAC;;;;;;;;;;;;8EAElB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA8B;;;;;;sFAC3C,6LAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAI,WAAU;;4DACZ;gEACC;oEAAE,MAAM,qMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAW,MAAM;gEAAa;gEACnD;oEAAE,MAAM,6MAAA,CAAA,WAAQ;oEAAE,OAAO;oEAAY,MAAM;gEAAa;gEACxD;oEAAE,MAAM,qMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAa,MAAM;gEAAI;gEAC5C;oEAAE,MAAM,uMAAA,CAAA,QAAK;oEAAE,OAAO;oEAAY,MAAM;gEAAI;6DAC7C,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC,KAAK,IAAI;4EAAC,WAAU;;;;;;sFACrB,6LAAC;sFAAM,KAAK,KAAK;;;;;;;mEAJZ,KAAK,KAAK;;;;;0EAOnB,6LAAC;gEAAG,WAAU;;;;;;0EACd,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;GAzPgB;KAAA", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/dashboard/settings/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  <PERSON><PERSON>s,\n  User,\n  Bell,\n  Shield,\n  Palette,\n  Globe,\n  Database,\n  Mail,\n  Smartphone,\n  Key,\n  Users,\n  Building,\n  Calendar,\n  Clock,\n  DollarSign,\n  FileText,\n  Save,\n  RefreshCw,\n  Download,\n  Upload,\n  Trash2,\n  Eye,\n  EyeOff\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Header } from \"@/components/layout/header\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Switch } from \"@/components/ui/switch\"\n\n// Mock settings data\nconst userSettings = {\n  profile: {\n    firstName: \"John\",\n    lastName: \"Doe\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    department: \"Engineering\",\n    position: \"Senior Software Engineer\",\n    avatar: \"/avatars/john.jpg\",\n    timezone: \"America/New_York\",\n    language: \"English\"\n  },\n  notifications: {\n    emailNotifications: true,\n    pushNotifications: true,\n    smsNotifications: false,\n    weeklyReports: true,\n    systemAlerts: true,\n    leaveApprovals: true,\n    payrollUpdates: false,\n    announcementUpdates: true\n  },\n  security: {\n    twoFactorEnabled: true,\n    sessionTimeout: 30,\n    passwordLastChanged: \"2024-01-15\",\n    loginHistory: true,\n    deviceManagement: true\n  },\n  appearance: {\n    theme: \"system\",\n    compactMode: false,\n    showAvatars: true,\n    animationsEnabled: true,\n    sidebarCollapsed: false\n  }\n}\n\nconst companySettings = {\n  general: {\n    companyName: \"PeopleNest Inc.\",\n    companyEmail: \"<EMAIL>\",\n    companyPhone: \"+****************\",\n    address: \"123 Business Ave, Suite 100, City, State 12345\",\n    website: \"https://www.peoplenest.com\",\n    timezone: \"America/New_York\",\n    fiscalYearStart: \"January\"\n  },\n  hrPolicies: {\n    workingHours: \"9:00 AM - 5:00 PM\",\n    workingDays: \"Monday - Friday\",\n    leavePolicyEnabled: true,\n    overtimeEnabled: true,\n    remoteWorkEnabled: true,\n    flexibleHoursEnabled: true\n  },\n  payroll: {\n    payFrequency: \"Bi-weekly\",\n    payrollCurrency: \"USD\",\n    taxCalculationEnabled: true,\n    benefitsEnabled: true,\n    bonusEnabled: true\n  }\n}\n\nexport default function SettingsPage() {\n  const [activeTab, setActiveTab] = useState(\"profile\")\n  const [showPassword, setShowPassword] = useState(false)\n  const [settings, setSettings] = useState(userSettings)\n  const [companyConfig, setCompanyConfig] = useState(companySettings)\n\n  const settingsTabs = [\n    { id: \"profile\", label: \"Profile\", icon: User },\n    { id: \"notifications\", label: \"Notifications\", icon: Bell },\n    { id: \"security\", label: \"Security\", icon: Shield },\n    { id: \"appearance\", label: \"Appearance\", icon: Palette },\n    { id: \"company\", label: \"Company\", icon: Building },\n    { id: \"system\", label: \"System\", icon: Database }\n  ]\n\n  const handleNotificationChange = (key: string, value: boolean) => {\n    setSettings(prev => ({\n      ...prev,\n      notifications: {\n        ...prev.notifications,\n        [key]: value\n      }\n    }))\n  }\n\n  const handleAppearanceChange = (key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      appearance: {\n        ...prev.appearance,\n        [key]: value\n      }\n    }))\n  }\n\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      <Header\n        title=\"Settings\"\n        subtitle=\"Manage your account, preferences, and system configuration\"\n        actions={\n          <div className=\"flex items-center space-x-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              Reset to Default\n            </Button>\n            <Button size=\"sm\">\n              <Save className=\"w-4 h-4 mr-2\" />\n              Save Changes\n            </Button>\n          </div>\n        }\n      />\n\n      <div className=\"flex flex-col lg:flex-row gap-6\">\n        {/* Settings Navigation */}\n        <div className=\"lg:w-64\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <nav className=\"space-y-2\">\n                {settingsTabs.map((tab) => {\n                  const Icon = tab.icon\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? \"bg-primary text-primary-foreground\"\n                          : \"hover:bg-muted text-muted-foreground\"\n                      }`}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      <span>{tab.label}</span>\n                    </button>\n                  )\n                })}\n              </nav>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Settings Content */}\n        <div className=\"flex-1\">\n          {activeTab === \"profile\" && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"space-y-6\"\n            >\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <User className=\"h-5 w-5\" />\n                    <span>Profile Information</span>\n                  </CardTitle>\n                  <CardDescription>Update your personal information and contact details</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"flex items-center space-x-6\">\n                    <Avatar className=\"h-20 w-20\">\n                      <AvatarImage src={settings.profile.avatar} alt=\"Profile\" />\n                      <AvatarFallback className=\"text-lg\">\n                        {settings.profile.firstName[0]}{settings.profile.lastName[0]}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className=\"space-y-2\">\n                      <Button variant=\"outline\" size=\"sm\">\n                        <Upload className=\"h-4 w-4 mr-2\" />\n                        Change Photo\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" className=\"text-destructive\">\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\n                        Remove Photo\n                      </Button>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">First Name</label>\n                      <Input value={settings.profile.firstName} />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Last Name</label>\n                      <Input value={settings.profile.lastName} />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Email</label>\n                      <Input value={settings.profile.email} type=\"email\" />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Phone</label>\n                      <Input value={settings.profile.phone} />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Department</label>\n                      <select className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\">\n                        <option value=\"Engineering\">Engineering</option>\n                        <option value=\"Product\">Product</option>\n                        <option value=\"Design\">Design</option>\n                        <option value=\"Sales\">Sales</option>\n                        <option value=\"Marketing\">Marketing</option>\n                      </select>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Position</label>\n                      <Input value={settings.profile.position} />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Timezone</label>\n                      <select className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\">\n                        <option value=\"America/New_York\">Eastern Time</option>\n                        <option value=\"America/Chicago\">Central Time</option>\n                        <option value=\"America/Denver\">Mountain Time</option>\n                        <option value=\"America/Los_Angeles\">Pacific Time</option>\n                      </select>\n                    </div>\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Language</label>\n                      <select className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\">\n                        <option value=\"English\">English</option>\n                        <option value=\"Spanish\">Spanish</option>\n                        <option value=\"French\">French</option>\n                        <option value=\"German\">German</option>\n                      </select>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          )}\n\n          {activeTab === \"notifications\" && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"space-y-6\"\n            >\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span>Notification Preferences</span>\n                  </CardTitle>\n                  <CardDescription>Choose how you want to be notified about important updates</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Mail className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">Email Notifications</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Receive notifications via email</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.emailNotifications}\n                        onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Bell className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">Push Notifications</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Receive browser push notifications</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.pushNotifications}\n                        onCheckedChange={(checked) => handleNotificationChange('pushNotifications', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Smartphone className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">SMS Notifications</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Receive notifications via text message</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.smsNotifications}\n                        onCheckedChange={(checked) => handleNotificationChange('smsNotifications', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <FileText className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">Weekly Reports</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Receive weekly summary reports</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.weeklyReports}\n                        onCheckedChange={(checked) => handleNotificationChange('weeklyReports', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Shield className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">System Alerts</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Important system and security alerts</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.systemAlerts}\n                        onCheckedChange={(checked) => handleNotificationChange('systemAlerts', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">Leave Approvals</span>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Notifications for leave requests and approvals</p>\n                      </div>\n                      <Switch\n                        checked={settings.notifications.leaveApprovals}\n                        onCheckedChange={(checked) => handleNotificationChange('leaveApprovals', checked)}\n                      />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          )}\n\n          {activeTab === \"security\" && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"space-y-6\"\n            >\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <Shield className=\"h-5 w-5\" />\n                    <span>Security Settings</span>\n                  </CardTitle>\n                  <CardDescription>Manage your account security and privacy settings</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Key className=\"h-4 w-4 text-muted-foreground\" />\n                          <span className=\"font-medium\">Two-Factor Authentication</span>\n                          <Badge variant=\"success\" className=\"text-xs\">Enabled</Badge>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground\">Add an extra layer of security to your account</p>\n                      </div>\n                      <Switch checked={settings.security.twoFactorEnabled} />\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Change Password</label>\n                      <div className=\"space-y-2\">\n                        <div className=\"relative\">\n                          <Input\n                            type={showPassword ? \"text\" : \"password\"}\n                            placeholder=\"Current password\"\n                          />\n                          <Button\n                            variant=\"ghost\"\n                            size=\"icon\"\n                            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6\"\n                            onClick={() => setShowPassword(!showPassword)}\n                          >\n                            {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                          </Button>\n                        </div>\n                        <Input type=\"password\" placeholder=\"New password\" />\n                        <Input type=\"password\" placeholder=\"Confirm new password\" />\n                        <Button size=\"sm\">Update Password</Button>\n                      </div>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Session Timeout</label>\n                      <select className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\">\n                        <option value=\"15\">15 minutes</option>\n                        <option value=\"30\">30 minutes</option>\n                        <option value=\"60\">1 hour</option>\n                        <option value=\"240\">4 hours</option>\n                        <option value=\"480\">8 hours</option>\n                      </select>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm font-medium\">Password Last Changed</span>\n                        <span className=\"text-sm text-muted-foreground\">\n                          {new Date(settings.security.passwordLastChanged).toLocaleDateString()}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <span className=\"font-medium\">Login History</span>\n                        <p className=\"text-sm text-muted-foreground\">Keep track of account access</p>\n                      </div>\n                      <Switch checked={settings.security.loginHistory} />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          )}\n\n          {activeTab === \"appearance\" && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"space-y-6\"\n            >\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center space-x-2\">\n                    <Palette className=\"h-5 w-5\" />\n                    <span>Appearance Settings</span>\n                  </CardTitle>\n                  <CardDescription>Customize the look and feel of your interface</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Theme</label>\n                      <select\n                        value={settings.appearance.theme}\n                        onChange={(e) => handleAppearanceChange('theme', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n                      >\n                        <option value=\"light\">Light</option>\n                        <option value=\"dark\">Dark</option>\n                        <option value=\"system\">System</option>\n                      </select>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <span className=\"font-medium\">Compact Mode</span>\n                        <p className=\"text-sm text-muted-foreground\">Use smaller spacing and elements</p>\n                      </div>\n                      <Switch\n                        checked={settings.appearance.compactMode}\n                        onCheckedChange={(checked) => handleAppearanceChange('compactMode', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <span className=\"font-medium\">Show Avatars</span>\n                        <p className=\"text-sm text-muted-foreground\">Display user profile pictures</p>\n                      </div>\n                      <Switch\n                        checked={settings.appearance.showAvatars}\n                        onCheckedChange={(checked) => handleAppearanceChange('showAvatars', checked)}\n                      />\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"space-y-1\">\n                        <span className=\"font-medium\">Animations</span>\n                        <p className=\"text-sm text-muted-foreground\">Enable smooth transitions and animations</p>\n                      </div>\n                      <Switch\n                        checked={settings.appearance.animationsEnabled}\n                        onCheckedChange={(checked) => handleAppearanceChange('animationsEnabled', checked)}\n                      />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnCA;;;;;;;;;;;AAqCA,qBAAqB;AACrB,MAAM,eAAe;IACnB,SAAS;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA,eAAe;QACb,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,qBAAqB;IACvB;IACA,UAAU;QACR,kBAAkB;QAClB,gBAAgB;QAChB,qBAAqB;QACrB,cAAc;QACd,kBAAkB;IACpB;IACA,YAAY;QACV,OAAO;QACP,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,kBAAkB;IACpB;AACF;AAEA,MAAM,kBAAkB;IACtB,SAAS;QACP,aAAa;QACb,cAAc;QACd,cAAc;QACd,SAAS;QACT,SAAS;QACT,UAAU;QACV,iBAAiB;IACnB;IACA,YAAY;QACV,cAAc;QACd,aAAa;QACb,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QACnB,sBAAsB;IACxB;IACA,SAAS;QACP,cAAc;QACd,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB;QACjB,cAAc;IAChB;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAAe;QACnB;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,2MAAA,CAAA,UAAO;QAAC;QACvD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAClD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACjD;IAED,MAAM,2BAA2B,CAAC,KAAa;QAC7C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,eAAe;oBACb,GAAG,KAAK,aAAa;oBACrB,CAAC,IAAI,EAAE;gBACT;YACF,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC,KAAa;QAC3C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY;oBACV,GAAG,KAAK,UAAU;oBAClB,CAAC,IAAI,EAAE;gBACT;YACF,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;gBACL,OAAM;gBACN,UAAS;gBACT,uBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC;wCACjB,MAAM,OAAO,IAAI,IAAI;wCACrB,qBACE,6LAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,uCACA,wCACJ;;8DAEF,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;8DAAM,IAAI,KAAK;;;;;;;2CATX,IAAI,EAAE;;;;;oCAYjB;;;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,2BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,qIAAA,CAAA,cAAW;oEAAC,KAAK,SAAS,OAAO,CAAC,MAAM;oEAAE,KAAI;;;;;;8EAC/C,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;;wEACvB,SAAS,OAAO,CAAC,SAAS,CAAC,EAAE;wEAAE,SAAS,OAAO,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;sEAGhE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;;sFAC7B,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGrC,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAK,WAAU;;sFAC1C,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;8DAMzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,OAAO,SAAS,OAAO,CAAC,SAAS;;;;;;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,OAAO,SAAS,OAAO,CAAC,QAAQ;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,OAAO,SAAS,OAAO,CAAC,KAAK;oEAAE,MAAK;;;;;;;;;;;;sEAE7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,OAAO,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAO,OAAM;sFAAc;;;;;;sFAC5B,6LAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,6LAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,6LAAC;4EAAO,OAAM;sFAAQ;;;;;;sFACtB,6LAAC;4EAAO,OAAM;sFAAY;;;;;;;;;;;;;;;;;;sEAG9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,OAAO,SAAS,OAAO,CAAC,QAAQ;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAO,OAAM;sFAAmB;;;;;;sFACjC,6LAAC;4EAAO,OAAM;sFAAkB;;;;;;sFAChC,6LAAC;4EAAO,OAAM;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAO,OAAM;sFAAsB;;;;;;;;;;;;;;;;;;sEAGxC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,6LAAC;4EAAO,OAAM;sFAAU;;;;;;sFACxB,6LAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,6LAAC;4EAAO,OAAM;sFAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASpC,cAAc,iCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,kBAAkB;gEAClD,iBAAiB,CAAC,UAAY,yBAAyB,sBAAsB;;;;;;;;;;;;kEAIjF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,iBAAiB;gEACjD,iBAAiB,CAAC,UAAY,yBAAyB,qBAAqB;;;;;;;;;;;;kEAIhF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,iNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,gBAAgB;gEAChD,iBAAiB,CAAC,UAAY,yBAAyB,oBAAoB;;;;;;;;;;;;kEAI/E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,iNAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,aAAa;gEAC7C,iBAAiB,CAAC,UAAY,yBAAyB,iBAAiB;;;;;;;;;;;;kEAI5E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,YAAY;gEAC5C,iBAAiB,CAAC,UAAY,yBAAyB,gBAAgB;;;;;;;;;;;;kEAI3E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;;;;;;;kFAEhC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,aAAa,CAAC,cAAc;gEAC9C,iBAAiB,CAAC,UAAY,yBAAyB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAStF,cAAc,4BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;0FACf,6LAAC;gFAAK,WAAU;0FAAc;;;;;;0FAC9B,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAU;;;;;;;;;;;;kFAE/C,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS,SAAS,QAAQ,CAAC,gBAAgB;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFACJ,MAAM,eAAe,SAAS;gFAC9B,aAAY;;;;;;0FAEd,6LAAC,qIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,WAAU;gFACV,SAAS,IAAM,gBAAgB,CAAC;0FAE/B,6BAAe,6LAAC,6MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;yGAAe,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;;;;;;;kFAGpE,6LAAC,oIAAA,CAAA,QAAK;wEAAC,MAAK;wEAAW,aAAY;;;;;;kFACnC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,MAAK;wEAAW,aAAY;;;;;;kFACnC,6LAAC,qIAAA,CAAA,SAAM;wEAAC,MAAK;kFAAK;;;;;;;;;;;;;;;;;;kEAItB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAK;;;;;;kFACnB,6LAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,6LAAC;wEAAO,OAAM;kFAAM;;;;;;;;;;;;;;;;;;kEAIxB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;8EACb,IAAI,KAAK,SAAS,QAAQ,CAAC,mBAAmB,EAAE,kBAAkB;;;;;;;;;;;;;;;;;kEAKzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS,SAAS,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ1D,cAAc,8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,6LAAC;gEACC,OAAO,SAAS,UAAU,CAAC,KAAK;gEAChC,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC/D,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,6LAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAI3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,UAAU,CAAC,WAAW;gEACxC,iBAAiB,CAAC,UAAY,uBAAuB,eAAe;;;;;;;;;;;;kEAIxE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,UAAU,CAAC,WAAW;gEACxC,iBAAiB,CAAC,UAAY,uBAAuB,eAAe;;;;;;;;;;;;kEAIxE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;0EAE/C,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,SAAS,UAAU,CAAC,iBAAiB;gEAC9C,iBAAiB,CAAC,UAAY,uBAAuB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlG;GAhbwB;KAAA", "debugId": null}}]}