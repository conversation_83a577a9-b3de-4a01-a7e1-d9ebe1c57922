"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  MessageSquare,
  Bell,
  Pin,
  Calendar,
  Users,
  Eye,
  Heart,
  MessageCircle,
  Share2,
  Plus,
  Filter,
  Search,
  Edit,

  AlertCircle,
  CheckCircle,
  Info,
  Megaphone
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock announcements data
const announcements = [
  {
    id: 1,
    title: "Q1 All-Hands Meeting",
    content: "Join us for our quarterly all-hands meeting where we'll discuss company performance, upcoming initiatives, and celebrate team achievements. Light refreshments will be provided.",
    author: "<PERSON>",
    authorRole: "CEO",
    department: "Company-wide",
    priority: "high",
    type: "meeting",
    publishedDate: "2024-01-25",
    expiryDate: "2024-02-15",
    isPinned: true,
    views: 245,
    likes: 32,
    comments: 8,
    avatar: "/avatars/sarah.jpg",
    tags: ["meeting", "quarterly", "all-hands"]
  },
  {
    id: 2,
    title: "New Health Insurance Benefits",
    content: "We're excited to announce enhanced health insurance coverage starting February 1st. The new plan includes dental, vision, and mental health benefits with reduced deductibles.",
    author: "Mike Chen",
    authorRole: "HR Director",
    department: "Human Resources",
    priority: "medium",
    type: "policy",
    publishedDate: "2024-01-22",
    expiryDate: "2024-03-01",
    isPinned: true,
    views: 189,
    likes: 45,
    comments: 12,
    avatar: "/avatars/mike.jpg",
    tags: ["benefits", "health", "insurance"]
  },
  {
    id: 3,
    title: "Office Renovation Update",
    content: "The second floor renovation is progressing well and is expected to complete by March 15th. During this time, please use the main elevator and temporary meeting rooms on the first floor.",
    author: "Emily Davis",
    authorRole: "Facilities Manager",
    department: "Operations",
    priority: "low",
    type: "update",
    publishedDate: "2024-01-20",
    expiryDate: "2024-03-20",
    isPinned: false,
    views: 156,
    likes: 18,
    comments: 5,
    avatar: "/avatars/emily.jpg",
    tags: ["renovation", "facilities", "office"]
  },
  {
    id: 4,
    title: "Security System Maintenance",
    content: "Scheduled maintenance on our security systems will occur this Saturday from 6 AM to 10 AM. Building access may be limited during this time. Please plan accordingly.",
    author: "David Wilson",
    authorRole: "IT Security",
    department: "Information Technology",
    priority: "high",
    type: "maintenance",
    publishedDate: "2024-01-24",
    expiryDate: "2024-01-28",
    isPinned: false,
    views: 203,
    likes: 12,
    comments: 3,
    avatar: "/avatars/david.jpg",
    tags: ["security", "maintenance", "weekend"]
  }
]

const announcementStats = {
  totalAnnouncements: announcements.length,
  pinnedAnnouncements: announcements.filter(a => a.isPinned).length,
  totalViews: announcements.reduce((sum, a) => sum + a.views, 0),
  totalEngagement: announcements.reduce((sum, a) => sum + a.likes + a.comments, 0),
  avgEngagementRate: 15.2,
  activeAnnouncements: announcements.filter(a => new Date(a.expiryDate) > new Date()).length
}

export default function AnnouncementsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedPriority, setSelectedPriority] = useState("All")
  const [selectedType, setSelectedType] = useState("All")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")

  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         announcement.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         announcement.author.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesPriority = selectedPriority === "All" || announcement.priority === selectedPriority
    const matchesType = selectedType === "All" || announcement.type === selectedType
    const matchesDepartment = selectedDepartment === "All" || announcement.department === selectedDepartment
    
    return matchesSearch && matchesPriority && matchesType && matchesDepartment
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive"
      case "medium": return "warning"
      case "low": return "secondary"
      default: return "secondary"
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high": return <AlertCircle className="h-4 w-4" />
      case "medium": return <Info className="h-4 w-4" />
      case "low": return <CheckCircle className="h-4 w-4" />
      default: return <Info className="h-4 w-4" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "meeting": return <Calendar className="h-4 w-4" />
      case "policy": return <MessageSquare className="h-4 w-4" />
      case "update": return <Bell className="h-4 w-4" />
      case "maintenance": return <AlertCircle className="h-4 w-4" />
      default: return <Megaphone className="h-4 w-4" />
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Announcements"
        subtitle={`${announcementStats.totalAnnouncements} announcements with ${announcementStats.totalViews} total views`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New Announcement
            </Button>
          </div>
        }
      />

      {/* Announcement Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.totalAnnouncements}</p>
                <p className="text-sm text-muted-foreground">Total</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Pin className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.pinnedAnnouncements}</p>
                <p className="text-sm text-muted-foreground">Pinned</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.totalViews}</p>
                <p className="text-sm text-muted-foreground">Total Views</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Heart className="h-8 w-8 text-red-600 dark:text-red-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.totalEngagement}</p>
                <p className="text-sm text-muted-foreground">Engagement</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.activeAnnouncements}</p>
                <p className="text-sm text-muted-foreground">Active</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{announcementStats.avgEngagementRate}%</p>
                <p className="text-sm text-muted-foreground">Avg Engagement</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            List View
          </Button>
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            Grid View
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search announcements by title, content, or author..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Priorities</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Types</option>
                <option value="meeting">Meeting</option>
                <option value="policy">Policy</option>
                <option value="update">Update</option>
                <option value="maintenance">Maintenance</option>
              </select>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Departments</option>
                <option value="Company-wide">Company-wide</option>
                <option value="Human Resources">Human Resources</option>
                <option value="Operations">Operations</option>
                <option value="Information Technology">Information Technology</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Announcements List/Grid */}
      {viewMode === "list" ? (
        <div className="space-y-4">
          {filteredAnnouncements.map((announcement, index) => (
            <motion.div
              key={announcement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={announcement.isPinned ? "border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20" : ""}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={announcement.avatar} alt={announcement.author} />
                        <AvatarFallback>
                          {announcement.author.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <CardTitle className="text-lg">{announcement.title}</CardTitle>
                          {announcement.isPinned && <Pin className="h-4 w-4 text-yellow-600" />}
                        </div>
                        <CardDescription className="flex items-center space-x-2 mt-1">
                          <span>{announcement.author} • {announcement.authorRole}</span>
                          <span>•</span>
                          <span>{announcement.department}</span>
                          <span>•</span>
                          <span>{new Date(announcement.publishedDate).toLocaleDateString()}</span>
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={getPriorityColor(announcement.priority) as "default" | "secondary" | "destructive" | "outline"} className="flex items-center space-x-1">
                        {getPriorityIcon(announcement.priority)}
                        <span className="ml-1">{announcement.priority}</span>
                      </Badge>
                      <Badge variant="outline" className="flex items-center space-x-1">
                        {getTypeIcon(announcement.type)}
                        <span className="ml-1">{announcement.type}</span>
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{announcement.content}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{announcement.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4" />
                        <span>{announcement.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="h-4 w-4" />
                        <span>{announcement.comments}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Heart className="h-4 w-4 mr-1" />
                        Like
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageCircle className="h-4 w-4 mr-1" />
                        Comment
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAnnouncements.map((announcement, index) => (
            <motion.div
              key={announcement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`h-full ${announcement.isPinned ? "border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20" : ""}`}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <CardTitle className="text-base">{announcement.title}</CardTitle>
                      {announcement.isPinned && <Pin className="h-4 w-4 text-yellow-600" />}
                    </div>
                    <Badge variant={getPriorityColor(announcement.priority) as "default" | "secondary" | "destructive" | "outline"} className="flex items-center space-x-1">
                      {getPriorityIcon(announcement.priority)}
                      <span className="ml-1 text-xs">{announcement.priority}</span>
                    </Badge>
                  </div>
                  <CardDescription className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={announcement.avatar} alt={announcement.author} />
                      <AvatarFallback className="text-xs">
                        {announcement.author.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm">{announcement.author}</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3">{announcement.content}</p>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <span>{announcement.views} views</span>
                      <span>•</span>
                      <span>{announcement.likes} likes</span>
                    </div>
                    <span>{new Date(announcement.publishedDate).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}
