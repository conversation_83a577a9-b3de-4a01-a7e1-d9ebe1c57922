{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string | number): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    month: \"long\",\n    day: \"numeric\",\n    year: \"numeric\",\n  }).format(new Date(date))\n}\n\nexport function formatCurrency(\n  amount: number,\n  currency: string = \"USD\"\n): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"currency\",\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"percent\",\n    minimumFractionDigits: 1,\n    maximumFractionDigits: 1,\n  }).format(value / 100)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(\" \")\n    .map((n) => n[0])\n    .join(\"\")\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + \"...\"\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, \"\")\n    .replace(/ +/g, \"-\")\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAA4B;IACrD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        success: \"bg-green-600 text-white hover:bg-green-700\",\n        warning: \"bg-yellow-600 text-white hover:bg-yellow-700\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant, \n    size, \n    asChild = false, \n    loading = false,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    ...props \n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    \n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {!loading && leftIcon && (\n          <span className=\"mr-2\">{leftIcon}</span>\n        )}\n        {children}\n        {!loading && rightIcon && (\n          <span className=\"ml-2\">{rightIcon}</span>\n        )}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAYF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EACC,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP,CAAC,WAAW,0BACX,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAEzB;YACA,CAAC,WAAW,2BACX,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAIhC;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst avatarVariants = cva(\n  \"relative flex shrink-0 overflow-hidden rounded-full\",\n  {\n    variants: {\n      size: {\n        sm: \"h-8 w-8\",\n        default: \"h-10 w-10\",\n        lg: \"h-12 w-12\",\n        xl: \"h-16 w-16\",\n        \"2xl\": \"h-20 w-20\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n    },\n  }\n)\n\nexport interface AvatarProps\n  extends React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>,\n    VariantProps<typeof avatarVariants> {\n  src?: string\n  alt?: string\n  fallback?: string\n  status?: \"online\" | \"offline\" | \"away\" | \"busy\"\n}\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  AvatarProps\n>(({ className, size, src, alt, fallback, status, ...props }, ref) => (\n  <div className=\"relative\">\n    <AvatarPrimitive.Root\n      ref={ref}\n      className={cn(avatarVariants({ size }), className)}\n      {...props}\n    >\n      <AvatarImage src={src} alt={alt} />\n      <AvatarFallback>{fallback}</AvatarFallback>\n    </AvatarPrimitive.Root>\n    {status && (\n      <div\n        className={cn(\n          \"absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white\",\n          {\n            \"bg-green-500\": status === \"online\",\n            \"bg-muted-foreground\": status === \"offline\",\n            \"bg-yellow-500\": status === \"away\",\n            \"bg-red-500\": status === \"busy\",\n          }\n        )}\n      />\n    )}\n  </div>\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback, avatarVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uDACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAYF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kKAAA,CAAA,OAAoB;gBACnB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;oBAAE;gBAAK,IAAI;gBACvC,GAAG,KAAK;;kCAET,8OAAC;wBAAY,KAAK;wBAAK,KAAK;;;;;;kCAC5B,8OAAC;kCAAgB;;;;;;;;;;;;YAElB,wBACC,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;oBACE,gBAAgB,WAAW;oBAC3B,uBAAuB,WAAW;oBAClC,iBAAiB,WAAW;oBAC5B,cAAc,WAAW;gBAC3B;;;;;;;;;;;;AAMV,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400\",\n        warning:\n          \"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400\",\n        info:\n          \"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400\",\n        // HRMS specific variants\n        active:\n          \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n        inactive:\n          \"border-transparent bg-muted text-muted-foreground\",\n        pending:\n          \"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400\",\n        approved:\n          \"border-transparent bg-green-500/10 text-green-600 dark:text-green-400\",\n        rejected:\n          \"border-transparent bg-red-500/10 text-red-600 dark:text-red-400\",\n        draft:\n          \"border-transparent bg-muted text-muted-foreground\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {\n  icon?: React.ReactNode\n  removable?: boolean\n  onRemove?: () => void\n}\n\nfunction Badge({ \n  className, \n  variant, \n  size, \n  icon, \n  removable, \n  onRemove, \n  children,\n  ...props \n}: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props}>\n      {icon && <span className=\"mr-1\">{icon}</span>}\n      {children}\n      {removable && (\n        <button\n          type=\"button\"\n          className=\"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10\"\n          onClick={onRemove}\n        >\n          <svg\n            className=\"h-3 w-3\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M6 18L18 6M6 6l12 12\"\n            />\n          </svg>\n        </button>\n      )}\n    </div>\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,yBAAyB;YACzB,QACE;YACF,UACE;YACF,SACE;YACF,UACE;YACF,UACE;YACF,OACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACQ;IACX,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;YACvE,sBAAQ,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChC;YACA,2BACC,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;0BAET,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { usePathname } from \"next/navigation\"\nimport Link from \"next/link\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport {\n  Building2,\n  LayoutDashboard,\n  Users,\n  DollarSign,\n  TrendingUp,\n  Calendar,\n  FileText,\n  Settings,\n  HelpCircle,\n  ChevronLeft,\n  ChevronRight,\n  Bell,\n  Search,\n  LogOut,\n  User,\n  Shield,\n  BarChart3,\n  Clock,\n  Award,\n  MessageSquare\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\n\ninterface SidebarProps {\n  className?: string\n}\n\nconst navigationItems = [\n  {\n    title: \"Overview\",\n    items: [\n      { name: \"Dashboard\", icon: LayoutDashboard, href: \"/dashboard\", badge: null },\n      { name: \"Analytics\", icon: BarChart3, href: \"/dashboard/analytics\", badge: null },\n    ]\n  },\n  {\n    title: \"People Management\",\n    items: [\n      { name: \"Employees\", icon: Users, href: \"/dashboard/employees\", badge: \"124\" },\n      { name: \"Recruitment\", icon: User, href: \"/dashboard/recruitment\", badge: \"12\" },\n      { name: \"Onboarding\", icon: Award, href: \"/dashboard/onboarding\", badge: \"3\" },\n    ]\n  },\n  {\n    title: \"Operations\",\n    items: [\n      { name: \"Payroll\", icon: DollarSign, href: \"/dashboard/payroll\", badge: null },\n      { name: \"Performance\", icon: TrendingUp, href: \"/dashboard/performance\", badge: \"5\" },\n      { name: \"Time & Attendance\", icon: Clock, href: \"/dashboard/attendance\", badge: \"2\" },\n      { name: \"Leave Management\", icon: Calendar, href: \"/dashboard/leave\", badge: \"8\" },\n    ]\n  },\n  {\n    title: \"Communication\",\n    items: [\n      { name: \"Announcements\", icon: MessageSquare, href: \"/dashboard/announcements\", badge: \"2\" },\n      { name: \"Documents\", icon: FileText, href: \"/dashboard/documents\", badge: null },\n    ]\n  },\n  {\n    title: \"System\",\n    items: [\n      { name: \"Settings\", icon: Settings, href: \"/dashboard/settings\", badge: null },\n      { name: \"Compliance\", icon: Shield, href: \"/dashboard/compliance\", badge: null },\n      { name: \"Help & Support\", icon: HelpCircle, href: \"/dashboard/help\", badge: null },\n    ]\n  }\n]\n\nexport function Sidebar({ className }: SidebarProps) {\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const pathname = usePathname()\n\n  return (\n    <motion.div\n      initial={false}\n      animate={{ width: isCollapsed ? 80 : 280 }}\n      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n      className={cn(\n        \"relative flex flex-col bg-white border-r border-gray-200 shadow-sm\",\n        className\n      )}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <AnimatePresence mode=\"wait\">\n          {!isCollapsed && (\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.2 }}\n              className=\"flex items-center space-x-3\"\n            >\n              <div className=\"flex items-center justify-center w-8 h-8 bg-primary rounded-lg\">\n                <Building2 className=\"w-5 h-5 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-semibold text-foreground\">PeopleNest</h1>\n                <p className=\"text-xs text-muted-foreground\">HRMS Platform</p>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n        \n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 text-muted-foreground hover:text-foreground\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 overflow-y-auto py-4\">\n        <nav className=\"space-y-6 px-3\">\n          {navigationItems.map((section, sectionIndex) => (\n            <div key={section.title}>\n              <AnimatePresence>\n                {!isCollapsed && (\n                  <motion.h3\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3\"\n                  >\n                    {section.title}\n                  </motion.h3>\n                )}\n              </AnimatePresence>\n              \n              <ul className=\"space-y-1\">\n                {section.items.map((item) => {\n                  const isActive = pathname === item.href\n                  return (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={cn(\n                          \"w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                          isActive\n                            ? \"bg-primary text-primary-foreground shadow-sm\"\n                            : \"text-foreground hover:bg-muted hover:text-foreground\"\n                        )}\n                      >\n                        <item.icon className={cn(\n                          \"flex-shrink-0 w-5 h-5\",\n                          isCollapsed ? \"mx-auto\" : \"mr-3\"\n                        )} />\n\n                        <AnimatePresence>\n                          {!isCollapsed && (\n                            <motion.div\n                              initial={{ opacity: 0, width: 0 }}\n                              animate={{ opacity: 1, width: \"auto\" }}\n                              exit={{ opacity: 0, width: 0 }}\n                              className=\"flex items-center justify-between flex-1 min-w-0\"\n                            >\n                              <span className=\"truncate\">{item.name}</span>\n                              {item.badge && (\n                                <Badge\n                                  variant={isActive ? \"secondary\" : \"outline\"}\n                                  className=\"ml-2 text-xs\"\n                                >\n                                  {item.badge}\n                                </Badge>\n                              )}\n                            </motion.div>\n                          )}\n                        </AnimatePresence>\n                      </Link>\n                    </li>\n                  )\n                })}\n              </ul>\n            </div>\n          ))}\n        </nav>\n      </div>\n\n      {/* User Profile */}\n      <div className=\"border-t border-gray-200 p-4\">\n        <div className={cn(\n          \"flex items-center\",\n          isCollapsed ? \"justify-center\" : \"space-x-3\"\n        )}>\n          <Avatar className=\"h-8 w-8\">\n            <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n            <AvatarFallback>JD</AvatarFallback>\n          </Avatar>\n          \n          <AnimatePresence>\n            {!isCollapsed && (\n              <motion.div\n                initial={{ opacity: 0, width: 0 }}\n                animate={{ opacity: 1, width: \"auto\" }}\n                exit={{ opacity: 0, width: 0 }}\n                className=\"flex-1 min-w-0\"\n              >\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  John Doe\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  HR Manager\n                </p>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          \n          <AnimatePresence>\n            {!isCollapsed && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n              >\n                <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                  <LogOut className=\"h-4 w-4\" />\n                </Button>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </div>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;AAqCA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM,4NAAA,CAAA,kBAAe;gBAAE,MAAM;gBAAc,OAAO;YAAK;YAC5E;gBAAE,MAAM;gBAAa,MAAM,kNAAA,CAAA,YAAS;gBAAE,MAAM;gBAAwB,OAAO;YAAK;SACjF;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM,oMAAA,CAAA,QAAK;gBAAE,MAAM;gBAAwB,OAAO;YAAM;YAC7E;gBAAE,MAAM;gBAAe,MAAM,kMAAA,CAAA,OAAI;gBAAE,MAAM;gBAA0B,OAAO;YAAK;YAC/E;gBAAE,MAAM;gBAAc,MAAM,oMAAA,CAAA,QAAK;gBAAE,MAAM;gBAAyB,OAAO;YAAI;SAC9E;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAW,MAAM,kNAAA,CAAA,aAAU;gBAAE,MAAM;gBAAsB,OAAO;YAAK;YAC7E;gBAAE,MAAM;gBAAe,MAAM,kNAAA,CAAA,aAAU;gBAAE,MAAM;gBAA0B,OAAO;YAAI;YACpF;gBAAE,MAAM;gBAAqB,MAAM,oMAAA,CAAA,QAAK;gBAAE,MAAM;gBAAyB,OAAO;YAAI;YACpF;gBAAE,MAAM;gBAAoB,MAAM,0MAAA,CAAA,WAAQ;gBAAE,MAAM;gBAAoB,OAAO;YAAI;SAClF;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM,wNAAA,CAAA,gBAAa;gBAAE,MAAM;gBAA4B,OAAO;YAAI;YAC3F;gBAAE,MAAM;gBAAa,MAAM,8MAAA,CAAA,WAAQ;gBAAE,MAAM;gBAAwB,OAAO;YAAK;SAChF;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM,0MAAA,CAAA,WAAQ;gBAAE,MAAM;gBAAuB,OAAO;YAAK;YAC7E;gBAAE,MAAM;gBAAc,MAAM,sMAAA,CAAA,SAAM;gBAAE,MAAM;gBAAyB,OAAO;YAAK;YAC/E;gBAAE,MAAM;gBAAkB,MAAM,8NAAA,CAAA,aAAU;gBAAE,MAAM;gBAAmB,OAAO;YAAK;SAClF;IACH;CACD;AAEM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;QACT,SAAS;YAAE,OAAO,cAAc,KAAK;QAAI;QACzC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;QAC/C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAMrD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,6BAC7B,8OAAC;;8CACC,8OAAC,yLAAA,CAAA,kBAAe;8CACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,WAAU;kDAET,QAAQ,KAAK;;;;;;;;;;;8CAKpB,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;wCAClB,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,WACI,iDACA;;kEAGN,8OAAC,KAAK,IAAI;wDAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,yBACA,cAAc,YAAY;;;;;;kEAG5B,8OAAC,yLAAA,CAAA,kBAAe;kEACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,OAAO;4DAAE;4DAChC,SAAS;gEAAE,SAAS;gEAAG,OAAO;4DAAO;4DACrC,MAAM;gEAAE,SAAS;gEAAG,OAAO;4DAAE;4DAC7B,WAAU;;8EAEV,8OAAC;oEAAK,WAAU;8EAAY,KAAK,IAAI;;;;;;gEACpC,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;oEACJ,SAAS,WAAW,cAAc;oEAClC,WAAU;8EAET,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;2CA7BhB,KAAK,IAAI;;;;;oCAsCtB;;;;;;;2BAxDM,QAAQ,KAAK;;;;;;;;;;;;;;;0BAgE7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,qBACA,cAAc,mBAAmB;;sCAEjC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,KAAI;oCAAoB,KAAI;;;;;;8CACzC,8OAAC,kIAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;sCAGlB,8OAAC,yLAAA,CAAA,kBAAe;sCACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAO;gCACrC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAC7B,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAG5D,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;sCAO5D,8OAAC,yLAAA,CAAA,kBAAe;sCACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;0CAEnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/layout/mobile-nav.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport {\n  LayoutDashboard,\n  Users,\n  DollarSign,\n  TrendingUp,\n  Calendar,\n  Settings,\n  Menu,\n  X,\n  Home,\n  Search,\n  Bell,\n  User\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\n\nconst mobileNavItems = [\n  { name: \"Dashboard\", icon: LayoutDashboard, href: \"/dashboard\", badge: null },\n  { name: \"Employees\", icon: Users, href: \"/dashboard/employees\", badge: \"124\" },\n  { name: \"Payroll\", icon: DollarSign, href: \"/dashboard/payroll\", badge: null },\n  { name: \"Performance\", icon: TrendingUp, href: \"/dashboard/performance\", badge: \"5\" },\n  { name: \"Leave\", icon: Calendar, href: \"/dashboard/leave\", badge: \"8\" },\n  { name: \"Settings\", icon: Settings, href: \"/dashboard/settings\", badge: null },\n]\n\nconst quickActions = [\n  { name: \"Search\", icon: Search, action: \"search\" },\n  { name: \"Notifications\", icon: Bell, action: \"notifications\", badge: \"3\" },\n  { name: \"Profile\", icon: User, action: \"profile\" },\n]\n\ninterface MobileNavProps {\n  className?: string\n}\n\nexport function MobileNav({ className }: MobileNavProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [activeItem, setActiveItem] = useState(\"/dashboard\")\n  const pathname = usePathname()\n\n  useEffect(() => {\n    setActiveItem(pathname)\n  }, [pathname])\n\n  useEffect(() => {\n    // Close mobile nav when route changes\n    setIsOpen(false)\n  }, [pathname])\n\n  // Prevent body scroll when menu is open\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n    \n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen])\n\n  const handleQuickAction = (action: string) => {\n    switch (action) {\n      case 'search':\n        // Implement search functionality\n        console.log('Search triggered')\n        break\n      case 'notifications':\n        // Implement notifications\n        console.log('Notifications triggered')\n        break\n      case 'profile':\n        // Implement profile\n        console.log('Profile triggered')\n        break\n    }\n    setIsOpen(false)\n  }\n\n  return (\n    <>\n      {/* Mobile Header */}\n      <div className=\"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsOpen(true)}\n              className=\"h-10 w-10\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </Button>\n            <div>\n              <h1 className=\"text-lg font-semibold text-foreground\">PeopleNest</h1>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            {quickActions.map((action) => (\n              <Button\n                key={action.name}\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => handleQuickAction(action.action)}\n                className=\"h-10 w-10 relative\"\n              >\n                <action.icon className=\"h-5 w-5\" />\n                {action.badge && (\n                  <Badge \n                    variant=\"destructive\" \n                    className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                  >\n                    {action.badge}\n                  </Badge>\n                )}\n              </Button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation Overlay */}\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              onClick={() => setIsOpen(false)}\n              className=\"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm\"\n            />\n            \n            {/* Navigation Panel */}\n            <motion.div\n              initial={{ x: \"-100%\" }}\n              animate={{ x: 0 }}\n              exit={{ x: \"-100%\" }}\n              transition={{ type: \"spring\", damping: 30, stiffness: 300 }}\n              className=\"lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-white shadow-xl\"\n            >\n              <div className=\"flex flex-col h-full\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between p-6 border-b border-border\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-primary rounded-lg flex items-center justify-center\">\n                      <Home className=\"h-6 w-6 text-primary-foreground\" />\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-semibold text-foreground\">PeopleNest</h2>\n                      <p className=\"text-sm text-muted-foreground\">HRMS Dashboard</p>\n                    </div>\n                  </div>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={() => setIsOpen(false)}\n                    className=\"h-10 w-10\"\n                  >\n                    <X className=\"h-6 w-6\" />\n                  </Button>\n                </div>\n\n                {/* Navigation Items */}\n                <div className=\"flex-1 overflow-y-auto py-6\">\n                  <nav className=\"px-6 space-y-2\">\n                    {mobileNavItems.map((item, index) => {\n                      const isActive = activeItem === item.href\n                      return (\n                        <motion.div\n                          key={item.name}\n                          initial={{ opacity: 0, x: -20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ delay: index * 0.1 }}\n                        >\n                          <Link\n                            href={item.href}\n                            onClick={() => setActiveItem(item.href)}\n                            className={`\n                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200\n                              ${isActive\n                                ? 'bg-primary/10 text-primary border border-primary/20'\n                                : 'text-muted-foreground hover:bg-muted hover:text-foreground'\n                              }\n                            `}\n                          >\n                            <item.icon className={`h-5 w-5 ${isActive ? 'text-primary' : 'text-muted-foreground'}`} />\n                            <span className=\"font-medium\">{item.name}</span>\n                            {item.badge && (\n                              <Badge \n                                variant={isActive ? \"default\" : \"secondary\"} \n                                className=\"ml-auto\"\n                              >\n                                {item.badge}\n                              </Badge>\n                            )}\n                          </Link>\n                        </motion.div>\n                      )\n                    })}\n                  </nav>\n                </div>\n\n                {/* Footer */}\n                <div className=\"p-6 border-t border-border\">\n                  <div className=\"flex items-center space-x-3 p-3 bg-muted rounded-lg\">\n                    <div className=\"w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center\">\n                      <User className=\"h-5 w-5 text-muted-foreground\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium text-foreground\">John Doe</p>\n                      <p className=\"text-xs text-muted-foreground\">HR Manager</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Bottom Navigation for Mobile */}\n      <div className=\"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2\">\n        <div className=\"flex items-center justify-around\">\n          {mobileNavItems.slice(0, 4).map((item) => {\n            const isActive = activeItem === item.href\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                onClick={() => setActiveItem(item.href)}\n                className={`\n                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative\n                  ${isActive\n                    ? 'text-primary'\n                    : 'text-muted-foreground hover:text-foreground'\n                  }\n                `}\n              >\n                <item.icon className=\"h-5 w-5\" />\n                <span className=\"text-xs font-medium\">{item.name}</span>\n                {item.badge && (\n                  <Badge \n                    variant=\"destructive\" \n                    className=\"absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs\"\n                  >\n                    {item.badge}\n                  </Badge>\n                )}\n                {isActive && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full\"\n                  />\n                )}\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </>\n  )\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AArBA;;;;;;;;;AAuBA,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAa,MAAM,4NAAA,CAAA,kBAAe;QAAE,MAAM;QAAc,OAAO;IAAK;IAC5E;QAAE,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAwB,OAAO;IAAM;IAC7E;QAAE,MAAM;QAAW,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAsB,OAAO;IAAK;IAC7E;QAAE,MAAM;QAAe,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAA0B,OAAO;IAAI;IACpF;QAAE,MAAM;QAAS,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAoB,OAAO;IAAI;IACtE;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAuB,OAAO;IAAK;CAC9E;AAED,MAAM,eAAe;IACnB;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,QAAQ;IAAS;IACjD;QAAE,MAAM;QAAiB,MAAM,kMAAA,CAAA,OAAI;QAAE,QAAQ;QAAiB,OAAO;IAAI;IACzE;QAAE,MAAM;QAAW,MAAM,kMAAA,CAAA,OAAI;QAAE,QAAQ;IAAU;CAClD;AAMM,SAAS,UAAU,EAAE,SAAS,EAAkB;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,UAAU;IACZ,GAAG;QAAC;KAAS;IAEb,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,iCAAiC;gBACjC,QAAQ,GAAG,CAAC;gBACZ;YACF,KAAK;gBACH,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC;gBACZ;YACF,KAAK;gBACH,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBACZ;QACJ;QACA,UAAU;IACZ;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;8CACC,cAAA,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;;;;;;;sCAI1D,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,kBAAkB,OAAO,MAAM;oCAC9C,WAAU;;sDAEV,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;wCACtB,OAAO,KAAK,kBACX,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDAET,OAAO,KAAK;;;;;;;mCAZZ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;0BAsB1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS,IAAM,UAAU;4BACzB,WAAU;;;;;;sCAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAQ;4BACtB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAQ;4BACnB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,UAAU;gDACzB,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,MAAM;gDACzB,MAAM,WAAW,eAAe,KAAK,IAAI;gDACzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO,QAAQ;oDAAI;8DAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,cAAc,KAAK,IAAI;wDACtC,WAAW,CAAC;;8BAEV,EAAE,WACE,wDACA,6DACH;4BACH,CAAC;;0EAED,8OAAC,KAAK,IAAI;gEAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,yBAAyB;;;;;;0EACtF,8OAAC;gEAAK,WAAU;0EAAe,KAAK,IAAI;;;;;;4DACvC,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,WAAW,YAAY;gEAChC,WAAU;0EAET,KAAK,KAAK;;;;;;;;;;;;mDAvBZ,KAAK,IAAI;;;;;4CA6BpB;;;;;;;;;;;kDAKJ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBAC/B,MAAM,WAAW,eAAe,KAAK,IAAI;wBACzC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,SAAS,IAAM,cAAc,KAAK,IAAI;4BACtC,WAAW,CAAC;;kBAEV,EAAE,WACE,iBACA,8CACH;gBACH,CAAC;;8CAED,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAuB,KAAK,IAAI;;;;;;gCAC/C,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CAET,KAAK,KAAK;;;;;;gCAGd,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;;;;;;;2BAxBT,KAAK,IAAI;;;;;oBA6BpB;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Sidebar } from \"@/components/layout/sidebar\"\nimport { MobileNav } from \"@/components/layout/mobile-nav\"\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Desktop Sidebar */}\n      <Sidebar className=\"hidden lg:flex\" />\n\n      {/* Mobile Navigation */}\n      <MobileNav />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <main className=\"flex-1 overflow-y-auto pt-16 lg:pt-0 pb-16 lg:pb-0\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAGnB,8OAAC,6IAAA,CAAA,YAAS;;;;;0BAEV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}