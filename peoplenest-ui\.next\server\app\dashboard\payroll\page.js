(()=>{var e={};e.id=827,e.ids=[827],e.modules={3223:(e,s,t)=>{Promise.resolve().then(t.bind(t,71064))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19913:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\payroll\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx","default")},27900:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},66271:(e,s,t)=>{Promise.resolve().then(t.bind(t,19913))},71064:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(60687),r=t(43210),l=t(50371),n=t(5336),o=t(48730),i=t(93613),d=t(16023),c=t(31158);let p=(0,t(62688).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var x=t(23928),m=t(99270),u=t(13861),h=t(63143),y=t(27900),g=t(29523),j=t(89667),f=t(44493),v=t(96834),N=t(74456),b=t(32584);let w=[{id:1,employeeId:1,employeeName:"Sarah Johnson",employeeEmail:"<EMAIL>",department:"Engineering",position:"Senior Software Engineer",baseSalary:125e3,overtime:2500,bonuses:5e3,deductions:1200,netPay:131300,payPeriod:"2024-01-01 to 2024-01-31",status:"processed",processedDate:"2024-02-01",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"Mike Chen",employeeEmail:"<EMAIL>",department:"Product",position:"Product Manager",baseSalary:115e3,overtime:1800,bonuses:3e3,deductions:1100,netPay:118700,payPeriod:"2024-01-01 to 2024-01-31",status:"pending",processedDate:null,avatar:"/avatars/mike.jpg"},{id:3,employeeId:3,employeeName:"Emily Davis",employeeEmail:"<EMAIL>",department:"Design",position:"UX Designer",baseSalary:95e3,overtime:1200,bonuses:2e3,deductions:900,netPay:97300,payPeriod:"2024-01-01 to 2024-01-31",status:"processed",processedDate:"2024-02-01",avatar:"/avatars/emily.jpg"},{id:4,employeeId:4,employeeName:"David Wilson",employeeEmail:"<EMAIL>",department:"Engineering",position:"DevOps Engineer",baseSalary:11e4,overtime:2200,bonuses:1500,deductions:1e3,netPay:112700,payPeriod:"2024-01-01 to 2024-01-31",status:"review",processedDate:null,avatar:"/avatars/david.jpg"}],P={totalEmployees:w.length,totalGrossPay:w.reduce((e,s)=>e+s.baseSalary+s.overtime+s.bonuses,0),totalDeductions:w.reduce((e,s)=>e+s.deductions,0),totalNetPay:w.reduce((e,s)=>e+s.netPay,0),processed:w.filter(e=>"processed"===e.status).length,pending:w.filter(e=>"pending"===e.status).length,review:w.filter(e=>"review"===e.status).length};function k(){let[e,s]=(0,r.useState)(""),[t,k]=(0,r.useState)("All"),[A,S]=(0,r.useState)("All"),[C,E]=(0,r.useState)("2024-01-01 to 2024-01-31"),M=w.filter(s=>{let a=s.employeeName.toLowerCase().includes(e.toLowerCase())||s.employeeEmail.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),r="All"===t||s.status===t,l="All"===A||s.department===A;return a&&r&&l}),D=e=>{switch(e){case"processed":return"success";case"pending":return"warning";case"review":return"destructive";default:return"secondary"}},_=e=>{switch(e){case"processed":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"pending":default:return(0,a.jsx)(o.A,{className:"h-4 w-4"});case"review":return(0,a.jsx)(i.A,{className:"h-4 w-4"})}};return(0,a.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,a.jsx)(N.Y,{title:"Payroll Management",subtitle:`Processing payroll for ${P.totalEmployees} employees`,actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Import"]}),(0,a.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,a.jsxs)(g.$,{size:"sm",children:[(0,a.jsx)(p,{className:"w-4 h-4 mr-2"}),"Process Payroll"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:["$",P.totalNetPay.toLocaleString()]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Net Pay"})]})]})})}),(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:P.processed}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Processed"})]})]})})}),(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:P.pending}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:P.review}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Needs Review"})]})]})})})]}),(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(j.p,{placeholder:"Search by employee name, email, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>k(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by status",children:[(0,a.jsx)("option",{value:"All",children:"All Status"}),(0,a.jsx)("option",{value:"processed",children:"Processed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"review",children:"Needs Review"})]}),(0,a.jsxs)("select",{value:A,onChange:e=>S(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by department",children:[(0,a.jsx)("option",{value:"All",children:"All Departments"}),(0,a.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,a.jsx)("option",{value:"Product",children:"Product"}),(0,a.jsx)("option",{value:"Design",children:"Design"}),(0,a.jsx)("option",{value:"Sales",children:"Sales"}),(0,a.jsx)("option",{value:"Marketing",children:"Marketing"})]})]})]})})}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",M.length," of ",w.length," payroll records"]})}),(0,a.jsx)(f.Zp,{children:(0,a.jsx)(f.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Department"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Base Salary"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Overtime"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Bonuses"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Deductions"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Net Pay"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:M.map((e,s)=>(0,a.jsxs)(l.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(b.eu,{className:"h-10 w-10",children:[(0,a.jsx)(b.BK,{src:e.avatar,alt:e.employeeName}),(0,a.jsx)(b.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.position})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground font-medium",children:["$",e.baseSalary.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground",children:["$",e.overtime.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-green-600 font-medium",children:["$",e.bonuses.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-red-600",children:["-$",e.deductions.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground font-bold",children:["$",e.netPay.toLocaleString()]}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)(v.E,{variant:D(e.status),className:"flex items-center space-x-1",children:[_(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})})]})}},79551:e=>{"use strict";e.exports=require("url")},84178:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d={children:["",{children:["dashboard",{children:["payroll",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,19913)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/payroll/page",pathname:"/dashboard/payroll",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,912,486,722,658,557,188],()=>t(84178));module.exports=a})();