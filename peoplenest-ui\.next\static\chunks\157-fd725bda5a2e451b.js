"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[157],{33109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},55868:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},61667:(e,t,r)=>{r.d(t,{Gk:()=>ei,Vf:()=>ea});var n=r(12115),a=r(52596),i=r(70688),l=r(51172),o=r(2348),c=r(36079),s=r(41643),u=r(16377),p=r(39827),d=r(70788),h=r(93262),y=r(56091),f=r(39226),m=r(37195),v=r(68924),g=r(14299),b=r(97238),x=r(60356),E=(e,t,r,n)=>(0,g.Gx)(e,"xAxis",t,n),O=(e,t,r,n)=>(0,g.CR)(e,"xAxis",t,n),P=(e,t,r,n)=>(0,g.Gx)(e,"yAxis",r,n),A=(e,t,r,n)=>(0,g.CR)(e,"yAxis",r,n),k=(0,v.Mz)([b.fz,E,P,O,A],(e,t,r,n,a)=>(0,p._L)(e,"xAxis")?(0,p.Hj)(t,n,!1):(0,p.Hj)(r,a,!1)),w=(0,v.Mz)([g.ld,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&(0,p.$8)(t.stackId)===e.stackId&&t.data===e.data))return t}),j=(0,v.Mz)([b.fz,E,P,O,A,(e,t,r,n,a)=>{var i,l,o=(0,b.fz)(e);if(null!=(l=(0,p._L)(o,"xAxis")?(0,g.TC)(e,"yAxis",r,n):(0,g.TC)(e,"xAxis",t,n))){var{dataKey:c,stackId:s}=a;if(null!=s){var u=null==(i=l[s])?void 0:i.stackedData;return null==u?void 0:u.find(e=>e.key===c)}}},x.HS,k,w],(e,t,r,n,a,i,l,o,c)=>{var s,{chartData:u,dataStartIndex:p,dataEndIndex:d}=l;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=a&&0!==n.length&&0!==a.length&&null!=o){var{data:h}=c;if(null!=(s=h&&h.length>0?h:null==u?void 0:u.slice(p,d+1)))return ea({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,dataStartIndex:p,areaSettings:c,stackedData:i,displayedData:s,chartBaseValue:void 0,bandSize:o})}}),I=r(71807),M=r(94732),N=r(79020),C=r(81971),D=r(39426),S=r(93389),W=r(78892),z=r(74460),L=["layout","type","stroke","connectNulls","isRange"],H=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function T(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){G(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function B(e,t){return e&&"none"!==e?e:t}var _=e=>{var{dataKey:t,name:r,stroke:n,fill:a,legendType:i,hide:l}=e;return[{inactive:l,dataKey:t,type:i,color:B(n,a),value:(0,p.uM)(r,t),payload:e}]};function J(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,fill:i,name:l,hide:o,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,nameKey:void 0,name:(0,p.uM)(l,t),hide:o,type:e.tooltipType,color:B(n,i),unit:c}}}var $=(e,t)=>{var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var i=(0,a.$)("recharts-area-dot","boolean"!=typeof e?e.className:"");r=n.createElement(l.c,V({},t,{className:i}))}return r};function q(e){var{clipPathId:t,points:r,props:a}=e,{needClip:i,dot:l,dataKey:c}=a;if(null==r||!l&&1!==r.length)return null;var s=(0,d.y$)(l),u=(0,d.J9)(a,!1),p=(0,d.J9)(l,!0),h=r.map((e,t)=>$(l,R(R(R({key:"dot-".concat(t),r:3},u),p),{},{index:t,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:r}))),y={clipPath:i?"url(#clipPath-".concat(s?"":"dots-").concat(t,")"):void 0};return n.createElement(o.W,V({className:"recharts-area-dots"},y),h)}function F(e){var{points:t,baseLine:r,needClip:a,clipPathId:l,props:s,showLabels:u}=e,{layout:p,type:h,stroke:y,connectNulls:f,isRange:m}=s,v=T(s,L);return n.createElement(n.Fragment,null,(null==t?void 0:t.length)>1&&n.createElement(o.W,{clipPath:a?"url(#clipPath-".concat(l,")"):void 0},n.createElement(i.I,V({},(0,d.J9)(v,!0),{points:t,connectNulls:f,type:h,baseLine:r,layout:p,stroke:"none",className:"recharts-area-area"})),"none"!==y&&n.createElement(i.I,V({},(0,d.J9)(s,!1),{className:"recharts-area-curve",layout:p,type:h,connectNulls:f,fill:"none",points:t})),"none"!==y&&m&&n.createElement(i.I,V({},(0,d.J9)(s,!1),{className:"recharts-area-curve",layout:p,type:h,connectNulls:f,fill:"none",points:r}))),n.createElement(q,{points:t,props:s,clipPathId:l}),u&&c.Z.renderCallByParent(s,t))}function Q(e){var{alpha:t,baseLine:r,points:a,strokeWidth:i}=e,l=a[0].y,o=a[a.length-1].y;if(!(0,W.H)(l)||!(0,W.H)(o))return null;var c=t*Math.abs(l-o),s=Math.max(...a.map(e=>e.x||0));return((0,u.Et)(r)?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(...r.map(e=>e.x||0),s)),(0,u.Et)(s))?n.createElement("rect",{x:0,y:l<o?l:l-c,width:s+(i?parseInt("".concat(i),10):1),height:Math.floor(c)}):null}function X(e){var{alpha:t,baseLine:r,points:a,strokeWidth:i}=e,l=a[0].x,o=a[a.length-1].x;if(!(0,W.H)(l)||!(0,W.H)(o))return null;var c=t*Math.abs(l-o),s=Math.max(...a.map(e=>e.y||0));return((0,u.Et)(r)?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(...r.map(e=>e.y||0),s)),(0,u.Et)(s))?n.createElement("rect",{x:l<o?l:l-c,y:0,width:c,height:Math.floor(s+(i?parseInt("".concat(i),10):1))}):null}function U(e){var{alpha:t,layout:r,points:a,baseLine:i,strokeWidth:l}=e;return"vertical"===r?n.createElement(Q,{alpha:t,points:a,baseLine:i,strokeWidth:l}):n.createElement(X,{alpha:t,points:a,baseLine:i,strokeWidth:l})}function Y(e){var{needClip:t,clipPathId:r,props:a,previousPointsRef:i,previousBaselineRef:l}=e,{points:c,baseLine:s,isAnimationActive:p,animationBegin:d,animationDuration:h,animationEasing:y,onAnimationStart:f,onAnimationEnd:m}=a,v=(0,D.n)(a,"recharts-area-"),[g,b]=(0,n.useState)(!0),x=(0,n.useCallback)(()=>{"function"==typeof m&&m(),b(!1)},[m]),E=(0,n.useCallback)(()=>{"function"==typeof f&&f(),b(!0)},[f]),O=i.current,P=l.current;return n.createElement(z.i,{begin:d,duration:h,isActive:p,easing:y,from:{t:0},to:{t:1},onAnimationEnd:x,onAnimationStart:E,key:v},e=>{var{t:p}=e;if(O){var d,h=O.length/c.length,y=1===p?c:c.map((e,t)=>{var r=Math.floor(t*h);if(O[r]){var n=O[r];return R(R({},e),{},{x:(0,u.GW)(n.x,e.x,p),y:(0,u.GW)(n.y,e.y,p)})}return e});return d=(0,u.Et)(s)?(0,u.GW)(P,s,p):(0,u.uy)(s)||(0,u.M8)(s)?(0,u.GW)(P,0,p):s.map((e,t)=>{var r=Math.floor(t*h);if(Array.isArray(P)&&P[r]){var n=P[r];return R(R({},e),{},{x:(0,u.GW)(n.x,e.x,p),y:(0,u.GW)(n.y,e.y,p)})}return e}),p>0&&(i.current=y,l.current=d),n.createElement(F,{points:y,baseLine:d,needClip:t,clipPathId:r,props:a,showLabels:!g})}return p>0&&(i.current=c,l.current=s),n.createElement(o.W,null,n.createElement("defs",null,n.createElement("clipPath",{id:"animationClipPath-".concat(r)},n.createElement(U,{alpha:p,points:c,baseLine:s,layout:a.layout,strokeWidth:a.strokeWidth}))),n.createElement(o.W,{clipPath:"url(#animationClipPath-".concat(r,")")},n.createElement(F,{points:c,baseLine:s,needClip:t,clipPathId:r,props:a,showLabels:!0})))})}function Z(e){var{needClip:t,clipPathId:r,props:a}=e,{points:i,baseLine:l,isAnimationActive:o}=a,c=(0,n.useRef)(null),s=(0,n.useRef)(),u=c.current,p=s.current;return o&&i&&i.length&&(u!==i||p!==l)?n.createElement(Y,{needClip:t,clipPathId:r,props:a,previousPointsRef:c,previousBaselineRef:s}):n.createElement(F,{points:i,baseLine:l,needClip:t,clipPathId:r,props:a,showLabels:!0})}class ee extends n.PureComponent{render(){var e,{hide:t,dot:r,points:i,className:l,top:c,left:s,needClip:p,xAxisId:y,yAxisId:f,width:v,height:g,id:b,baseLine:x}=this.props;if(t)return null;var E=(0,a.$)("recharts-area",l),O=(0,u.uy)(b)?this.id:b,{r:P=3,strokeWidth:A=2}=null!=(e=(0,d.J9)(r,!1))?e:{r:3,strokeWidth:2},k=(0,d.y$)(r),w=2*P+A;return n.createElement(n.Fragment,null,n.createElement(o.W,{className:E},p&&n.createElement("defs",null,n.createElement(m.Q,{clipPathId:O,xAxisId:y,yAxisId:f}),!k&&n.createElement("clipPath",{id:"clipPath-dots-".concat(O)},n.createElement("rect",{x:s-w/2,y:c-w/2,width:v+w,height:g+w}))),n.createElement(Z,{needClip:p,clipPathId:O,props:this.props})),n.createElement(h.W,{points:i,mainColor:B(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(x)&&n.createElement(h.W,{points:x,mainColor:B(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}constructor(){super(...arguments),G(this,"id",(0,u.NF)("recharts-area-"))}}var et={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!s.m.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function er(e){var t,r=(0,S.e)(e,et),{activeDot:a,animationBegin:i,animationDuration:l,animationEasing:o,connectNulls:c,dot:s,fill:u,fillOpacity:p,hide:d,isAnimationActive:h,legendType:y,stroke:f,xAxisId:v,yAxisId:g}=r,x=T(r,H),E=(0,b.WX)(),O=(0,M.fW)(),{needClip:P}=(0,m.l)(v,g),A=(0,I.r)(),k=(0,n.useMemo)(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:c,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,c,e.data,e.dataKey]),{points:w,isRange:N,baseLine:D}=null!=(t=(0,C.G)(e=>j(e,v,g,A,k)))?t:{},{height:W,width:z,left:L,top:K}=(0,b.hj)();return"horizontal"!==E&&"vertical"!==E||"AreaChart"!==O&&"ComposedChart"!==O?null:n.createElement(ee,V({},x,{activeDot:a,animationBegin:i,animationDuration:l,animationEasing:o,baseLine:D,connectNulls:c,dot:s,fill:u,fillOpacity:p,height:W,hide:d,layout:E,isAnimationActive:h,isRange:N,legendType:y,needClip:P,points:w,stroke:f,width:z,left:L,top:K,xAxisId:v,yAxisId:g}))}var en=(e,t,r,n,a)=>{var i=null!=r?r:t;if((0,u.Et)(i))return i;var l="horizontal"===e?a:n,o=l.scale.domain();if("number"===l.type){var c=Math.max(o[0],o[1]),s=Math.min(o[0],o[1]);return"dataMin"===i?s:"dataMax"===i||c<0?c:Math.max(Math.min(o[0],o[1]),0)}return"dataMin"===i?o[0]:"dataMax"===i?o[1]:o[0]};function ea(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:a},stackedData:i,layout:l,chartBaseValue:o,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:d,xAxisTicks:h,yAxisTicks:y,bandSize:f}=e,m=i&&i.length,v=en(l,o,n,c,s),g="horizontal"===l,b=!1,x=u.map((e,t)=>{m?n=i[d+t]:Array.isArray(n=(0,p.kr)(e,a))?b=!0:n=[v,n];var n,l=null==n[1]||m&&!r&&null==(0,p.kr)(e,a);return g?{x:(0,p.nb)({axis:c,ticks:h,bandSize:f,entry:e,index:t}),y:l?null:s.scale(n[1]),value:n,payload:e}:{x:l?null:c.scale(n[1]),y:(0,p.nb)({axis:s,ticks:y,bandSize:f,entry:e,index:t}),value:n,payload:e}});return t=m||b?x.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return g?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}}):g?s.scale(v):c.scale(v),{points:x,baseLine:t,isRange:b}}class ei extends n.PureComponent{render(){return n.createElement(f._S,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},n.createElement(N.A,{legendPayload:_(this.props)}),n.createElement(y.r,{fn:J,args:this.props}),n.createElement(er,this.props))}}G(ei,"displayName","Area"),G(ei,"defaultProps",et)},64683:(e,t,r)=>{r.d(t,{s:()=>S});var n=r(12115),a=r(47650),i=r(15679),l=r(52596),o=r(20241),c=r.n(o),s=r(72790),u=r(9795),p=r(43597);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,a=32/6,i=32/3,l=e.inactive?r:e.color,o=null!=t?t:e.type;if("none"===o)return null;if("plainline"===o)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===o)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===o)return n.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete c.legendIcon,n.cloneElement(e.legendIcon,c)}return n.createElement(u.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:o})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:a,inactiveColor:i,iconType:o,itemSorter:u}=this.props,h={x:0,y:0,width:32,height:32},y={display:"horizontal"===r?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return(u?c()(e,u):e).map((e,r)=>{var c=e.formatter||a,u=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var m=e.inactive?i:e.color,v=c?c(e.value,e,r):e.value;return n.createElement("li",d({className:u,style:y,key:"legend-item-".concat(r)},(0,p.XC)(this.props,e,r)),n.createElement(s.u,{width:t,height:t,viewBox:h,style:f,"aria-label":"".concat(v," legend icon")},this.renderIcon(e,o)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:m}},v))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}y(f,"displayName","Legend"),y(f,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var m=r(16377),v=r(2494),g=r(81971),b=r(35803),x=r(77918),E=r(97238),O=r(32634),P=["contextPayload"];function A(){return(A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){j(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function j(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I(e){return e.value}function M(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,P),a=(0,v.s)(t,e.payloadUniqBy,I),i=w(w({},r),{},{payload:a});return n.isValidElement(e.content)?n.cloneElement(e.content,i):"function"==typeof e.content?n.createElement(e.content,i):n.createElement(f,i)}function N(e){var t=(0,g.j)();return(0,n.useEffect)(()=>{t((0,O.h1)(e))},[t,e]),null}function C(e){var t=(0,g.j)();return(0,n.useEffect)(()=>(t((0,O.hx)(e)),()=>{t((0,O.hx)({width:0,height:0}))}),[t,e]),null}function D(e){var t=(0,g.G)(b.g0),r=(0,i.M)(),l=(0,E.Kp)(),{width:o,height:c,wrapperStyle:s,portal:u}=e,[p,d]=(0,x.V)([t]),h=(0,E.yi)(),y=(0,E.rY)(),f=h-(l.left||0)-(l.right||0),m=S.getWidthOrHeight(e.layout,c,o,f),v=u?s:w(w({position:"absolute",width:(null==m?void 0:m.width)||o||"auto",height:(null==m?void 0:m.height)||c||"auto"},function(e,t,r,n,a,i){var l,o,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===s&&"vertical"===c?{left:((n||0)-i.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(o="middle"===u?{top:((a||0)-i.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),w(w({},l),o)}(s,e,l,h,y,p)),s),O=null!=u?u:r;if(null==O)return null;var P=n.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:d},n.createElement(N,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),n.createElement(C,{width:p.width,height:p.height}),n.createElement(M,A({},e,m,{margin:l,chartWidth:h,chartHeight:y,contextPayload:t})));return(0,a.createPortal)(P,O)}class S extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,m.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(D,this.props)}}j(S,"displayName","Legend"),j(S,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},69037:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},88515:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},94870:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},99445:(e,t,r)=>{r.d(t,{Q:()=>o});var n=r(12115),a=r(46641),i=r(82396),l=["axis"],o=(0,n.forwardRef)((e,t)=>n.createElement(i.P,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))}}]);