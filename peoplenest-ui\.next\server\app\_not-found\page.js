(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7696:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>c,tree:()=>p});var r=n(65239),o=n(48088),i=n(88170),s=n.n(i),a=n(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);n.d(t,l);let p={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],m={require:n,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12872:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19731:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,86346,23)),Promise.resolve().then(n.t.bind(n,27924,23)),Promise.resolve().then(n.t.bind(n,35656,23)),Promise.resolve().then(n.t.bind(n,40099,23)),Promise.resolve().then(n.t.bind(n,38243,23)),Promise.resolve().then(n.t.bind(n,28827,23)),Promise.resolve().then(n.t.bind(n,62763,23)),Promise.resolve().then(n.t.bind(n,97173,23))},26024:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82939:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,16444,23)),Promise.resolve().then(n.t.bind(n,16042,23)),Promise.resolve().then(n.t.bind(n,88170,23)),Promise.resolve().then(n.t.bind(n,49477,23)),Promise.resolve().then(n.t.bind(n,29345,23)),Promise.resolve().then(n.t.bind(n,12089,23)),Promise.resolve().then(n.t.bind(n,46577,23)),Promise.resolve().then(n.t.bind(n,31307,23))},94431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>p,metadata:()=>l});var r=n(37413),o=n(58745),i=n.n(o),s=n(64066),a=n.n(s);n(61135);let l={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",themeColor:"#3b82f6",viewport:{width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover"},appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}};function p({children:e}){return(0,r.jsxs)("html",{lang:"en",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"/icons/icon-192x192.png"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"PeopleNest"}),(0,r.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"application-name",content:"PeopleNest HRMS"}),(0,r.jsx)("meta",{name:"msapplication-TileColor",content:"#3b82f6"}),(0,r.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"}),(0,r.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"})]}),(0,r.jsxs)("body",{className:`${i().variable} ${a().variable} font-sans antialiased touch-manipulation`,children:[e,(0,r.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,912],()=>n(7696));module.exports=r})();