[{"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx": "1", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\analytics\\page.tsx": "2", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\announcements\\page.tsx": "3", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx": "4", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\compliance\\page.tsx": "5", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\documents\\page.tsx": "6", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\employees\\page.tsx": "7", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\help\\page.tsx": "8", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx": "9", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx": "10", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx": "11", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\page.tsx": "12", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx": "13", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\performance\\page.tsx": "14", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx": "15", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx": "16", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx": "17", "C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx": "18", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\employees\\EmployeeForm.tsx": "19", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\employees\\EmployeeProfile.tsx": "20", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\header.tsx": "21", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\mobile-nav.tsx": "22", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\sidebar.tsx": "23", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\avatar.tsx": "24", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\badge.tsx": "25", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\button.tsx": "26", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\card.tsx": "27", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\dialog.tsx": "28", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\input.tsx": "29", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\label.tsx": "30", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\progress.tsx": "31", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\select.tsx": "32", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\switch.tsx": "33", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\tabs.tsx": "34", "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\touch-friendly.tsx": "35", "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\api\\employeeApi.ts": "36", "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\constants.ts": "37", "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\theme.ts": "38", "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\utils.ts": "39"}, {"size": 6657, "mtime": 1750840659365, "results": "40", "hashOfConfig": "41"}, {"size": 11569, "mtime": 1750841108695, "results": "42", "hashOfConfig": "41"}, {"size": 19324, "mtime": 1750840819006, "results": "43", "hashOfConfig": "41"}, {"size": 20920, "mtime": 1750841007604, "results": "44", "hashOfConfig": "41"}, {"size": 24901, "mtime": 1750841206393, "results": "45", "hashOfConfig": "41"}, {"size": 22931, "mtime": 1750831755073, "results": "46", "hashOfConfig": "41"}, {"size": 39274, "mtime": 1750838765767, "results": "47", "hashOfConfig": "41"}, {"size": 26694, "mtime": 1750832053799, "results": "48", "hashOfConfig": "41"}, {"size": 666, "mtime": 1750803411308, "results": "49", "hashOfConfig": "41"}, {"size": 21755, "mtime": 1750831043415, "results": "50", "hashOfConfig": "41"}, {"size": 20493, "mtime": 1750830906297, "results": "51", "hashOfConfig": "41"}, {"size": 22351, "mtime": 1750803363009, "results": "52", "hashOfConfig": "41"}, {"size": 13918, "mtime": 1750804146540, "results": "53", "hashOfConfig": "41"}, {"size": 19003, "mtime": 1750804345619, "results": "54", "hashOfConfig": "41"}, {"size": 17660, "mtime": 1750830838768, "results": "55", "hashOfConfig": "41"}, {"size": 23313, "mtime": 1750831821971, "results": "56", "hashOfConfig": "41"}, {"size": 3105, "mtime": 1750796468760, "results": "57", "hashOfConfig": "41"}, {"size": 2081, "mtime": 1750803267688, "results": "58", "hashOfConfig": "41"}, {"size": 35059, "mtime": 1750803544041, "results": "59", "hashOfConfig": "41"}, {"size": 21287, "mtime": 1750803697055, "results": "60", "hashOfConfig": "41"}, {"size": 11011, "mtime": 1750804591127, "results": "61", "hashOfConfig": "41"}, {"size": 10060, "mtime": 1750839022644, "results": "62", "hashOfConfig": "41"}, {"size": 8270, "mtime": 1750838958694, "results": "63", "hashOfConfig": "41"}, {"size": 2556, "mtime": 1750803509988, "results": "64", "hashOfConfig": "41"}, {"size": 3036, "mtime": 1750802985447, "results": "65", "hashOfConfig": "41"}, {"size": 3133, "mtime": 1750794402561, "results": "66", "hashOfConfig": "41"}, {"size": 2645, "mtime": 1750794428124, "results": "67", "hashOfConfig": "41"}, {"size": 3849, "mtime": 1750838848744, "results": "68", "hashOfConfig": "41"}, {"size": 2788, "mtime": 1750802741667, "results": "69", "hashOfConfig": "41"}, {"size": 724, "mtime": 1750838859106, "results": "70", "hashOfConfig": "41"}, {"size": 791, "mtime": 1750838895019, "results": "71", "hashOfConfig": "41"}, {"size": 5629, "mtime": 1750838885396, "results": "72", "hashOfConfig": "41"}, {"size": 1153, "mtime": 1750838228808, "results": "73", "hashOfConfig": "41"}, {"size": 1897, "mtime": 1750838905721, "results": "74", "hashOfConfig": "41"}, {"size": 9261, "mtime": 1750802773429, "results": "75", "hashOfConfig": "41"}, {"size": 8093, "mtime": 1750798229764, "results": "76", "hashOfConfig": "41"}, {"size": 3771, "mtime": 1750794387608, "results": "77", "hashOfConfig": "41"}, {"size": 5050, "mtime": 1750802908016, "results": "78", "hashOfConfig": "41"}, {"size": 1859, "mtime": 1750794341062, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6qwi2k", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\analytics\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\announcements\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\compliance\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\documents\\page.tsx", ["197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\employees\\page.tsx", ["209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\help\\page.tsx", ["228", "229", "230", "231", "232", "233", "234", "235", "236"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx", ["237", "238", "239", "240", "241"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx", ["242", "243", "244"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\page.tsx", ["245", "246", "247", "248", "249"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\payroll\\page.tsx", ["250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\performance\\page.tsx", ["267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx", ["280", "281", "282"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx", ["283", "284", "285", "286", "287", "288", "289", "290", "291"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\employees\\EmployeeForm.tsx", ["292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304", "305"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\employees\\EmployeeProfile.tsx", ["306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\header.tsx", ["320"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\mobile-nav.tsx", ["321"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\layout\\sidebar.tsx", ["322", "323", "324"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\avatar.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\badge.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\button.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\card.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\dialog.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\input.tsx", ["325"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\label.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\progress.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\select.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\switch.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\tabs.tsx", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\components\\ui\\touch-friendly.tsx", ["326", "327"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\api\\employeeApi.ts", ["328", "329", "330"], [], "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\constants.ts", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\theme.ts", [], [], "C:\\PeopleNest\\peoplenest-ui\\src\\lib\\utils.ts", ["331", "332"], [], {"ruleId": "333", "severity": 2, "message": "334", "line": 13, "column": 3, "nodeType": null, "messageId": "335", "endLine": 13, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "336", "line": 15, "column": 3, "nodeType": null, "messageId": "335", "endLine": 15, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "337", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "338", "line": 19, "column": 3, "nodeType": null, "messageId": "335", "endLine": 19, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "339", "line": 28, "column": 3, "nodeType": null, "messageId": "335", "endLine": 28, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "340", "line": 29, "column": 3, "nodeType": null, "messageId": "335", "endLine": 29, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "341", "line": 33, "column": 29, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 44}, {"ruleId": "333", "severity": 2, "message": "342", "line": 33, "column": 46, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 56}, {"ruleId": "333", "severity": 2, "message": "343", "line": 33, "column": 58, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 67}, {"ruleId": "344", "severity": 1, "message": "345", "line": 166, "column": 63, "nodeType": "346", "endLine": 166, "endColumn": 108}, {"ruleId": "347", "severity": 2, "message": "348", "line": 458, "column": 80, "nodeType": "349", "messageId": "350", "endLine": 458, "endColumn": 83, "suggestions": "351"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 537, "column": 76, "nodeType": "349", "messageId": "350", "endLine": 537, "endColumn": 79, "suggestions": "352"}, {"ruleId": "333", "severity": 2, "message": "353", "line": 12, "column": 3, "nodeType": null, "messageId": "335", "endLine": 12, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "354", "line": 26, "column": 3, "nodeType": null, "messageId": "335", "endLine": 26, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "355", "line": 35, "column": 3, "nodeType": null, "messageId": "335", "endLine": 35, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "356", "line": 37, "column": 3, "nodeType": null, "messageId": "335", "endLine": 37, "endColumn": 14}, {"ruleId": "333", "severity": 2, "message": "341", "line": 41, "column": 29, "nodeType": null, "messageId": "335", "endLine": 41, "endColumn": 44}, {"ruleId": "333", "severity": 2, "message": "343", "line": 41, "column": 58, "nodeType": null, "messageId": "335", "endLine": 41, "endColumn": 67}, {"ruleId": "333", "severity": 2, "message": "357", "line": 53, "column": 7, "nodeType": null, "messageId": "335", "endLine": 53, "endColumn": 16}, {"ruleId": "333", "severity": 2, "message": "358", "line": 140, "column": 7, "nodeType": null, "messageId": "335", "endLine": 140, "endColumn": 18}, {"ruleId": "333", "severity": 2, "message": "359", "line": 151, "column": 23, "nodeType": null, "messageId": "335", "endLine": 151, "endColumn": 37}, {"ruleId": "333", "severity": 2, "message": "360", "line": 152, "column": 10, "nodeType": null, "messageId": "335", "endLine": 152, "endColumn": 20}, {"ruleId": "361", "severity": 1, "message": "362", "line": 177, "column": 6, "nodeType": "363", "endLine": 177, "endColumn": 8, "suggestions": "364"}, {"ruleId": "361", "severity": 1, "message": "365", "line": 182, "column": 6, "nodeType": "363", "endLine": 182, "endColumn": 87, "suggestions": "366"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 216, "column": 74, "nodeType": "349", "messageId": "350", "endLine": 216, "endColumn": 77, "suggestions": "367"}, {"ruleId": "333", "severity": 2, "message": "368", "line": 308, "column": 9, "nodeType": null, "messageId": "335", "endLine": 308, "endColumn": 27}, {"ruleId": "333", "severity": 2, "message": "369", "line": 352, "column": 14, "nodeType": null, "messageId": "335", "endLine": 352, "endColumn": 19}, {"ruleId": "333", "severity": 2, "message": "370", "line": 377, "column": 29, "nodeType": null, "messageId": "335", "endLine": 377, "endColumn": 39}, {"ruleId": "347", "severity": 2, "message": "348", "line": 382, "column": 49, "nodeType": "349", "messageId": "350", "endLine": 382, "endColumn": 52, "suggestions": "371"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 778, "column": 72, "nodeType": "349", "messageId": "350", "endLine": 778, "endColumn": 75, "suggestions": "372"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 908, "column": 76, "nodeType": "349", "messageId": "350", "endLine": 908, "endColumn": 79, "suggestions": "373"}, {"ruleId": "333", "severity": 2, "message": "374", "line": 12, "column": 3, "nodeType": null, "messageId": "335", "endLine": 12, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "375", "line": 14, "column": 3, "nodeType": null, "messageId": "335", "endLine": 14, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "376", "line": 25, "column": 3, "nodeType": null, "messageId": "335", "endLine": 25, "endColumn": 6}, {"ruleId": "333", "severity": 2, "message": "377", "line": 37, "column": 10, "nodeType": null, "messageId": "335", "endLine": 37, "endColumn": 16}, {"ruleId": "333", "severity": 2, "message": "378", "line": 37, "column": 18, "nodeType": null, "messageId": "335", "endLine": 37, "endColumn": 32}, {"ruleId": "333", "severity": 2, "message": "379", "line": 37, "column": 34, "nodeType": null, "messageId": "335", "endLine": 37, "endColumn": 45}, {"ruleId": "347", "severity": 2, "message": "348", "line": 532, "column": 78, "nodeType": "349", "messageId": "350", "endLine": 532, "endColumn": 81, "suggestions": "380"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 536, "column": 80, "nodeType": "349", "messageId": "350", "endLine": 536, "endColumn": 83, "suggestions": "381"}, {"ruleId": "382", "severity": 2, "message": "383", "line": 624, "column": 62, "nodeType": "384", "messageId": "385", "suggestions": "386"}, {"ruleId": "333", "severity": 2, "message": "336", "line": 18, "column": 3, "nodeType": null, "messageId": "335", "endLine": 18, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "387", "line": 31, "column": 10, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 19}, {"ruleId": "333", "severity": 2, "message": "388", "line": 31, "column": 21, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 25}, {"ruleId": "347", "severity": 2, "message": "348", "line": 402, "column": 75, "nodeType": "349", "messageId": "350", "endLine": 402, "endColumn": 78, "suggestions": "389"}, {"ruleId": "390", "severity": 2, "message": "391", "line": 513, "column": 20, "nodeType": "392", "messageId": "393", "endLine": 513, "endColumn": 23}, {"ruleId": "333", "severity": 2, "message": "353", "line": 19, "column": 3, "nodeType": null, "messageId": "335", "endLine": 19, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "336", "line": 23, "column": 3, "nodeType": null, "messageId": "335", "endLine": 23, "endColumn": 9}, {"ruleId": "347", "severity": 2, "message": "348", "line": 379, "column": 72, "nodeType": "349", "messageId": "350", "endLine": 379, "endColumn": 75, "suggestions": "394"}, {"ruleId": "333", "severity": 2, "message": "395", "line": 18, "column": 3, "nodeType": null, "messageId": "335", "endLine": 18, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "396", "line": 21, "column": 3, "nodeType": null, "messageId": "335", "endLine": 21, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "397", "line": 22, "column": 3, "nodeType": null, "messageId": "335", "endLine": 22, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "398", "line": 40, "column": 3, "nodeType": null, "messageId": "335", "endLine": 40, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "399", "line": 41, "column": 3, "nodeType": null, "messageId": "335", "endLine": 41, "endColumn": 6}, {"ruleId": "333", "severity": 2, "message": "339", "line": 7, "column": 3, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "374", "line": 10, "column": 3, "nodeType": null, "messageId": "335", "endLine": 10, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "338", "line": 11, "column": 3, "nodeType": null, "messageId": "335", "endLine": 11, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "400", "line": 12, "column": 3, "nodeType": null, "messageId": "335", "endLine": 12, "endColumn": 13}, {"ruleId": "333", "severity": 2, "message": "336", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "401", "line": 18, "column": 3, "nodeType": null, "messageId": "335", "endLine": 18, "endColumn": 17}, {"ruleId": "333", "severity": 2, "message": "402", "line": 23, "column": 3, "nodeType": null, "messageId": "335", "endLine": 23, "endColumn": 13}, {"ruleId": "333", "severity": 2, "message": "403", "line": 24, "column": 3, "nodeType": null, "messageId": "335", "endLine": 24, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "340", "line": 25, "column": 3, "nodeType": null, "messageId": "335", "endLine": 25, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "404", "line": 26, "column": 3, "nodeType": null, "messageId": "335", "endLine": 26, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "353", "line": 27, "column": 3, "nodeType": null, "messageId": "335", "endLine": 27, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "341", "line": 31, "column": 29, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 44}, {"ruleId": "333", "severity": 2, "message": "342", "line": 31, "column": 46, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 56}, {"ruleId": "333", "severity": 2, "message": "343", "line": 31, "column": 58, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 67}, {"ruleId": "333", "severity": 2, "message": "405", "line": 122, "column": 10, "nodeType": null, "messageId": "335", "endLine": 122, "endColumn": 27}, {"ruleId": "333", "severity": 2, "message": "406", "line": 122, "column": 29, "nodeType": null, "messageId": "335", "endLine": 122, "endColumn": 49}, {"ruleId": "347", "severity": 2, "message": "348", "line": 335, "column": 73, "nodeType": "349", "messageId": "350", "endLine": 335, "endColumn": 76, "suggestions": "407"}, {"ruleId": "333", "severity": 2, "message": "408", "line": 8, "column": 3, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "339", "line": 9, "column": 3, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "338", "line": 10, "column": 3, "nodeType": null, "messageId": "335", "endLine": 10, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "396", "line": 13, "column": 3, "nodeType": null, "messageId": "335", "endLine": 13, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "336", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "340", "line": 24, "column": 3, "nodeType": null, "messageId": "335", "endLine": 24, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "403", "line": 25, "column": 3, "nodeType": null, "messageId": "335", "endLine": 25, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "409", "line": 33, "column": 27, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 36}, {"ruleId": "333", "severity": 2, "message": "410", "line": 33, "column": 38, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 42}, {"ruleId": "333", "severity": 2, "message": "411", "line": 33, "column": 71, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 87}, {"ruleId": "333", "severity": 2, "message": "412", "line": 33, "column": 89, "nodeType": null, "messageId": "335", "endLine": 33, "endColumn": 93}, {"ruleId": "333", "severity": 2, "message": "413", "line": 123, "column": 7, "nodeType": null, "messageId": "335", "endLine": 123, "endColumn": 13}, {"ruleId": "347", "severity": 2, "message": "348", "line": 444, "column": 77, "nodeType": "349", "messageId": "350", "endLine": 444, "endColumn": 80, "suggestions": "414"}, {"ruleId": "333", "severity": 2, "message": "336", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "415", "line": 22, "column": 3, "nodeType": null, "messageId": "335", "endLine": 22, "endColumn": 16}, {"ruleId": "347", "severity": 2, "message": "348", "line": 373, "column": 77, "nodeType": "349", "messageId": "350", "endLine": 373, "endColumn": 80, "suggestions": "416"}, {"ruleId": "333", "severity": 2, "message": "355", "line": 6, "column": 3, "nodeType": null, "messageId": "335", "endLine": 6, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "417", "line": 11, "column": 3, "nodeType": null, "messageId": "335", "endLine": 11, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "338", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "418", "line": 19, "column": 3, "nodeType": null, "messageId": "335", "endLine": 19, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "419", "line": 20, "column": 3, "nodeType": null, "messageId": "335", "endLine": 20, "endColumn": 13}, {"ruleId": "333", "severity": 2, "message": "375", "line": 24, "column": 3, "nodeType": null, "messageId": "335", "endLine": 24, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "420", "line": 108, "column": 10, "nodeType": null, "messageId": "335", "endLine": 108, "endColumn": 23}, {"ruleId": "333", "severity": 2, "message": "421", "line": 108, "column": 25, "nodeType": null, "messageId": "335", "endLine": 108, "endColumn": 41}, {"ruleId": "347", "severity": 2, "message": "348", "line": 129, "column": 55, "nodeType": "349", "messageId": "350", "endLine": 129, "endColumn": 58, "suggestions": "422"}, {"ruleId": "333", "severity": 2, "message": "423", "line": 14, "column": 3, "nodeType": null, "messageId": "335", "endLine": 14, "endColumn": 12}, {"ruleId": "333", "severity": 2, "message": "403", "line": 16, "column": 3, "nodeType": null, "messageId": "335", "endLine": 16, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "338", "line": 17, "column": 3, "nodeType": null, "messageId": "335", "endLine": 17, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "356", "line": 21, "column": 3, "nodeType": null, "messageId": "335", "endLine": 21, "endColumn": 14}, {"ruleId": "333", "severity": 2, "message": "424", "line": 22, "column": 3, "nodeType": null, "messageId": "335", "endLine": 22, "endColumn": 14}, {"ruleId": "333", "severity": 2, "message": "425", "line": 29, "column": 10, "nodeType": null, "messageId": "335", "endLine": 29, "endColumn": 18}, {"ruleId": "333", "severity": 2, "message": "426", "line": 31, "column": 10, "nodeType": null, "messageId": "335", "endLine": 31, "endColumn": 18}, {"ruleId": "347", "severity": 2, "message": "348", "line": 77, "column": 14, "nodeType": "349", "messageId": "350", "endLine": 77, "endColumn": 17, "suggestions": "427"}, {"ruleId": "333", "severity": 2, "message": "428", "line": 108, "column": 5, "nodeType": null, "messageId": "335", "endLine": 108, "endColumn": 10}, {"ruleId": "347", "severity": 2, "message": "348", "line": 347, "column": 79, "nodeType": "349", "messageId": "350", "endLine": 347, "endColumn": 82, "suggestions": "429"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 506, "column": 85, "nodeType": "349", "messageId": "350", "endLine": 506, "endColumn": 88, "suggestions": "430"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 533, "column": 85, "nodeType": "349", "messageId": "350", "endLine": 533, "endColumn": 88, "suggestions": "431"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 785, "column": 85, "nodeType": "349", "messageId": "350", "endLine": 785, "endColumn": 88, "suggestions": "432"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 812, "column": 85, "nodeType": "349", "messageId": "350", "endLine": 812, "endColumn": 88, "suggestions": "433"}, {"ruleId": "333", "severity": 2, "message": "434", "line": 9, "column": 3, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "403", "line": 12, "column": 3, "nodeType": null, "messageId": "335", "endLine": 12, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "338", "line": 13, "column": 3, "nodeType": null, "messageId": "335", "endLine": 13, "endColumn": 8}, {"ruleId": "333", "severity": 2, "message": "400", "line": 21, "column": 3, "nodeType": null, "messageId": "335", "endLine": 21, "endColumn": 13}, {"ruleId": "333", "severity": 2, "message": "419", "line": 23, "column": 3, "nodeType": null, "messageId": "335", "endLine": 23, "endColumn": 13}, {"ruleId": "333", "severity": 2, "message": "374", "line": 24, "column": 3, "nodeType": null, "messageId": "335", "endLine": 24, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "435", "line": 25, "column": 3, "nodeType": null, "messageId": "335", "endLine": 25, "endColumn": 16}, {"ruleId": "333", "severity": 2, "message": "355", "line": 26, "column": 3, "nodeType": null, "messageId": "335", "endLine": 26, "endColumn": 11}, {"ruleId": "333", "severity": 2, "message": "424", "line": 29, "column": 3, "nodeType": null, "messageId": "335", "endLine": 29, "endColumn": 14}, {"ruleId": "333", "severity": 2, "message": "436", "line": 38, "column": 10, "nodeType": null, "messageId": "335", "endLine": 38, "endColumn": 19}, {"ruleId": "333", "severity": 2, "message": "437", "line": 72, "column": 55, "nodeType": null, "messageId": "335", "endLine": 72, "endColumn": 62}, {"ruleId": "361", "severity": 1, "message": "438", "line": 79, "column": 6, "nodeType": "363", "endLine": 79, "endColumn": 18, "suggestions": "439"}, {"ruleId": "333", "severity": 2, "message": "369", "line": 101, "column": 14, "nodeType": null, "messageId": "335", "endLine": 101, "endColumn": 19}, {"ruleId": "333", "severity": 2, "message": "369", "line": 110, "column": 14, "nodeType": null, "messageId": "335", "endLine": 110, "endColumn": 19}, {"ruleId": "333", "severity": 2, "message": "440", "line": 13, "column": 3, "nodeType": null, "messageId": "335", "endLine": 13, "endColumn": 6}, {"ruleId": "333", "severity": 2, "message": "441", "line": 44, "column": 29, "nodeType": null, "messageId": "335", "endLine": 44, "endColumn": 38}, {"ruleId": "333", "severity": 2, "message": "442", "line": 19, "column": 3, "nodeType": null, "messageId": "335", "endLine": 19, "endColumn": 7}, {"ruleId": "333", "severity": 2, "message": "443", "line": 20, "column": 3, "nodeType": null, "messageId": "335", "endLine": 20, "endColumn": 9}, {"ruleId": "333", "severity": 2, "message": "444", "line": 133, "column": 42, "nodeType": null, "messageId": "335", "endLine": 133, "endColumn": 54}, {"ruleId": "445", "severity": 2, "message": "446", "line": 51, "column": 27, "nodeType": "447", "endLine": 51, "endColumn": 38}, {"ruleId": "333", "severity": 2, "message": "448", "line": 3, "column": 28, "nodeType": null, "messageId": "335", "endLine": 3, "endColumn": 37}, {"ruleId": "347", "severity": 2, "message": "348", "line": 108, "column": 33, "nodeType": "349", "messageId": "350", "endLine": 108, "endColumn": 36, "suggestions": "449"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 125, "column": 13, "nodeType": "349", "messageId": "350", "endLine": 125, "endColumn": 16, "suggestions": "450"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 126, "column": 20, "nodeType": "349", "messageId": "350", "endLine": 126, "endColumn": 23, "suggestions": "451"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 127, "column": 18, "nodeType": "349", "messageId": "350", "endLine": 127, "endColumn": 21, "suggestions": "452"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 48, "column": 46, "nodeType": "349", "messageId": "350", "endLine": 48, "endColumn": 49, "suggestions": "453"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 48, "column": 56, "nodeType": "349", "messageId": "350", "endLine": 48, "endColumn": 59, "suggestions": "454"}, "@typescript-eslint/no-unused-vars", "'Trash2' is defined but never used.", "unusedVar", "'Filter' is defined but never used.", "'Plus' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'User' is defined but never used.", "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["455", "456"], ["457", "458"], "'Phone' is defined but never used.", "'UserX' is defined but never used.", "'Settings' is defined but never used.", "'AlertCircle' is defined but never used.", "'employees' is assigned a value but never used.", "'departments' is assigned a value but never used.", "'setCurrentPage' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", "ArrayExpression", ["459"], "React Hook useEffect has a missing dependency: 'loadEmployees'. Either include it or remove the dependency array.", ["460"], ["461", "462"], "'handleViewEmployee' is assigned a value but never used.", "'error' is defined but never used.", "'employeeId' is defined but never used.", ["463", "464"], ["465", "466"], ["467", "468"], "'FileText' is defined but never used.", "'Download' is defined but never used.", "'Zap' is defined but never used.", "'Avatar' is defined but never used.", "'AvatarFallback' is defined but never used.", "'AvatarImage' is defined but never used.", ["469", "470"], ["471", "472"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["473", "474", "475", "476"], "'LineChart' is defined but never used.", "'Line' is defined but never used.", ["477", "478"], "react/jsx-no-undef", "'Pie' is not defined.", "JSXIdentifier", "undefined", ["479", "480"], "'Target' is defined but never used.", "'PieChart' is defined but never used.", "'Activity' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'TrendingUp' is defined but never used.", "'MoreHorizontal' is defined but never used.", "'CreditCard' is defined but never used.", "'Building' is defined but never used.", "'Mail' is defined but never used.", "'selectedPayPeriod' is assigned a value but never used.", "'setSelectedPayPeriod' is assigned a value but never used.", ["481", "482"], "'Award' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'RechartsPieChart' is defined but never used.", "'Cell' is defined but never used.", "'COLORS' is assigned a value but never used.", ["483", "484"], "'GraduationCap' is defined but never used.", ["485", "486"], "'Globe' is defined but never used.", "'Clock' is defined but never used.", "'DollarSign' is defined but never used.", "'companyConfig' is assigned a value but never used.", "'setCompanyConfig' is assigned a value but never used.", ["487", "488"], "'Briefcase' is defined but never used.", "'CheckCircle' is defined but never used.", "'Textarea' is defined but never used.", "'Checkbox' is defined but never used.", ["489", "490"], "'reset' is assigned a value but never used.", ["491", "492"], ["493", "494"], ["495", "496"], ["497", "498"], ["499", "500"], "'MapPin' is defined but never used.", "'MessageSquare' is defined but never used.", "'Separator' is defined but never used.", "'onClose' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEmployeeProfile'. Either include it or remove the dependency array.", ["501"], "'Sun' is defined but never used.", "'className' is defined but never used.", "'Bell' is defined but never used.", "'Search' is defined but never used.", "'sectionIndex' is defined but never used.", "react-hooks/rules-of-hooks", "React Hook \"React.useId\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "MemberExpression", "'useEffect' is defined but never used.", ["502", "503"], ["504", "505"], ["506", "507"], ["508", "509"], ["510", "511"], ["512", "513"], {"messageId": "514", "fix": "515", "desc": "516"}, {"messageId": "517", "fix": "518", "desc": "519"}, {"messageId": "514", "fix": "520", "desc": "516"}, {"messageId": "517", "fix": "521", "desc": "519"}, {"desc": "522", "fix": "523"}, {"desc": "524", "fix": "525"}, {"messageId": "514", "fix": "526", "desc": "516"}, {"messageId": "517", "fix": "527", "desc": "519"}, {"messageId": "514", "fix": "528", "desc": "516"}, {"messageId": "517", "fix": "529", "desc": "519"}, {"messageId": "514", "fix": "530", "desc": "516"}, {"messageId": "517", "fix": "531", "desc": "519"}, {"messageId": "514", "fix": "532", "desc": "516"}, {"messageId": "517", "fix": "533", "desc": "519"}, {"messageId": "514", "fix": "534", "desc": "516"}, {"messageId": "517", "fix": "535", "desc": "519"}, {"messageId": "514", "fix": "536", "desc": "516"}, {"messageId": "517", "fix": "537", "desc": "519"}, {"messageId": "538", "data": "539", "fix": "540", "desc": "541"}, {"messageId": "538", "data": "542", "fix": "543", "desc": "544"}, {"messageId": "538", "data": "545", "fix": "546", "desc": "547"}, {"messageId": "538", "data": "548", "fix": "549", "desc": "550"}, {"messageId": "514", "fix": "551", "desc": "516"}, {"messageId": "517", "fix": "552", "desc": "519"}, {"messageId": "514", "fix": "553", "desc": "516"}, {"messageId": "517", "fix": "554", "desc": "519"}, {"messageId": "514", "fix": "555", "desc": "516"}, {"messageId": "517", "fix": "556", "desc": "519"}, {"messageId": "514", "fix": "557", "desc": "516"}, {"messageId": "517", "fix": "558", "desc": "519"}, {"messageId": "514", "fix": "559", "desc": "516"}, {"messageId": "517", "fix": "560", "desc": "519"}, {"messageId": "514", "fix": "561", "desc": "516"}, {"messageId": "517", "fix": "562", "desc": "519"}, {"messageId": "514", "fix": "563", "desc": "516"}, {"messageId": "517", "fix": "564", "desc": "519"}, {"messageId": "514", "fix": "565", "desc": "516"}, {"messageId": "517", "fix": "566", "desc": "519"}, {"messageId": "514", "fix": "567", "desc": "516"}, {"messageId": "517", "fix": "568", "desc": "519"}, {"messageId": "514", "fix": "569", "desc": "516"}, {"messageId": "517", "fix": "570", "desc": "519"}, {"messageId": "514", "fix": "571", "desc": "516"}, {"messageId": "517", "fix": "572", "desc": "519"}, {"messageId": "514", "fix": "573", "desc": "516"}, {"messageId": "517", "fix": "574", "desc": "519"}, {"desc": "575", "fix": "576"}, {"messageId": "514", "fix": "577", "desc": "516"}, {"messageId": "517", "fix": "578", "desc": "519"}, {"messageId": "514", "fix": "579", "desc": "516"}, {"messageId": "517", "fix": "580", "desc": "519"}, {"messageId": "514", "fix": "581", "desc": "516"}, {"messageId": "517", "fix": "582", "desc": "519"}, {"messageId": "514", "fix": "583", "desc": "516"}, {"messageId": "517", "fix": "584", "desc": "519"}, {"messageId": "514", "fix": "585", "desc": "516"}, {"messageId": "517", "fix": "586", "desc": "519"}, {"messageId": "514", "fix": "587", "desc": "516"}, {"messageId": "517", "fix": "588", "desc": "519"}, "suggestUnknown", {"range": "589", "text": "590"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "591", "text": "592"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "593", "text": "590"}, {"range": "594", "text": "592"}, "Update the dependencies array to be: [loadInitialData]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [searchQuery, selectedDepartment, selectedStatus, sortBy, sortOrder, currentPage, loadEmployees]", {"range": "597", "text": "598"}, {"range": "599", "text": "590"}, {"range": "600", "text": "592"}, {"range": "601", "text": "590"}, {"range": "602", "text": "592"}, {"range": "603", "text": "590"}, {"range": "604", "text": "592"}, {"range": "605", "text": "590"}, {"range": "606", "text": "592"}, {"range": "607", "text": "590"}, {"range": "608", "text": "592"}, {"range": "609", "text": "590"}, {"range": "610", "text": "592"}, "replaceWithAlt", {"alt": "611"}, {"range": "612", "text": "613"}, "Replace with `&apos;`.", {"alt": "614"}, {"range": "615", "text": "616"}, "Replace with `&lsquo;`.", {"alt": "617"}, {"range": "618", "text": "619"}, "Replace with `&#39;`.", {"alt": "620"}, {"range": "621", "text": "622"}, "Replace with `&rsquo;`.", {"range": "623", "text": "590"}, {"range": "624", "text": "592"}, {"range": "625", "text": "590"}, {"range": "626", "text": "592"}, {"range": "627", "text": "590"}, {"range": "628", "text": "592"}, {"range": "629", "text": "590"}, {"range": "630", "text": "592"}, {"range": "631", "text": "590"}, {"range": "632", "text": "592"}, {"range": "633", "text": "590"}, {"range": "634", "text": "592"}, {"range": "635", "text": "590"}, {"range": "636", "text": "592"}, {"range": "637", "text": "590"}, {"range": "638", "text": "592"}, {"range": "639", "text": "590"}, {"range": "640", "text": "592"}, {"range": "641", "text": "590"}, {"range": "642", "text": "592"}, {"range": "643", "text": "590"}, {"range": "644", "text": "592"}, {"range": "645", "text": "590"}, {"range": "646", "text": "592"}, "Update the dependencies array to be: [employeeId, loadEmployeeProfile]", {"range": "647", "text": "648"}, {"range": "649", "text": "590"}, {"range": "650", "text": "592"}, {"range": "651", "text": "590"}, {"range": "652", "text": "592"}, {"range": "653", "text": "590"}, {"range": "654", "text": "592"}, {"range": "655", "text": "590"}, {"range": "656", "text": "592"}, {"range": "657", "text": "590"}, {"range": "658", "text": "592"}, {"range": "659", "text": "590"}, {"range": "660", "text": "592"}, [18033, 18036], "unknown", [18033, 18036], "never", [22100, 22103], [22100, 22103], [5504, 5506], "[loadInitialData]", [5594, 5675], "[searchQuery, selectedDepartment, selectedStatus, sortBy, sortOrder, currentPage, loadEmployees]", [6799, 6802], [6799, 6802], [12071, 12074], [12071, 12074], [28366, 28369], [28366, 28369], [35499, 35502], [35499, 35502], [20364, 20367], [20364, 20367], [20565, 20568], [20565, 20568], "&apos;", [24505, 24559], "Fill out the form below and we&apos;ll get back to you soon", "&lsquo;", [24505, 24559], "Fill out the form below and we&lsquo;ll get back to you soon", "&#39;", [24505, 24559], "Fill out the form below and we&#39;ll get back to you soon", "&rsquo;", [24505, 24559], "Fill out the form below and we&rsquo;ll get back to you soon", [15805, 15808], [15805, 15808], [15313, 15316], [15313, 15316], [12806, 12809], [12806, 12809], [17852, 17855], [17852, 17855], [13678, 13681], [13678, 13681], [3288, 3291], [3288, 3291], [2714, 2717], [2714, 2717], [11803, 11806], [11803, 11806], [18243, 18246], [18243, 18246], [19418, 19421], [19418, 19421], [29471, 29474], [29471, 29474], [30646, 30649], [30646, 30649], [1827, 1839], "[employeeId, loadEmployeeProfile]", [2927, 2930], [2927, 2930], [3094, 3097], [3094, 3097], [3119, 3122], [3119, 3122], [3142, 3145], [3142, 3145], [1133, 1136], [1133, 1136], [1143, 1146], [1143, 1146]]