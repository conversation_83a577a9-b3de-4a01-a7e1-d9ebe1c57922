1:"$Sreact.fragment"
2:I[87555,[],""]
3:I[31295,[],""]
4:I[94970,[],"ClientSegmentRoot"]
5:I[14554,["706","static/chunks/706-2d6968fbc25dc983.js","352","static/chunks/352-85bf3c1b3189c5b6.js","289","static/chunks/289-50888f0cf6985b94.js","620","static/chunks/620-21c8b862950cc7bd.js","954","static/chunks/app/dashboard/layout-797715d4efbc7430.js"],"default"]
7:I[90894,[],"ClientPageRoot"]
8:I[93233,["706","static/chunks/706-2d6968fbc25dc983.js","352","static/chunks/352-85bf3c1b3189c5b6.js","289","static/chunks/289-50888f0cf6985b94.js","728","static/chunks/728-e5f41d4f91690ce1.js","262","static/chunks/262-4980819a2b903cd6.js","587","static/chunks/app/dashboard/onboarding/page-57515879ebc41389.js"],"default"]
b:I[59665,[],"OutletBoundary"]
e:I[74911,[],"AsyncMetadataOutlet"]
10:I[59665,[],"ViewportBoundary"]
12:I[59665,[],"MetadataBoundary"]
14:I[26614,[],""]
:HL["/_next/static/css/6dd119916675a23e.css","style"]
0:{"P":null,"b":"3cTJBW6vgHrxRr3ICgYmy","p":"","c":["","dashboard","onboarding"],"i":false,"f":[[["",{"children":["dashboard",{"children":["onboarding",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/6dd119916675a23e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","link",null,{"rel":"manifest","href":"/manifest.json"}],["$","meta",null,{"name":"theme-color","content":"#3b82f6"}],["$","link",null,{"rel":"apple-touch-icon","href":"/icons/icon-192x192.png"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta",null,{"name":"apple-mobile-web-app-title","content":"PeopleNest"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"application-name","content":"PeopleNest HRMS"}],["$","meta",null,{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta",null,{"name":"msapplication-tap-highlight","content":"no"}],["$","meta",null,{"name":"format-detection","content":"telephone=no"}],["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"}]]}],["$","body",null,{"className":"__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation","children":[["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            "}}]]}]]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L4",null,{"Component":"$5","slots":{"children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]},"params":{},"promise":"$@6"}]]}],{"children":["onboarding",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":"$0:f:0:1:2:children:1:props:children:1:props:params","promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","gTl6DuNqWm1PbWeP9t9UAv",{"children":[["$","$L10",null,{"children":"$L11"}],null]}],["$","$L12",null,{"children":"$L13"}]]}],false]],"m":"$undefined","G":["$14","$undefined"],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[74911,[],"AsyncMetadata"]
6:{}
9:{}
a:{}
13:["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":["$","$L16",null,{"promise":"$@17"}]}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
f:{"metadata":[["$","title","0",{"children":"PeopleNest - Enterprise HRMS Platform"}],["$","meta","1",{"name":"description","content":"Modern, AI-powered Human Resource Management System for enterprise organizations"}],["$","link","2",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","3",{"name":"keywords","content":"HRMS,HR,Human Resources,Employee Management,Payroll,Performance"}],["$","meta","4",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","5",{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta","6",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","7",{"name":"apple-mobile-web-app-title","content":"PeopleNest"}],["$","meta","8",{"name":"application-name","content":"PeopleNest HRMS"}],["$","meta","9",{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta","10",{"name":"msapplication-config","content":"/browserconfig.xml"}],["$","meta","11",{"name":"format-detection","content":"telephone=no"}],["$","meta","12",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","13",{"name":"apple-mobile-web-app-title","content":"PeopleNest HRMS"}],["$","meta","14",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
17:{"metadata":"$f:metadata","error":null,"digest":"$undefined"}
