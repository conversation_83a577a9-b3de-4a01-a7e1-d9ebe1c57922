"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{1243:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17576:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},33109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47298:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},51976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},54861:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},64683:(e,t,r)=>{r.d(t,{s:()=>L});var n=r(12115),a=r(47650),i=r(15679),l=r(52596),c=r(20241),o=r.n(c),u=r(72790),s=r(9795),d=r(43597);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,a=32/6,i=32/3,l=e.inactive?r:e.color,c=null!=t?t:e.type;if("none"===c)return null;if("plainline"===c)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===c)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===c)return n.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var o=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete o.legendIcon,n.cloneElement(e.legendIcon,o)}return n.createElement(s.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:c})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:a,inactiveColor:i,iconType:c,itemSorter:s}=this.props,p={x:0,y:0,width:32,height:32},y={display:"horizontal"===r?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return(s?o()(e,s):e).map((e,r)=>{var o=e.formatter||a,s=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var g=e.inactive?i:e.color,m=o?o(e.value,e,r):e.value;return n.createElement("li",h({className:s,style:y,key:"legend-item-".concat(r)},(0,d.XC)(this.props,e,r)),n.createElement(u.u,{width:t,height:t,viewBox:p,style:f,"aria-label":"".concat(m," legend icon")},this.renderIcon(e,c)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},m))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}y(f,"displayName","Legend"),y(f,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var g=r(16377),m=r(2494),v=r(81971),b=r(35803),O=r(77918),k=r(97238),w=r(32634),j=["contextPayload"];function E(){return(E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){return e.value}function S(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,j),a=(0,m.s)(t,e.payloadUniqBy,M),i=P(P({},r),{},{payload:a});return n.isValidElement(e.content)?n.cloneElement(e.content,i):"function"==typeof e.content?n.createElement(e.content,i):n.createElement(f,i)}function z(e){var t=(0,v.j)();return(0,n.useEffect)(()=>{t((0,w.h1)(e))},[t,e]),null}function N(e){var t=(0,v.j)();return(0,n.useEffect)(()=>(t((0,w.hx)(e)),()=>{t((0,w.hx)({width:0,height:0}))}),[t,e]),null}function C(e){var t=(0,v.G)(b.g0),r=(0,i.M)(),l=(0,k.Kp)(),{width:c,height:o,wrapperStyle:u,portal:s}=e,[d,h]=(0,O.V)([t]),p=(0,k.yi)(),y=(0,k.rY)(),f=p-(l.left||0)-(l.right||0),g=L.getWidthOrHeight(e.layout,o,c,f),m=s?u:P(P({position:"absolute",width:(null==g?void 0:g.width)||c||"auto",height:(null==g?void 0:g.height)||o||"auto"},function(e,t,r,n,a,i){var l,c,{layout:o,align:u,verticalAlign:s}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===u&&"vertical"===o?{left:((n||0)-i.width)/2}:"right"===u?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(c="middle"===s?{top:((a||0)-i.height)/2}:"bottom"===s?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),P(P({},l),c)}(u,e,l,p,y,d)),u),w=null!=s?s:r;if(null==w)return null;var j=n.createElement("div",{className:"recharts-legend-wrapper",style:m,ref:h},n.createElement(z,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),n.createElement(N,{width:d.width,height:d.height}),n.createElement(S,E({},e,g,{margin:l,chartWidth:p,chartHeight:y,contextPayload:t})));return(0,a.createPortal)(j,w)}class L extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,g.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(C,this.props)}}x(L,"displayName","Legend"),x(L,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},87949:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},90170:(e,t,r)=>{r.d(t,{r:()=>k});var n=r(12115),a=r(46641),i=r(49972),l=r(59068),c=r(95932),o=r(73433),u=r(81971),s=r(2267);function d(e){var t=(0,u.j)();return(0,n.useEffect)(()=>{t((0,s.U)(e))},[t,e]),null}var h=r(75380),p=r(93389),y=r(78892),f=["width","height","layout"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var m={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},v=(0,n.forwardRef)(function(e,t){var r,a=(0,p.e)(e.categoricalChartProps,m),{width:u,height:s,layout:v}=a,b=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(a,f);if(!(0,y.F)(u)||!(0,y.F)(s))return null;var{chartName:O,defaultTooltipEventType:k,validateTooltipEventTypes:w,tooltipPayloadSearcher:j}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:O,defaultTooltipEventType:k,validateTooltipEventTypes:w,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!=(r=a.id)?r:O},n.createElement(l.TK,{chartData:a.data}),n.createElement(c.s,{width:u,height:s,layout:v,margin:a.margin}),n.createElement(o.p,{accessibilityLayer:a.accessibilityLayer,barCategoryGap:a.barCategoryGap,maxBarSize:a.maxBarSize,stackOffset:a.stackOffset,barGap:a.barGap,barSize:a.barSize,syncId:a.syncId,syncMethod:a.syncMethod,className:a.className}),n.createElement(d,{cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius}),n.createElement(h.L,g({width:u,height:s},b,{ref:t})))}),b=["item"],O={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},k=(0,n.forwardRef)((e,t)=>{var r=(0,p.e)(e,O);return n.createElement(v,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:a.uN,categoricalChartProps:r,ref:t})})},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);