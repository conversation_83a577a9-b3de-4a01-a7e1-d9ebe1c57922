(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[587],{12535:(e,s,t)=>{Promise.resolve().then(t.bind(t,93233))},24944:(e,s,t)=>{"use strict";t.d(s,{k:()=>n});var a=t(95155),r=t(12115),i=t(55863),l=t(59434);let n=r.forwardRef((e,s)=>{let{className:t,value:r,...n}=e;return(0,a.jsx)(i.bL,{ref:s,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:(0,a.jsx)(i.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});n.displayName=i.bL.displayName},93233:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(95155),r=t(12115),i=t(17859),l=t(40646),n=t(14186),c=t(1243),d=t(57434),m=t(42148),o=t(69803),x=t(81586),u=t(5040),h=t(17580),p=t(4516),j=t(69037),g=t(69074),N=t(91788),f=t(84616),v=t(12318),y=t(47924),w=t(28883),b=t(92657),A=t(13717),T=t(81497),k=t(30285),S=t(62523),C=t(66695),D=t(26126),P=t(92262),q=t(91394),E=t(24944);let M=[{id:1,name:"Alex Thompson",position:"Senior Software Engineer",department:"Engineering",startDate:"2024-02-01",status:"in-progress",progress:75,completedTasks:9,totalTasks:12,avatar:"/avatars/alex.jpg",email:"<EMAIL>",phone:"+****************",manager:"Sarah Johnson",buddy:"Mike Chen"},{id:2,name:"Maria Garcia",position:"Product Manager",department:"Product",startDate:"2024-02-05",status:"pending",progress:25,completedTasks:3,totalTasks:12,avatar:"/avatars/maria.jpg",email:"<EMAIL>",phone:"+****************",manager:"David Wilson",buddy:"Emily Davis"},{id:3,name:"James Liu",position:"UX Designer",department:"Design",startDate:"2024-01-28",status:"completed",progress:100,completedTasks:12,totalTasks:12,avatar:"/avatars/james.jpg",email:"<EMAIL>",phone:"+****************",manager:"Lisa Park",buddy:"Tom Anderson"}],W=[{id:1,title:"Complete HR Documentation",category:"Documentation",required:!0,estimatedTime:"30 min"},{id:2,title:"IT Setup & Equipment Assignment",category:"IT Setup",required:!0,estimatedTime:"45 min"},{id:3,title:"Security Badge & Access Cards",category:"Security",required:!0,estimatedTime:"15 min"},{id:4,title:"Benefits Enrollment",category:"Benefits",required:!0,estimatedTime:"60 min"},{id:5,title:"Company Orientation Session",category:"Training",required:!0,estimatedTime:"2 hours"},{id:6,title:"Department Introduction",category:"Team",required:!0,estimatedTime:"1 hour"},{id:7,title:"Buddy System Assignment",category:"Team",required:!0,estimatedTime:"30 min"},{id:8,title:"Workspace Setup",category:"Workspace",required:!0,estimatedTime:"30 min"},{id:9,title:"Manager 1:1 Meeting",category:"Management",required:!0,estimatedTime:"45 min"},{id:10,title:"Company Policies Review",category:"Training",required:!0,estimatedTime:"45 min"},{id:11,title:"Role-Specific Training",category:"Training",required:!0,estimatedTime:"4 hours"},{id:12,title:"30-Day Check-in",category:"Follow-up",required:!0,estimatedTime:"30 min"}],L={totalNewHires:M.length,inProgress:M.filter(e=>"in-progress"===e.status).length,completed:M.filter(e=>"completed"===e.status).length,pending:M.filter(e=>"pending"===e.status).length,avgCompletionTime:14,avgSatisfactionScore:4.6};function Z(){let[e,s]=(0,r.useState)(""),[t,Z]=(0,r.useState)("All"),[z,B]=(0,r.useState)("All"),[$,H]=(0,r.useState)("overview"),R=M.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.position.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),r="All"===t||s.status===t,i="All"===z||s.department===z;return a&&r&&i}),I=e=>{switch(e){case"completed":return"success";case"in-progress":return"warning";default:return"secondary"}},O=e=>{switch(e){case"completed":return(0,a.jsx)(l.A,{className:"h-4 w-4"});case"in-progress":default:return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"pending":return(0,a.jsx)(c.A,{className:"h-4 w-4"})}},_=e=>{switch(e){case"Documentation":default:return(0,a.jsx)(d.A,{className:"h-4 w-4"});case"IT Setup":return(0,a.jsx)(m.A,{className:"h-4 w-4"});case"Security":return(0,a.jsx)(o.A,{className:"h-4 w-4"});case"Benefits":return(0,a.jsx)(x.A,{className:"h-4 w-4"});case"Training":return(0,a.jsx)(u.A,{className:"h-4 w-4"});case"Team":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"Workspace":return(0,a.jsx)(p.A,{className:"h-4 w-4"});case"Management":return(0,a.jsx)(j.A,{className:"h-4 w-4"});case"Follow-up":return(0,a.jsx)(g.A,{className:"h-4 w-4"})}};return(0,a.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,a.jsx)(P.Y,{title:"Onboarding Management",subtitle:"Managing onboarding for ".concat(L.totalNewHires," new hires"),actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,a.jsxs)(k.$,{size:"sm",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Add New Hire"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.totalNewHires}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total New Hires"})]})]})})}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.inProgress}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"In Progress"})]})]})})}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.completed}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completed"})]})]})})}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.pending}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.avgCompletionTime}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days"})]})]})})}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:L.avgSatisfactionScore}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Satisfaction"})]})]})})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.$,{variant:"overview"===$?"default":"outline",size:"sm",onClick:()=>H("overview"),children:"Overview"}),(0,a.jsx)(k.$,{variant:"tasks"===$?"default":"outline",size:"sm",onClick:()=>H("tasks"),children:"Tasks"}),(0,a.jsx)(k.$,{variant:"schedule"===$?"default":"outline",size:"sm",onClick:()=>H("schedule"),children:"Schedule"})]}),(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(S.p,{placeholder:"Search by name, position, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>Z(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,a.jsx)("option",{value:"completed",children:"Completed"})]}),(0,a.jsxs)("select",{value:z,onChange:e=>B(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Departments"}),(0,a.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,a.jsx)("option",{value:"Product",children:"Product"}),(0,a.jsx)("option",{value:"Design",children:"Design"})]})]})]})})}),"overview"===$?(0,a.jsx)(C.Zp,{children:(0,a.jsx)(C.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"New Hire"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Start Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Progress"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Manager"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:R.map((e,s)=>(0,a.jsxs)(i.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(q.eu,{className:"h-10 w-10",children:[(0,a.jsx)(q.BK,{src:e.avatar,alt:e.name}),(0,a.jsx)(q.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,a.jsx)(w.A,{className:"h-3 w-3 mr-1"}),e.email]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.position}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:new Date(e.startDate).toLocaleDateString()}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{children:[e.completedTasks,"/",e.totalTasks," tasks"]}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)(E.k,{value:e.progress,className:"w-20"})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)(D.E,{variant:I(e.status),className:"flex items-center space-x-1 w-fit",children:[O(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1).replace("-"," ")})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.manager}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Buddy: ",e.buddy]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(k.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsx)(k.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(k.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(T.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"tasks"===$?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:W.map((e,s)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},children:(0,a.jsxs)(C.Zp,{children:[(0,a.jsx)(C.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[_(e.category),(0,a.jsxs)("div",{children:[(0,a.jsx)(C.ZB,{className:"text-sm",children:e.title}),(0,a.jsx)(C.BT,{className:"text-xs",children:e.category})]})]}),e.required&&(0,a.jsx)(D.E,{variant:"destructive",className:"text-xs",children:"Required"})]})}),(0,a.jsx)(C.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Est. Time:"}),(0,a.jsx)("span",{className:"font-medium",children:e.estimatedTime})]})})]})},e.id))}):(0,a.jsxs)(C.Zp,{children:[(0,a.jsxs)(C.aR,{children:[(0,a.jsx)(C.ZB,{children:"Onboarding Schedule"}),(0,a.jsx)(C.BT,{children:"Upcoming onboarding activities and milestones"})]}),(0,a.jsx)(C.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"New Hire Orientation"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 5, 2024 at 9:00 AM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Maria Garcia"})]}),(0,a.jsx)(k.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Department Introduction"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 6, 2024 at 2:00 PM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Alex Thompson"})]}),(0,a.jsx)(k.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"30-Day Check-in"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 28, 2024 at 10:00 AM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: James Liu"})]}),(0,a.jsx)(k.$,{variant:"outline",size:"sm",children:"View Details"})]})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[706,352,289,728,262,441,684,358],()=>s(12535)),_N_E=e.O()}]);