(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[631],{29136:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>L});var i=a(95155),n=a(12115),c=a(17859),t=a(71007),l=a(23861),r=a(75525),o=a(33127),d=a(48136),m=a(54213),x=a(53904),h=a(4229),p=a(29869),u=a(62525),j=a(28883),f=a(46767),N=a(57434),y=a(69074),v=a(69803),g=a(78749),b=a(92657),w=a(30285),k=a(62523),A=a(66695),C=a(26126),E=a(92262),S=a(91394),P=a(4884),R=a(59434);let M=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,i.jsx)(P.bL,{className:(0,R.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...n,ref:s,children:(0,i.jsx)(P.zi,{className:(0,R.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});M.displayName=P.bL.displayName;let T={profile:{firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",department:"Engineering",position:"Senior Software Engineer",avatar:"/avatars/john.jpg",timezone:"America/New_York",language:"English"},notifications:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,weeklyReports:!0,systemAlerts:!0,leaveApprovals:!0,payrollUpdates:!1,announcementUpdates:!0},security:{twoFactorEnabled:!0,sessionTimeout:30,passwordLastChanged:"2024-01-15",loginHistory:!0,deviceManagement:!0},appearance:{theme:"system",compactMode:!1,showAvatars:!0,animationsEnabled:!0,sidebarCollapsed:!1}},D={general:{companyName:"PeopleNest Inc.",companyEmail:"<EMAIL>",companyPhone:"+****************",address:"123 Business Ave, Suite 100, City, State 12345",website:"https://www.peoplenest.com",timezone:"America/New_York",fiscalYearStart:"January"},hrPolicies:{workingHours:"9:00 AM - 5:00 PM",workingDays:"Monday - Friday",leavePolicyEnabled:!0,overtimeEnabled:!0,remoteWorkEnabled:!0,flexibleHoursEnabled:!0},payroll:{payFrequency:"Bi-weekly",payrollCurrency:"USD",taxCalculationEnabled:!0,benefitsEnabled:!0,bonusEnabled:!0}};function L(){let[e,s]=(0,n.useState)("profile"),[a,P]=(0,n.useState)(!1),[R,L]=(0,n.useState)(T),[z,B]=(0,n.useState)(D),_=[{id:"profile",label:"Profile",icon:t.A},{id:"notifications",label:"Notifications",icon:l.A},{id:"security",label:"Security",icon:r.A},{id:"appearance",label:"Appearance",icon:o.A},{id:"company",label:"Company",icon:d.A},{id:"system",label:"System",icon:m.A}],Z=(e,s)=>{L(a=>({...a,notifications:{...a.notifications,[e]:s}}))},F=(e,s)=>{L(a=>({...a,appearance:{...a.appearance,[e]:s}}))};return(0,i.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,i.jsx)(E.Y,{title:"Settings",subtitle:"Manage your account, preferences, and system configuration",actions:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)(w.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Reset to Default"]}),(0,i.jsxs)(w.$,{size:"sm",children:[(0,i.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})}),(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,i.jsx)("div",{className:"lg:w-64",children:(0,i.jsx)(A.Zp,{children:(0,i.jsx)(A.Wu,{className:"p-4",children:(0,i.jsx)("nav",{className:"space-y-2",children:_.map(a=>{let n=a.icon;return(0,i.jsxs)("button",{onClick:()=>s(a.id),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(e===a.id?"bg-primary text-primary-foreground":"hover:bg-muted text-muted-foreground"),children:[(0,i.jsx)(n,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:a.label})]},a.id)})})})})}),(0,i.jsxs)("div",{className:"flex-1",children:["profile"===e&&(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,i.jsxs)(A.Zp,{children:[(0,i.jsxs)(A.aR,{children:[(0,i.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(t.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Profile Information"})]}),(0,i.jsx)(A.BT,{children:"Update your personal information and contact details"})]}),(0,i.jsxs)(A.Wu,{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,i.jsxs)(S.eu,{className:"h-20 w-20",children:[(0,i.jsx)(S.BK,{src:R.profile.avatar,alt:"Profile"}),(0,i.jsxs)(S.q5,{className:"text-lg",children:[R.profile.firstName[0],R.profile.lastName[0]]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(w.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Change Photo"]}),(0,i.jsxs)(w.$,{variant:"ghost",size:"sm",className:"text-destructive",children:[(0,i.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Remove Photo"]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"First Name"}),(0,i.jsx)(k.p,{value:R.profile.firstName})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Last Name"}),(0,i.jsx)(k.p,{value:R.profile.lastName})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,i.jsx)(k.p,{value:R.profile.email,type:"email"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,i.jsx)(k.p,{value:R.profile.phone})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Department"}),(0,i.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,i.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,i.jsx)("option",{value:"Product",children:"Product"}),(0,i.jsx)("option",{value:"Design",children:"Design"}),(0,i.jsx)("option",{value:"Sales",children:"Sales"}),(0,i.jsx)("option",{value:"Marketing",children:"Marketing"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Position"}),(0,i.jsx)(k.p,{value:R.profile.position})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Timezone"}),(0,i.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,i.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,i.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,i.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,i.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Language"}),(0,i.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,i.jsx)("option",{value:"English",children:"English"}),(0,i.jsx)("option",{value:"Spanish",children:"Spanish"}),(0,i.jsx)("option",{value:"French",children:"French"}),(0,i.jsx)("option",{value:"German",children:"German"})]})]})]})]})]})}),"notifications"===e&&(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,i.jsxs)(A.Zp,{children:[(0,i.jsxs)(A.aR,{children:[(0,i.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(l.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Notification Preferences"})]}),(0,i.jsx)(A.BT,{children:"Choose how you want to be notified about important updates"})]}),(0,i.jsx)(A.Wu,{className:"space-y-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"Email Notifications"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),(0,i.jsx)(M,{checked:R.notifications.emailNotifications,onCheckedChange:e=>Z("emailNotifications",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"Push Notifications"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive browser push notifications"})]}),(0,i.jsx)(M,{checked:R.notifications.pushNotifications,onCheckedChange:e=>Z("pushNotifications",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"SMS Notifications"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via text message"})]}),(0,i.jsx)(M,{checked:R.notifications.smsNotifications,onCheckedChange:e=>Z("smsNotifications",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"Weekly Reports"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive weekly summary reports"})]}),(0,i.jsx)(M,{checked:R.notifications.weeklyReports,onCheckedChange:e=>Z("weeklyReports",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(r.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"System Alerts"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Important system and security alerts"})]}),(0,i.jsx)(M,{checked:R.notifications.systemAlerts,onCheckedChange:e=>Z("systemAlerts",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"Leave Approvals"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Notifications for leave requests and approvals"})]}),(0,i.jsx)(M,{checked:R.notifications.leaveApprovals,onCheckedChange:e=>Z("leaveApprovals",e)})]})]})})]})}),"security"===e&&(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,i.jsxs)(A.Zp,{children:[(0,i.jsxs)(A.aR,{children:[(0,i.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(r.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Security Settings"})]}),(0,i.jsx)(A.BT,{children:"Manage your account security and privacy settings"})]}),(0,i.jsx)(A.Wu,{className:"space-y-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,i.jsx)("span",{className:"font-medium",children:"Two-Factor Authentication"}),(0,i.jsx)(C.E,{variant:"success",className:"text-xs",children:"Enabled"})]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,i.jsx)(M,{checked:R.security.twoFactorEnabled})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Change Password"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(k.p,{type:a?"text":"password",placeholder:"Current password"}),(0,i.jsx)(w.$,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6",onClick:()=>P(!a),children:a?(0,i.jsx)(g.A,{className:"h-4 w-4"}):(0,i.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,i.jsx)(k.p,{type:"password",placeholder:"New password"}),(0,i.jsx)(k.p,{type:"password",placeholder:"Confirm new password"}),(0,i.jsx)(w.$,{size:"sm",children:"Update Password"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Session Timeout"}),(0,i.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,i.jsx)("option",{value:"15",children:"15 minutes"}),(0,i.jsx)("option",{value:"30",children:"30 minutes"}),(0,i.jsx)("option",{value:"60",children:"1 hour"}),(0,i.jsx)("option",{value:"240",children:"4 hours"}),(0,i.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,i.jsx)("div",{className:"space-y-2",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Password Last Changed"}),(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(R.security.passwordLastChanged).toLocaleDateString()})]})}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("span",{className:"font-medium",children:"Login History"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Keep track of account access"})]}),(0,i.jsx)(M,{checked:R.security.loginHistory})]})]})})]})}),"appearance"===e&&(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,i.jsxs)(A.Zp,{children:[(0,i.jsxs)(A.aR,{children:[(0,i.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(o.A,{className:"h-5 w-5"}),(0,i.jsx)("span",{children:"Appearance Settings"})]}),(0,i.jsx)(A.BT,{children:"Customize the look and feel of your interface"})]}),(0,i.jsx)(A.Wu,{className:"space-y-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Theme"}),(0,i.jsxs)("select",{value:R.appearance.theme,onChange:e=>F("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,i.jsx)("option",{value:"light",children:"Light"}),(0,i.jsx)("option",{value:"dark",children:"Dark"}),(0,i.jsx)("option",{value:"system",children:"System"})]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("span",{className:"font-medium",children:"Compact Mode"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use smaller spacing and elements"})]}),(0,i.jsx)(M,{checked:R.appearance.compactMode,onCheckedChange:e=>F("compactMode",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("span",{className:"font-medium",children:"Show Avatars"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display user profile pictures"})]}),(0,i.jsx)(M,{checked:R.appearance.showAvatars,onCheckedChange:e=>F("showAvatars",e)})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("span",{className:"font-medium",children:"Animations"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable smooth transitions and animations"})]}),(0,i.jsx)(M,{checked:R.appearance.animationsEnabled,onCheckedChange:e=>F("animationsEnabled",e)})]})]})})]})})]})]})]})}},47931:(e,s,a)=>{Promise.resolve().then(a.bind(a,29136))}},e=>{var s=s=>e(e.s=s);e.O(0,[706,352,289,598,262,441,684,358],()=>s(47931)),_N_E=e.O()}]);