exports.id=557,exports.ids=[557],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{Ee:()=>i,cn:()=>s,vv:()=>o});var n=r(49384),a=r(82348);function s(...e){return(0,a.QP)((0,n.$)(e))}function o(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function i(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},12872:()=>{},19731:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},26024:()=>{},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(60687),a=r(43210),s=r(8730),o=r(24224),i=r(4780);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,loading:o=!1,leftIcon:d,rightIcon:c,children:m,disabled:u,...p},f)=>{let b=a?s.DX:"button";return(0,n.jsxs)(b,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:f,disabled:u||o,...p,children:[o&&(0,n.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!o&&d&&(0,n.jsx)("span",{className:"mr-2",children:d}),m,!o&&c&&(0,n.jsx)("span",{className:"ml-2",children:c})]})});d.displayName="Button"},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>m,ZB:()=>d,Zp:()=>i,aR:()=>l});var n=r(60687),a=r(43210),s=r(4780);let o=(0,r(24224).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),i=a.forwardRef(({className:e,variant:t,padding:r,hover:a=!1,...i},l)=>(0,n.jsx)("div",{ref:l,className:(0,s.cn)(o({variant:t,padding:r}),a&&"transition-shadow hover:shadow-md",e),...i}));i.displayName="Card";let l=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let m=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));m.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},61135:()=>{},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},82939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687),a=r(43210),s=r(4780);let o=(0,r(24224).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef(({className:e,type:t,variant:r,size:i,leftIcon:l,rightIcon:d,error:c,label:m,helperText:u,id:p,...f},b)=>{let g=p||a.useId(),x=!!c;return(0,n.jsxs)("div",{className:"w-full",children:[m&&(0,n.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-foreground mb-1",children:m}),(0,n.jsxs)("div",{className:"relative",children:[l&&(0,n.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:l}),(0,n.jsx)("input",{type:t,className:(0,s.cn)(o({variant:x?"error":r,size:i,className:e}),l&&"pl-10",d&&"pr-10"),ref:b,id:g,...f}),d&&(0,n.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:d})]}),(c||u)&&(0,n.jsx)("p",{className:(0,s.cn)("mt-1 text-xs",x?"text-destructive":"text-muted-foreground"),children:c||u})]})});i.displayName="Input"},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(37413),a=r(58745),s=r.n(a),o=r(64066),i=r.n(o);r(61135);let l={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",themeColor:"#3b82f6",viewport:{width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover"},appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}};function d({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/icons/icon-192x192.png"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"PeopleNest"}),(0,n.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"application-name",content:"PeopleNest HRMS"}),(0,n.jsx)("meta",{name:"msapplication-TileColor",content:"#3b82f6"}),(0,n.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"}),(0,n.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"})]}),(0,n.jsxs)("body",{className:`${s().variable} ${i().variable} font-sans antialiased touch-manipulation`,children:[e,(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(60687);r(43210);var a=r(24224),s=r(4780);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i({className:e,variant:t,size:r,icon:a,removable:i,onRemove:l,children:d,...c}){return(0,n.jsxs)("div",{className:(0,s.cn)(o({variant:t,size:r}),e),...c,children:[a&&(0,n.jsx)("span",{className:"mr-1",children:a}),d,i&&(0,n.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:l,children:(0,n.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}}};