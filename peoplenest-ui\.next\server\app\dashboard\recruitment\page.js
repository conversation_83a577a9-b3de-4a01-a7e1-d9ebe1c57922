(()=>{var e={};e.id=376,e.ids=[376],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11102:(e,s,t)=>{Promise.resolve().then(t.bind(t,70032))},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15342:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\recruitment\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23026:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},57800:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64246:(e,s,t)=>{Promise.resolve().then(t.bind(t,15342))},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65594:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["dashboard",{children:["recruitment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15342)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\recruitment\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/recruitment/page",pathname:"/dashboard/recruitment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70032:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var a=t(60687),r=t(43210),i=t(50371),n=t(5336),l=t(58887),c=t(48730),d=t(35071),o=t(31158),x=t(96474),p=t(41312),m=t(57800),u=t(40228),h=t(23026),j=t(99270),f=t(97992),g=t(64398),v=t(13861),N=t(63143),y=t(29523),w=t(89667),A=t(44493),b=t(96834),P=t(74456),k=t(32584);let C=[{id:1,name:"Alex Thompson",position:"Senior Software Engineer",department:"Engineering",status:"interview",stage:"Technical Interview",appliedDate:"2024-01-15",experience:"5 years",location:"San Francisco, CA",rating:4.8,avatar:"/avatars/alex.jpg",skills:["React","Node.js","TypeScript","AWS"],education:"MS Computer Science"},{id:2,name:"Sarah Chen",position:"Product Manager",department:"Product",status:"offer",stage:"Offer Extended",appliedDate:"2024-01-10",experience:"7 years",location:"New York, NY",rating:4.9,avatar:"/avatars/sarah-chen.jpg",skills:["Product Strategy","Analytics","Agile","Leadership"],education:"MBA, BS Engineering"},{id:3,name:"Michael Rodriguez",position:"UX Designer",department:"Design",status:"screening",stage:"Phone Screening",appliedDate:"2024-01-20",experience:"4 years",location:"Austin, TX",rating:4.6,avatar:"/avatars/michael.jpg",skills:["Figma","User Research","Prototyping","Design Systems"],education:"BFA Design"},{id:4,name:"Emily Johnson",position:"Data Scientist",department:"Analytics",status:"rejected",stage:"Final Interview",appliedDate:"2024-01-05",experience:"3 years",location:"Seattle, WA",rating:4.2,avatar:"/avatars/emily-j.jpg",skills:["Python","Machine Learning","SQL","Statistics"],education:"PhD Data Science"}],S={totalCandidates:C.length,activePositions:8,interviewsScheduled:12,offersExtended:3,newApplications:15,avgTimeToHire:18},D=[{id:1,title:"Senior Software Engineer",department:"Engineering",location:"San Francisco, CA",type:"Full-time",applicants:24,posted:"2024-01-10",status:"active"},{id:2,title:"Product Manager",department:"Product",location:"New York, NY",type:"Full-time",applicants:18,posted:"2024-01-08",status:"active"},{id:3,title:"UX Designer",department:"Design",location:"Remote",type:"Full-time",applicants:31,posted:"2024-01-12",status:"active"}];function E(){let[e,s]=(0,r.useState)(""),[t,E]=(0,r.useState)("All"),[z,M]=(0,r.useState)("All"),[_,q]=(0,r.useState)("candidates"),L=C.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.position.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),r="All"===t||s.status===t,i="All"===z||s.department===z;return a&&r&&i}),R=e=>{switch(e){case"offer":return"success";case"interview":return"warning";case"screening":default:return"secondary";case"rejected":return"destructive"}},T=e=>{switch(e){case"offer":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"interview":return(0,a.jsx)(l.A,{className:"h-4 w-4"});case"screening":default:return(0,a.jsx)(c.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(d.A,{className:"h-4 w-4"})}};return(0,a.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,a.jsx)(P.Y,{title:"Recruitment Management",subtitle:`Managing ${S.totalCandidates} candidates across ${S.activePositions} open positions`,actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(y.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,a.jsxs)(y.$,{size:"sm",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"New Position"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.totalCandidates}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Candidates"})]})]})})}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.activePositions}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Open Positions"})]})]})})}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.interviewsScheduled}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Interviews Scheduled"})]})]})})}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.offersExtended}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Offers Extended"})]})]})})}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.newApplications}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"New Applications"})]})]})})}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:S.avgTimeToHire}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days to Hire"})]})]})})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.$,{variant:"candidates"===_?"default":"outline",size:"sm",onClick:()=>q("candidates"),children:"Candidates"}),(0,a.jsx)(y.$,{variant:"positions"===_?"default":"outline",size:"sm",onClick:()=>q("positions"),children:"Job Openings"})]}),(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(w.p,{placeholder:"Search by candidate name, position, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>E(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Status"}),(0,a.jsx)("option",{value:"screening",children:"Screening"}),(0,a.jsx)("option",{value:"interview",children:"Interview"}),(0,a.jsx)("option",{value:"offer",children:"Offer"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,a.jsxs)("select",{value:z,onChange:e=>M(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Departments"}),(0,a.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,a.jsx)("option",{value:"Product",children:"Product"}),(0,a.jsx)("option",{value:"Design",children:"Design"}),(0,a.jsx)("option",{value:"Analytics",children:"Analytics"})]})]})]})})}),"candidates"===_?(0,a.jsx)(A.Zp,{children:(0,a.jsx)(A.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Candidate"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Rating"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Experience"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Applied"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:L.map((e,s)=>(0,a.jsxs)(i.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(k.eu,{className:"h-10 w-10",children:[(0,a.jsx)(k.BK,{src:e.avatar,alt:e.name}),(0,a.jsx)(k.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,a.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e.location]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.position}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)(b.E,{variant:R(e.status),className:"flex items-center space-x-1 w-fit",children:[T(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.stage})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)("span",{className:"font-medium",children:e.rating})]})}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.experience}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:new Date(e.appliedDate).toLocaleDateString()}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:D.map((e,s)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,a.jsxs)(A.Zp,{children:[(0,a.jsx)(A.aR,{children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(A.ZB,{className:"text-lg",children:e.title}),(0,a.jsxs)(A.BT,{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:e.department}),(0,a.jsx)("span",{children:"•"}),(0,a.jsx)("span",{children:e.location})]})]}),(0,a.jsx)(b.E,{variant:"secondary",children:e.type})]})}),(0,a.jsx)(A.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Applicants"}),(0,a.jsx)("span",{className:"font-medium",children:e.applicants})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Posted"}),(0,a.jsx)("span",{className:"font-medium",children:new Date(e.posted).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"View"]}),(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})]})})]})},e.id))})]})}},79551:e=>{"use strict";e.exports=require("url")},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,912,486,722,658,557,188],()=>t(65594));module.exports=a})();