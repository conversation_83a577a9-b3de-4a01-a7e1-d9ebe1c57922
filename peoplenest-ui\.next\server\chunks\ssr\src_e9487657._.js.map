{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        error: \"border-red-500 focus-visible:ring-red-500\",\n        success: \"border-green-500 focus-visible:ring-green-500\",\n      },\n      size: {\n        default: \"h-10\",\n        sm: \"h-9 px-2 text-xs\",\n        lg: \"h-11 px-4\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n  label?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type, \n    variant, \n    size, \n    leftIcon, \n    rightIcon, \n    error, \n    label, \n    helperText,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || React.useId()\n    const hasError = !!error\n    const finalVariant = hasError ? \"error\" : variant\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-foreground mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              inputVariants({ variant: finalVariant, size, className }),\n              leftIcon && \"pl-10\",\n              rightIcon && \"pr-10\"\n            )}\n            ref={ref}\n            id={inputId}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {(error || helperText) && (\n          <p className={cn(\n            \"mt-1 text-xs\",\n            hasError ? \"text-destructive\" : \"text-muted-foreground\"\n          )}>\n            {error || helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,2VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAaF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EACC,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAChC,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,eAAe,WAAW,UAAU;IAE1C,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;4BAAE,SAAS;4BAAc;4BAAM;wBAAU,IACvD,YAAY,SACZ,aAAa;wBAEf,KAAK;wBACL,IAAI;wBACH,GAAG,KAAK;;;;;;oBAEV,2BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,CAAC,SAAS,UAAU,mBACnB,8OAAC;gBAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,gBACA,WAAW,qBAAqB;0BAE/B,SAAS;;;;;;;;;;;;AAKpB;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md\",\n        outlined: \"border-2\",\n        ghost: \"border-transparent shadow-none\",\n      },\n      padding: {\n        none: \"\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {\n  hover?: boolean\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, hover = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        cardVariants({ variant, padding }),\n        hover && \"transition-shadow hover:shadow-md\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { \n  Card, \n  CardHeader, \n  CardFooter, \n  CardTitle, \n  CardDescription, \n  CardContent,\n  cardVariants \n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AASF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aAAa;YAAE;YAAS;QAAQ,IAChC,SAAS,qCACT;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport {\n  Search,\n  Bell,\n  MessageSquare,\n  Settings,\n  User,\n  LogOut,\n  Moon,\n  Sun,\n  Globe,\n  ChevronDown,\n  Plus,\n  Filter,\n  Download,\n  RefreshCw\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { cn } from \"@/lib/utils\"\n\ninterface HeaderProps {\n  title?: string\n  subtitle?: string\n  actions?: React.ReactNode\n  className?: string\n}\n\nexport function Header({ title, subtitle, actions, className }: HeaderProps) {\n  const [showNotifications, setShowNotifications] = useState(false)\n  const [showProfile, setShowProfile] = useState(false)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  const notifications = [\n    {\n      id: 1,\n      title: \"New Employee Onboarding\",\n      message: \"<PERSON> has completed her onboarding checklist\",\n      time: \"2 minutes ago\",\n      unread: true,\n      type: \"success\"\n    },\n    {\n      id: 2,\n      title: \"Leave Request Pending\",\n      message: \"Mike <PERSON> has requested 3 days of annual leave\",\n      time: \"1 hour ago\",\n      unread: true,\n      type: \"warning\"\n    },\n    {\n      id: 3,\n      title: \"Performance Review Due\",\n      message: \"5 performance reviews are due this week\",\n      time: \"3 hours ago\",\n      unread: false,\n      type: \"info\"\n    }\n  ]\n\n  const quickActions = [\n    { name: \"Add Employee\", icon: Plus, action: () => {} },\n    { name: \"Export Data\", icon: Download, action: () => {} },\n    { name: \"Refresh\", icon: RefreshCw, action: () => {} },\n    { name: \"Filter\", icon: Filter, action: () => {} },\n  ]\n\n  return (\n    <header className={cn(\n      \"sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm\",\n      className\n    )}>\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Left Section - Title and Breadcrumb */}\n        <div className=\"flex items-center space-x-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-xl font-semibold text-foreground\">{title}</h1>\n            )}\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-8\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              type=\"text\"\n              placeholder=\"Search employees, documents, or anything...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors\"\n            />\n          </div>\n        </div>\n\n        {/* Right Section - Actions and Profile */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Quick Actions */}\n          {actions && (\n            <div className=\"flex items-center space-x-2 mr-4\">\n              {actions}\n            </div>\n          )}\n\n          {/* Default Quick Actions */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {quickActions.map((action) => (\n              <Button\n                key={action.name}\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={action.action}\n                className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n                title={action.name}\n              >\n                <action.icon className=\"h-4 w-4\" />\n              </Button>\n            ))}\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"h-9 w-9 text-muted-foreground hover:text-foreground relative\"\n            >\n              <Bell className=\"h-4 w-4\" />\n              {notifications.some(n => n.unread) && (\n                <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full\" />\n              )}\n            </Button>\n\n            <AnimatePresence>\n              {showNotifications && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-80 z-50\"\n                >\n                  <Card className=\"shadow-lg border-border\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-border\">\n                        <div className=\"flex items-center justify-between\">\n                          <h3 className=\"font-semibold text-foreground\">Notifications</h3>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {notifications.filter(n => n.unread).length} new\n                          </Badge>\n                        </div>\n                      </div>\n                      <div className=\"max-h-80 overflow-y-auto\">\n                        {notifications.map((notification) => (\n                          <div\n                            key={notification.id}\n                            className={cn(\n                              \"p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors\",\n                              notification.unread && \"bg-primary/5\"\n                            )}\n                          >\n                            <div className=\"flex items-start space-x-3\">\n                              <div className={cn(\n                                \"w-2 h-2 rounded-full mt-2 flex-shrink-0\",\n                                notification.type === \"success\" && \"bg-green-500\",\n                                notification.type === \"warning\" && \"bg-yellow-500\",\n                                notification.type === \"info\" && \"bg-primary\"\n                              )} />\n                              <div className=\"flex-1 min-w-0\">\n                                <p className=\"text-sm font-medium text-foreground\">\n                                  {notification.title}\n                                </p>\n                                <p className=\"text-sm text-muted-foreground mt-1\">\n                                  {notification.message}\n                                </p>\n                                <p className=\"text-xs text-muted-foreground/70 mt-2\">\n                                  {notification.time}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                      <div className=\"p-3 border-t border-border\">\n                        <Button variant=\"ghost\" className=\"w-full text-sm\">\n                          View all notifications\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Messages */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n          >\n            <MessageSquare className=\"h-4 w-4\" />\n          </Button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              onClick={() => setShowProfile(!showProfile)}\n              className=\"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80\"\n            >\n              <Avatar className=\"h-7 w-7\">\n                <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                <AvatarFallback>JD</AvatarFallback>\n              </Avatar>\n              <ChevronDown className=\"h-3 w-3\" />\n            </Button>\n\n            <AnimatePresence>\n              {showProfile && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-56 z-50\"\n                >\n                  <Card className=\"shadow-lg border-gray-200\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-gray-200\">\n                        <div className=\"flex items-center space-x-3\">\n                          <Avatar className=\"h-10 w-10\">\n                            <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                            <AvatarFallback>JD</AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"font-medium text-foreground\">John Doe</p>\n                            <p className=\"text-sm text-muted-foreground\">HR Manager</p>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"py-2\">\n                        {[\n                          { icon: User, label: \"Profile\", href: \"/dashboard\" },\n                          { icon: Settings, label: \"Settings\", href: \"/dashboard\" },\n                          { icon: Moon, label: \"Dark Mode\", href: \"#\" },\n                          { icon: Globe, label: \"Language\", href: \"#\" },\n                        ].map((item) => (\n                          <button\n                            key={item.label}\n                            className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors\"\n                          >\n                            <item.icon className=\"h-4 w-4\" />\n                            <span>{item.label}</span>\n                          </button>\n                        ))}\n                        <hr className=\"my-2\" />\n                        <button className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors\">\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign out</span>\n                        </button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;AAkCO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAe;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAgB,MAAM,kMAAA,CAAA,OAAI;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAe,MAAM,0MAAA,CAAA,WAAQ;YAAE,QAAQ,KAAO;QAAE;QACxD;YAAE,MAAM;YAAW,MAAM,gNAAA,CAAA,YAAS;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAU,MAAM,sMAAA,CAAA,SAAM;YAAE,QAAQ,KAAO;QAAE;KAClD;IAED,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,kFACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BACE,uBACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;4BAExD,0BACC,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;8BAMpD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;wBAEZ,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,OAAO,MAAM;oCACtB,WAAU;oCACV,OAAO,OAAO,IAAI;8CAElB,cAAA,8OAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;mCAPlB,OAAO,IAAI;;;;;;;;;;sCAatB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,mBAC/B,8OAAC;4CAAK,WAAU;;;;;;;;;;;;8CAIpB,8OAAC,yLAAA,CAAA,kBAAe;8CACb,mCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgC;;;;;;8EAC9C,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAClC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;wEAAC;;;;;;;;;;;;;;;;;;kEAIlD,8OAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;gEAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8EACA,aAAa,MAAM,IAAI;0EAGzB,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2CACA,aAAa,IAAI,KAAK,aAAa,gBACnC,aAAa,IAAI,KAAK,aAAa,iBACnC,aAAa,IAAI,KAAK,UAAU;;;;;;sFAElC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;8FACV,aAAa,KAAK;;;;;;8FAErB,8OAAC;oFAAE,WAAU;8FACV,aAAa,OAAO;;;;;;8FAEvB,8OAAC;oFAAE,WAAU;8FACV,aAAa,IAAI;;;;;;;;;;;;;;;;;;+DArBnB,aAAa,EAAE;;;;;;;;;;kEA4B1B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYjE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAI3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAoB,KAAI;;;;;;8DACzC,8OAAC,kIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;sDAElB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAGzB,8OAAC,yLAAA,CAAA,kBAAe;8CACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,8OAAC,kIAAA,CAAA,cAAW;4EAAC,KAAI;4EAAoB,KAAI;;;;;;sFACzC,8OAAC,kIAAA,CAAA,iBAAc;sFAAC;;;;;;;;;;;;8EAElB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAA8B;;;;;;sFAC3C,8OAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;kEAInD,8OAAC;wDAAI,WAAU;;4DACZ;gEACC;oEAAE,MAAM,kMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAW,MAAM;gEAAa;gEACnD;oEAAE,MAAM,0MAAA,CAAA,WAAQ;oEAAE,OAAO;oEAAY,MAAM;gEAAa;gEACxD;oEAAE,MAAM,kMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAa,MAAM;gEAAI;gEAC5C;oEAAE,MAAM,oMAAA,CAAA,QAAK;oEAAE,OAAO;oEAAY,MAAM;gEAAI;6DAC7C,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC,KAAK,IAAI;4EAAC,WAAU;;;;;;sFACrB,8OAAC;sFAAM,KAAK,KAAK;;;;;;;mEAJZ,KAAK,KAAK;;;;;0EAOnB,8OAAC;gEAAG,WAAU;;;;;;0EACd,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/dashboard/documents/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  FileText,\n  Folder,\n  Download,\n  Upload,\n  Share2,\n  Eye,\n  Edit,\n  Trash2,\n  Search,\n  Filter,\n  Plus,\n  Star,\n  Clock,\n  Users,\n  Lock,\n  Unlock,\n  Image,\n  Video,\n  Music,\n  Archive,\n  File,\n  FolderOpen,\n  Calendar,\n  User\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Header } from \"@/components/layout/header\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\n\n// Mock documents data\nconst documents = [\n  {\n    id: 1,\n    name: \"Employee Handbook 2024\",\n    type: \"pdf\",\n    size: \"2.4 MB\",\n    folder: \"HR Policies\",\n    uploadedBy: \"<PERSON>\",\n    uploadedDate: \"2024-01-15\",\n    lastModified: \"2024-01-20\",\n    downloads: 156,\n    isStarred: true,\n    isShared: true,\n    permissions: \"read-only\",\n    tags: [\"handbook\", \"policies\", \"hr\"],\n    avatar: \"/avatars/sarah.jpg\"\n  },\n  {\n    id: 2,\n    name: \"Q4 Financial Report\",\n    type: \"xlsx\",\n    size: \"1.8 MB\",\n    folder: \"Finance\",\n    uploadedBy: \"Mike Chen\",\n    uploadedDate: \"2024-01-10\",\n    lastModified: \"2024-01-18\",\n    downloads: 89,\n    isStarred: false,\n    isShared: false,\n    permissions: \"restricted\",\n    tags: [\"finance\", \"quarterly\", \"report\"],\n    avatar: \"/avatars/mike.jpg\"\n  },\n  {\n    id: 3,\n    name: \"Project Timeline Template\",\n    type: \"pptx\",\n    size: \"3.2 MB\",\n    folder: \"Templates\",\n    uploadedBy: \"Emily Davis\",\n    uploadedDate: \"2024-01-12\",\n    lastModified: \"2024-01-12\",\n    downloads: 234,\n    isStarred: true,\n    isShared: true,\n    permissions: \"edit\",\n    tags: [\"template\", \"project\", \"timeline\"],\n    avatar: \"/avatars/emily.jpg\"\n  },\n  {\n    id: 4,\n    name: \"Security Guidelines\",\n    type: \"docx\",\n    size: \"856 KB\",\n    folder: \"IT Security\",\n    uploadedBy: \"David Wilson\",\n    uploadedDate: \"2024-01-08\",\n    lastModified: \"2024-01-16\",\n    downloads: 67,\n    isStarred: false,\n    isShared: true,\n    permissions: \"read-only\",\n    tags: [\"security\", \"guidelines\", \"it\"],\n    avatar: \"/avatars/david.jpg\"\n  },\n  {\n    id: 5,\n    name: \"Team Photo 2024\",\n    type: \"jpg\",\n    size: \"4.1 MB\",\n    folder: \"Company Events\",\n    uploadedBy: \"Lisa Park\",\n    uploadedDate: \"2024-01-22\",\n    lastModified: \"2024-01-22\",\n    downloads: 45,\n    isStarred: false,\n    isShared: true,\n    permissions: \"read-only\",\n    tags: [\"photo\", \"team\", \"event\"],\n    avatar: \"/avatars/lisa.jpg\"\n  }\n]\n\nconst folders = [\n  { name: \"HR Policies\", count: 12, lastModified: \"2024-01-20\" },\n  { name: \"Finance\", count: 8, lastModified: \"2024-01-18\" },\n  { name: \"Templates\", count: 15, lastModified: \"2024-01-16\" },\n  { name: \"IT Security\", count: 6, lastModified: \"2024-01-16\" },\n  { name: \"Company Events\", count: 23, lastModified: \"2024-01-22\" },\n  { name: \"Training Materials\", count: 18, lastModified: \"2024-01-14\" }\n]\n\nconst documentStats = {\n  totalDocuments: documents.length + 45, // Additional documents in folders\n  totalFolders: folders.length,\n  totalSize: \"156.7 GB\",\n  sharedDocuments: documents.filter(d => d.isShared).length + 28,\n  starredDocuments: documents.filter(d => d.isStarred).length + 12,\n  recentUploads: documents.filter(d => new Date(d.uploadedDate) > new Date('2024-01-15')).length + 8\n}\n\nexport default function DocumentsPage() {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedFolder, setSelectedFolder] = useState(\"All\")\n  const [selectedType, setSelectedType] = useState(\"All\")\n  const [selectedPermission, setSelectedPermission] = useState(\"All\")\n  const [viewMode, setViewMode] = useState<\"list\" | \"grid\" | \"folders\">(\"folders\")\n  const [showStarredOnly, setShowStarredOnly] = useState(false)\n\n  const filteredDocuments = documents.filter(doc => {\n    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||\n                         doc.uploadedBy.toLowerCase().includes(searchQuery.toLowerCase())\n    const matchesFolder = selectedFolder === \"All\" || doc.folder === selectedFolder\n    const matchesType = selectedType === \"All\" || doc.type === selectedType\n    const matchesPermission = selectedPermission === \"All\" || doc.permissions === selectedPermission\n    const matchesStarred = !showStarredOnly || doc.isStarred\n    \n    return matchesSearch && matchesFolder && matchesType && matchesPermission && matchesStarred\n  })\n\n  const getFileIcon = (type: string) => {\n    switch (type.toLowerCase()) {\n      case \"pdf\": return <FileText className=\"h-5 w-5 text-red-600\" />\n      case \"docx\": case \"doc\": return <FileText className=\"h-5 w-5 text-blue-600\" />\n      case \"xlsx\": case \"xls\": return <FileText className=\"h-5 w-5 text-green-600\" />\n      case \"pptx\": case \"ppt\": return <FileText className=\"h-5 w-5 text-orange-600\" />\n      case \"jpg\": case \"jpeg\": case \"png\": case \"gif\": return <Image className=\"h-5 w-5 text-purple-600\" />\n      case \"mp4\": case \"avi\": case \"mov\": return <Video className=\"h-5 w-5 text-pink-600\" />\n      case \"mp3\": case \"wav\": return <Music className=\"h-5 w-5 text-indigo-600\" />\n      case \"zip\": case \"rar\": return <Archive className=\"h-5 w-5 text-gray-600\" />\n      default: return <File className=\"h-5 w-5 text-gray-600\" />\n    }\n  }\n\n  const getPermissionColor = (permission: string) => {\n    switch (permission) {\n      case \"edit\": return \"success\"\n      case \"read-only\": return \"secondary\"\n      case \"restricted\": return \"destructive\"\n      default: return \"secondary\"\n    }\n  }\n\n  const getPermissionIcon = (permission: string) => {\n    switch (permission) {\n      case \"edit\": return <Unlock className=\"h-3 w-3\" />\n      case \"read-only\": return <Eye className=\"h-3 w-3\" />\n      case \"restricted\": return <Lock className=\"h-3 w-3\" />\n      default: return <Eye className=\"h-3 w-3\" />\n    }\n  }\n\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      <Header\n        title=\"Document Management\"\n        subtitle={`${documentStats.totalDocuments} documents across ${documentStats.totalFolders} folders`}\n        actions={\n          <div className=\"flex items-center space-x-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"w-4 h-4 mr-2\" />\n              Bulk Download\n            </Button>\n            <Button size=\"sm\">\n              <Upload className=\"w-4 h-4 mr-2\" />\n              Upload Files\n            </Button>\n          </div>\n        }\n      />\n\n      {/* Document Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <FileText className=\"h-8 w-8 text-blue-600 dark:text-blue-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.totalDocuments}</p>\n                <p className=\"text-sm text-muted-foreground\">Total Documents</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Folder className=\"h-8 w-8 text-yellow-600 dark:text-yellow-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.totalFolders}</p>\n                <p className=\"text-sm text-muted-foreground\">Folders</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Share2 className=\"h-8 w-8 text-green-600 dark:text-green-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.sharedDocuments}</p>\n                <p className=\"text-sm text-muted-foreground\">Shared</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Star className=\"h-8 w-8 text-orange-600 dark:text-orange-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.starredDocuments}</p>\n                <p className=\"text-sm text-muted-foreground\">Starred</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Clock className=\"h-8 w-8 text-purple-600 dark:text-purple-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.recentUploads}</p>\n                <p className=\"text-sm text-muted-foreground\">Recent</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Archive className=\"h-8 w-8 text-teal-600 dark:text-teal-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{documentStats.totalSize}</p>\n                <p className=\"text-sm text-muted-foreground\">Total Size</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* View Mode Tabs */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant={viewMode === \"folders\" ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setViewMode(\"folders\")}\n          >\n            <Folder className=\"w-4 h-4 mr-2\" />\n            Folders\n          </Button>\n          <Button\n            variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setViewMode(\"list\")}\n          >\n            List View\n          </Button>\n          <Button\n            variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setViewMode(\"grid\")}\n          >\n            Grid View\n          </Button>\n        </div>\n        <Button\n          variant={showStarredOnly ? \"default\" : \"outline\"}\n          size=\"sm\"\n          onClick={() => setShowStarredOnly(!showStarredOnly)}\n        >\n          <Star className=\"w-4 h-4 mr-2\" />\n          Starred Only\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Search documents by name, tags, or uploader...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedFolder}\n                onChange={(e) => setSelectedFolder(e.target.value)}\n                className=\"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n              >\n                <option value=\"All\">All Folders</option>\n                {folders.map(folder => (\n                  <option key={folder.name} value={folder.name}>{folder.name}</option>\n                ))}\n              </select>\n              <select\n                value={selectedType}\n                onChange={(e) => setSelectedType(e.target.value)}\n                className=\"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n              >\n                <option value=\"All\">All Types</option>\n                <option value=\"pdf\">PDF</option>\n                <option value=\"docx\">Word</option>\n                <option value=\"xlsx\">Excel</option>\n                <option value=\"pptx\">PowerPoint</option>\n                <option value=\"jpg\">Image</option>\n              </select>\n              <select\n                value={selectedPermission}\n                onChange={(e) => setSelectedPermission(e.target.value)}\n                className=\"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n              >\n                <option value=\"All\">All Permissions</option>\n                <option value=\"edit\">Edit</option>\n                <option value=\"read-only\">Read Only</option>\n                <option value=\"restricted\">Restricted</option>\n              </select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Content based on view mode */}\n      {viewMode === \"folders\" ? (\n        /* Folders View */\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {folders.map((folder, index) => (\n            <motion.div\n              key={folder.name}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <FolderOpen className=\"h-12 w-12 text-blue-600\" />\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-lg\">{folder.name}</h3>\n                      <p className=\"text-sm text-muted-foreground\">{folder.count} documents</p>\n                      <p className=\"text-xs text-muted-foreground\">Modified {new Date(folder.lastModified).toLocaleDateString()}</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      ) : viewMode === \"list\" ? (\n        /* List View */\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-muted/50 border-b border-border\">\n                  <tr>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Name</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Type</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Size</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Folder</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Uploaded By</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Modified</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Permissions</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredDocuments.map((doc, index) => (\n                    <motion.tr\n                      key={doc.id}\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: index * 0.05 }}\n                      className=\"border-b border-border hover:bg-muted/50\"\n                    >\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex items-center space-x-3\">\n                          {getFileIcon(doc.type)}\n                          <div>\n                            <p className=\"font-medium text-foreground flex items-center space-x-2\">\n                              <span>{doc.name}</span>\n                              {doc.isStarred && <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />}\n                              {doc.isShared && <Share2 className=\"h-4 w-4 text-blue-500\" />}\n                            </p>\n                            <div className=\"flex flex-wrap gap-1 mt-1\">\n                              {doc.tags.map(tag => (\n                                <Badge key={tag} variant=\"outline\" className=\"text-xs\">{tag}</Badge>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-6 text-muted-foreground uppercase text-sm\">{doc.type}</td>\n                      <td className=\"py-4 px-6 text-muted-foreground\">{doc.size}</td>\n                      <td className=\"py-4 px-6 text-muted-foreground\">{doc.folder}</td>\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex items-center space-x-2\">\n                          <Avatar className=\"h-6 w-6\">\n                            <AvatarImage src={doc.avatar} alt={doc.uploadedBy} />\n                            <AvatarFallback className=\"text-xs\">\n                              {doc.uploadedBy.split(' ').map(n => n[0]).join('')}\n                            </AvatarFallback>\n                          </Avatar>\n                          <span className=\"text-sm\">{doc.uploadedBy}</span>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-6 text-muted-foreground text-sm\">\n                        {new Date(doc.lastModified).toLocaleDateString()}\n                      </td>\n                      <td className=\"py-4 px-6\">\n                        <Badge variant={getPermissionColor(doc.permissions) as any} className=\"flex items-center space-x-1 w-fit\">\n                          {getPermissionIcon(doc.permissions)}\n                          <span className=\"ml-1\">{doc.permissions}</span>\n                        </Badge>\n                      </td>\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex space-x-2\">\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Share2 className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </td>\n                    </motion.tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </CardContent>\n        </Card>\n      ) : (\n        /* Grid View */\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {filteredDocuments.map((doc, index) => (\n            <motion.div\n              key={doc.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <Card className=\"hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-4\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      {getFileIcon(doc.type)}\n                      <span className=\"text-xs text-muted-foreground uppercase\">{doc.type}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      {doc.isStarred && <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />}\n                      {doc.isShared && <Share2 className=\"h-4 w-4 text-blue-500\" />}\n                    </div>\n                  </div>\n                  <h3 className=\"font-medium text-sm mb-2 line-clamp-2\">{doc.name}</h3>\n                  <div className=\"space-y-2 text-xs text-muted-foreground\">\n                    <div className=\"flex items-center justify-between\">\n                      <span>Size:</span>\n                      <span>{doc.size}</span>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span>Downloads:</span>\n                      <span>{doc.downloads}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Avatar className=\"h-4 w-4\">\n                        <AvatarImage src={doc.avatar} alt={doc.uploadedBy} />\n                        <AvatarFallback className=\"text-xs\">\n                          {doc.uploadedBy.split(' ').map(n => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <span className=\"truncate\">{doc.uploadedBy}</span>\n                    </div>\n                  </div>\n                  <div className=\"flex flex-wrap gap-1 mt-2\">\n                    {doc.tags.slice(0, 2).map(tag => (\n                      <Badge key={tag} variant=\"outline\" className=\"text-xs\">{tag}</Badge>\n                    ))}\n                    {doc.tags.length > 2 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">+{doc.tags.length - 2}</Badge>\n                    )}\n                  </div>\n                  <div className=\"flex items-center justify-between mt-3 pt-3 border-t\">\n                    <Badge variant={getPermissionColor(doc.permissions) as any} className=\"text-xs\">\n                      {doc.permissions}\n                    </Badge>\n                    <div className=\"flex space-x-1\">\n                      <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\">\n                        <Eye className=\"h-3 w-3\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\">\n                        <Download className=\"h-3 w-3\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\">\n                        <Share2 className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA;AACA;AACA;AACA;AACA;AACA;AAnCA;;;;;;;;;;;AAqCA,sBAAsB;AACtB,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,MAAM;YAAC;YAAY;YAAY;SAAK;QACpC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,MAAM;YAAC;YAAW;YAAa;SAAS;QACxC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,MAAM;YAAC;YAAY;YAAW;SAAW;QACzC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,MAAM;YAAC;YAAY;YAAc;SAAK;QACtC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,cAAc;QACd,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,MAAM;YAAC;YAAS;YAAQ;SAAQ;QAChC,QAAQ;IACV;CACD;AAED,MAAM,UAAU;IACd;QAAE,MAAM;QAAe,OAAO;QAAI,cAAc;IAAa;IAC7D;QAAE,MAAM;QAAW,OAAO;QAAG,cAAc;IAAa;IACxD;QAAE,MAAM;QAAa,OAAO;QAAI,cAAc;IAAa;IAC3D;QAAE,MAAM;QAAe,OAAO;QAAG,cAAc;IAAa;IAC5D;QAAE,MAAM;QAAkB,OAAO;QAAI,cAAc;IAAa;IAChE;QAAE,MAAM;QAAsB,OAAO;QAAI,cAAc;IAAa;CACrE;AAED,MAAM,gBAAgB;IACpB,gBAAgB,UAAU,MAAM,GAAG;IACnC,cAAc,QAAQ,MAAM;IAC5B,WAAW;IACX,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG;IAC5D,kBAAkB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM,GAAG;IAC9D,eAAe,UAAU,MAAM,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,YAAY,IAAI,IAAI,KAAK,eAAe,MAAM,GAAG;AACnG;AAEe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,QACvE,IAAI,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAClF,MAAM,gBAAgB,mBAAmB,SAAS,IAAI,MAAM,KAAK;QACjE,MAAM,cAAc,iBAAiB,SAAS,IAAI,IAAI,KAAK;QAC3D,MAAM,oBAAoB,uBAAuB,SAAS,IAAI,WAAW,KAAK;QAC9E,MAAM,iBAAiB,CAAC,mBAAmB,IAAI,SAAS;QAExD,OAAO,iBAAiB,iBAAiB,eAAe,qBAAqB;IAC/E;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ,KAAK,WAAW;YACtB,KAAK;gBAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACvC,KAAK;YAAQ,KAAK;gBAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACpD,KAAK;YAAQ,KAAK;gBAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACpD,KAAK;YAAQ,KAAK;gBAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YACpD,KAAK;YAAO,KAAK;YAAQ,KAAK;YAAO,KAAK;gBAAO,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACzE,KAAK;YAAO,KAAK;YAAO,KAAK;gBAAO,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC5D,KAAK;YAAO,KAAK;gBAAO,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAChD,KAAK;YAAO,KAAK;gBAAO,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAQ,qBAAO,8OAAC,4MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACtC,KAAK;gBAAa,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAc,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YAC1C;gBAAS,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBACL,OAAM;gBACN,UAAU,GAAG,cAAc,cAAc,CAAC,kBAAkB,EAAE,cAAc,YAAY,CAAC,QAAQ,CAAC;gBAClG,uBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGvC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,cAAc;;;;;;0DAC/E,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,YAAY;;;;;;0DAC7E,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,eAAe;;;;;;0DAChF,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,gBAAgB;;;;;;0DACjF,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,aAAa;;;;;;0DAC9E,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsC,cAAc,SAAS;;;;;;0DAC1E,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,YAAY,YAAY;gCAC9C,MAAK;gCACL,SAAS,IAAM,YAAY;;kDAE3B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,SAAS,YAAY;gCAC3C,MAAK;gCACL,SAAS,IAAM,YAAY;0CAC5B;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,SAAS,YAAY;gCAC3C,MAAK;gCACL,SAAS,IAAM,YAAY;0CAC5B;;;;;;;;;;;;kCAIH,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,kBAAkB,YAAY;wBACvC,MAAK;wBACL,SAAS,IAAM,mBAAmB,CAAC;;0CAEnC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;oDAAyB,OAAO,OAAO,IAAI;8DAAG,OAAO,IAAI;mDAA7C,OAAO,IAAI;;;;;;;;;;;kDAG5B,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;kDAEtB,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpC,aAAa,YACZ,gBAAgB,iBAChB,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB,OAAO,IAAI;;;;;;8DAClD,8OAAC;oDAAE,WAAU;;wDAAiC,OAAO,KAAK;wDAAC;;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;;wDAAgC;wDAAU,IAAI,KAAK,OAAO,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAZ1G,OAAO,IAAI;;;;;;;;;uBAoBpB,aAAa,SACf,aAAa,iBACb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;;;;;;;;;;;;8CAGpE,8OAAC;8CACE,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,OAAO,QAAQ;4CAAK;4CAClC,WAAU;;8DAEV,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,IAAI,IAAI;0EACrB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;0FAAM,IAAI,IAAI;;;;;;4EACd,IAAI,SAAS,kBAAI,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EACjC,IAAI,QAAQ,kBAAI,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;kFAErC,8OAAC;wEAAI,WAAU;kFACZ,IAAI,IAAI,CAAC,GAAG,CAAC,CAAA,oBACZ,8OAAC,iIAAA,CAAA,QAAK;gFAAW,SAAQ;gFAAU,WAAU;0FAAW;+EAA5C;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAMtB,8OAAC;oDAAG,WAAU;8DAAqD,IAAI,IAAI;;;;;;8DAC3E,8OAAC;oDAAG,WAAU;8DAAmC,IAAI,IAAI;;;;;;8DACzD,8OAAC;oDAAG,WAAU;8DAAmC,IAAI,MAAM;;;;;;8DAC3D,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,IAAI,MAAM;wEAAE,KAAK,IAAI,UAAU;;;;;;kFACjD,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;0EAGnD,8OAAC;gEAAK,WAAU;0EAAW,IAAI,UAAU;;;;;;;;;;;;;;;;;8DAG7C,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,IAAI,YAAY,EAAE,kBAAkB;;;;;;8DAEhD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,mBAAmB,IAAI,WAAW;wDAAU,WAAU;;4DACnE,kBAAkB,IAAI,WAAW;0EAClC,8OAAC;gEAAK,WAAU;0EAAQ,IAAI,WAAW;;;;;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAO,WAAU;0EAC5C,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAO,WAAU;0EAC5C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAO,WAAU;0EAC5C,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAO,WAAU;0EAC5C,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA1DjB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAsEzB,aAAa,iBACb,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,YAAY,IAAI,IAAI;kEACrB,8OAAC;wDAAK,WAAU;kEAA2C,IAAI,IAAI;;;;;;;;;;;;0DAErE,8OAAC;gDAAI,WAAU;;oDACZ,IAAI,SAAS,kBAAI,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACjC,IAAI,QAAQ,kBAAI,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAGvC,8OAAC;wCAAG,WAAU;kDAAyC,IAAI,IAAI;;;;;;kDAC/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAM,IAAI,IAAI;;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAM,IAAI,SAAS;;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEAAC,KAAK,IAAI,MAAM;gEAAE,KAAK,IAAI,UAAU;;;;;;0EACjD,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;kEAGnD,8OAAC;wDAAK,WAAU;kEAAY,IAAI,UAAU;;;;;;;;;;;;;;;;;;kDAG9C,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,oBACxB,8OAAC,iIAAA,CAAA,QAAK;oDAAW,SAAQ;oDAAU,WAAU;8DAAW;mDAA5C;;;;;4CAEb,IAAI,IAAI,CAAC,MAAM,GAAG,mBACjB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAU;oDAAE,IAAI,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;kDAGrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,mBAAmB,IAAI,WAAW;gDAAU,WAAU;0DACnE,IAAI,WAAW;;;;;;0DAElB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAzDvB,IAAI,EAAE;;;;;;;;;;;;;;;;AAqEzB", "debugId": null}}]}