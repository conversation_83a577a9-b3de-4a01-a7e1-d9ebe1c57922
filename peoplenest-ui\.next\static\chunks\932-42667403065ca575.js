"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[932],{3401:(e,r,t)=>{t.d(r,{E:()=>l});var n=t(12115),a=t(46641),i=t(82396),o=["axis","item"],l=(0,n.forwardRef)((e,r)=>n.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:r}))},56690:(e,r,t)=>{t.d(r,{y:()=>ex,L:()=>eg});var n=t(12115),a=t(52596),i=t(2348),o=t(11808),l=t(54811),s=t(36079),u=t(16377),c=t(70788),f=t(41643),p=t(39827),d=t(43597),y=t(67790),v=["x","y"];function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}function h(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function m(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?h(Object(t),!0).forEach(function(r){var n,a,i;n=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):h(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function g(e,r){var{x:t,y:n}=e,a=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],-1===r.indexOf(t)&&({}).propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,v),i=parseInt("".concat(t),10),o=parseInt("".concat(n),10),l=parseInt("".concat(r.height||a.height),10),s=parseInt("".concat(r.width||a.width),10);return m(m(m(m(m({},r),a),i?{x:i}:{}),o?{y:o}:{}),{},{height:l,width:s,name:r.name,radius:r.radius})}function x(e){return n.createElement(y.y,b({shapeType:"rectangle",propTransformer:g,activeClassName:"recharts-active-bar"},e))}var O=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(t,n)=>{if((0,u.Et)(e))return e;var a=(0,u.Et)(t)||(0,u.uy)(t);return a?e(t,n):(a||function(e,r){if(!e)throw Error("Invariant failed")}(!1),r)}},j=t(99129),P=t(56091),E=t(81971),z=t(22248),k=()=>{var e=(0,E.j)();return(0,n.useEffect)(()=>(e((0,z.lm)()),()=>{e((0,z.Ch)())})),null},w=t(39226),A=t(37195),S=t(97238),I=t(68924),M=t(14299),C=t(60356),D=t(56961),K=t(18478),B=t(78892);function N(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function T(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?N(Object(t),!0).forEach(function(r){var n,a,i;n=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):N(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var G=(e,r,t,n,a)=>a,R=(e,r,t)=>{var n=null!=t?t:e;if(!(0,u.uy)(n))return(0,u.F4)(n,r,0)},L=(0,I.Mz)([S.fz,M.ld,(e,r)=>r,(e,r,t)=>t,(e,r,t,n)=>n],(e,r,t,n,a)=>r.filter(r=>"horizontal"===e?r.xAxisId===t:r.yAxisId===n).filter(e=>e.isPanorama===a).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function _(e){return null!=e.stackId&&null!=e.dataKey}var F=(0,I.Mz)([L,K.x3,(e,r,t)=>"horizontal"===(0,S.fz)(e)?(0,M.BQ)(e,"xAxis",r):(0,M.BQ)(e,"yAxis",t)],(e,r,t)=>{var n=e.filter(_),a=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,r)=>(e[r.stackId]||(e[r.stackId]=[]),e[r.stackId].push(r),e),{})).map(e=>{var[n,a]=e;return{stackId:n,dataKeys:a.map(e=>e.dataKey),barSize:R(r,t,a[0].barSize)}}),...a.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:R(r,t,e.barSize)}))]}),W=(e,r,t,n)=>{var a,i;return"horizontal"===(0,S.fz)(e)?(a=(0,M.Gx)(e,"xAxis",r,n),i=(0,M.CR)(e,"xAxis",r,n)):(a=(0,M.Gx)(e,"yAxis",t,n),i=(0,M.CR)(e,"yAxis",t,n)),(0,p.Hj)(a,i)},H=(0,I.Mz)([F,K.JN,K._5,K.gY,(e,r,t,n,a)=>{var i,o,l,s,c=(0,S.fz)(e),f=(0,K.JN)(e),{maxBarSize:d}=a,y=(0,u.uy)(d)?f:d;return"horizontal"===c?(l=(0,M.Gx)(e,"xAxis",r,n),s=(0,M.CR)(e,"xAxis",r,n)):(l=(0,M.Gx)(e,"yAxis",t,n),s=(0,M.CR)(e,"yAxis",t,n)),null!=(i=null!=(o=(0,p.Hj)(l,s,!0))?o:y)?i:0},W,(e,r,t,n,a)=>a.maxBarSize],(e,r,t,n,a,i,o)=>{var l=function(e,r,t,n,a){var i,o=n.length;if(!(o<1)){var l=(0,u.F4)(e,t,0,!0),s=[];if((0,B.H)(n[0].barSize)){var c=!1,f=t/o,p=n.reduce((e,r)=>e+(r.barSize||0),0);(p+=(o-1)*l)>=t&&(p-=(o-1)*l,l=0),p>=t&&f>0&&(c=!0,f*=.9,p=o*f);var d={offset:((t-p)/2|0)-l,size:0};i=n.reduce((e,r)=>{var t,n=[...e,{stackId:r.stackId,dataKeys:r.dataKeys,position:{offset:d.offset+d.size+l,size:c?f:null!=(t=r.barSize)?t:0}}];return d=n[n.length-1].position,n},s)}else{var y=(0,u.F4)(r,t,0,!0);t-2*y-(o-1)*l<=0&&(l=0);var v=(t-2*y-(o-1)*l)/o;v>1&&(v>>=0);var b=(0,B.H)(a)?Math.min(v,a):v;i=n.reduce((e,r,t)=>[...e,{stackId:r.stackId,dataKeys:r.dataKeys,position:{offset:y+(v+l)*t+(v-b)/2,size:b}}],s)}return i}}(t,n,a!==i?a:i,e,(0,u.uy)(o)?r:o);return a!==i&&null!=l&&(l=l.map(e=>T(T({},e),{},{position:T(T({},e.position),{},{offset:e.position.offset-a/2})}))),l}),J=(0,I.Mz)([H,G],(e,r)=>{if(null!=e){var t=e.find(e=>e.stackId===r.stackId&&e.dataKeys.includes(r.dataKey));if(null!=t)return t.position}}),X=(0,I.Mz)([M.ld,G],(e,r)=>{if(e.some(e=>"bar"===e.type&&r.dataKey===e.dataKey&&r.stackId===e.stackId&&r.stackId===e.stackId))return r}),Q=(0,I.Mz)([(e,r,t,n)=>"horizontal"===(0,S.fz)(e)?(0,M.TC)(e,"yAxis",t,n):(0,M.TC)(e,"xAxis",r,n),G],(e,r)=>{if(!e||(null==r?void 0:r.dataKey)==null)return;var{stackId:t}=r;if(null!=t){var n=e[t];if(n){var{stackedData:a}=n;if(a)return a.find(e=>e.key===r.dataKey)}}}),U=(0,I.Mz)([D.GO,(e,r,t,n)=>(0,M.Gx)(e,"xAxis",r,n),(e,r,t,n)=>(0,M.Gx)(e,"yAxis",t,n),(e,r,t,n)=>(0,M.CR)(e,"xAxis",r,n),(e,r,t,n)=>(0,M.CR)(e,"yAxis",t,n),J,S.fz,C.HS,W,Q,X,(e,r,t,n,a,i)=>i],(e,r,t,n,a,i,o,l,s,u,c,f)=>{var p,{chartData:d,dataStartIndex:y,dataEndIndex:v}=l;if(null!=c&&null!=i&&("horizontal"===o||"vertical"===o)&&null!=r&&null!=t&&null!=n&&null!=a&&null!=s){var{data:b}=c;if(null!=(p=null!=b&&b.length>0?b:null==d?void 0:d.slice(y,v+1)))return eg({layout:o,barSettings:c,pos:i,bandSize:s,xAxis:r,yAxis:t,xAxisTicks:n,yAxisTicks:a,stackedData:u,dataStartIndex:y,displayedData:p,offset:e,cells:f})}}),$=t(71807),V=t(20215),Y=t(79020),Z=t(39426),q=t(93389),ee=t(74460),er=["onMouseEnter","onMouseLeave","onClick"],et=["value","background","tooltipPosition"],en=["onMouseEnter","onClick","onMouseLeave"];function ea(){return(ea=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(null,arguments)}function ei(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function eo(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ei(Object(t),!0).forEach(function(r){el(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ei(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function el(e,r,t){var n;return(r="symbol"==typeof(n=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(r,"string"))?n:n+"")in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function es(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==r.indexOf(n))continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],-1===r.indexOf(t)&&({}).propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}var eu=e=>{var{dataKey:r,name:t,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:r,type:a,color:n,value:(0,p.uM)(t,r),payload:e}]};function ec(e){var{dataKey:r,stroke:t,strokeWidth:n,fill:a,name:i,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:t,strokeWidth:n,fill:a,dataKey:r,nameKey:void 0,name:(0,p.uM)(i,r),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function ef(e){var r=(0,E.G)(V.A2),{data:t,dataKey:a,background:i,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:s,onClick:u}=o,f=es(o,er),p=(0,j.Cj)(l,a),y=(0,j.Pg)(s),v=(0,j.Ub)(u,a);if(!i||null==t)return null;var b=(0,c.J9)(i,!1);return n.createElement(n.Fragment,null,t.map((e,t)=>{var{value:o,background:l,tooltipPosition:s}=e,u=es(e,et);if(!l)return null;var c=p(e,t),h=y(e,t),m=v(e,t),g=eo(eo(eo(eo(eo({option:i,isActive:String(t)===r},u),{},{fill:"#eee"},l),b),(0,d.XC)(f,e,t)),{},{onMouseEnter:c,onMouseLeave:h,onClick:m,dataKey:a,index:t,className:"recharts-bar-background-rectangle"});return n.createElement(x,ea({key:"background-bar-".concat(t)},g))}))}function ep(e){var{data:r,props:t,showLabels:a}=e,o=(0,c.J9)(t,!1),{shape:l,dataKey:u,activeBar:f}=t,p=(0,E.G)(V.A2),y=(0,E.G)(V.Xb),{onMouseEnter:v,onClick:b,onMouseLeave:h}=t,m=es(t,en),g=(0,j.Cj)(v,u),O=(0,j.Pg)(h),P=(0,j.Ub)(b,u);return r?n.createElement(n.Fragment,null,r.map((e,r)=>{var t=f&&String(r)===p&&(null==y||u===y),a=eo(eo(eo({},o),e),{},{isActive:t,option:t?f:l,index:r,dataKey:u});return n.createElement(i.W,ea({className:"recharts-bar-rectangle"},(0,d.XC)(m,e,r),{onMouseEnter:g(e,r),onMouseLeave:O(e,r),onClick:P(e,r),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),n.createElement(x,a))}),a&&s.Z.renderCallByParent(t,r)):null}function ed(e){var{props:r,previousRectanglesRef:t}=e,{data:a,layout:o,isAnimationActive:l,animationBegin:s,animationDuration:c,animationEasing:f,onAnimationEnd:p,onAnimationStart:d}=r,y=t.current,v=(0,Z.n)(r,"recharts-bar-"),[b,h]=(0,n.useState)(!1),m=(0,n.useCallback)(()=>{"function"==typeof p&&p(),h(!1)},[p]),g=(0,n.useCallback)(()=>{"function"==typeof d&&d(),h(!0)},[d]);return n.createElement(ee.i,{begin:s,duration:c,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:g,key:v},e=>{var{t:l}=e,s=1===l?a:a.map((e,r)=>{var t=y&&y[r];if(t){var n=(0,u.Dj)(t.x,e.x),a=(0,u.Dj)(t.y,e.y),i=(0,u.Dj)(t.width,e.width),s=(0,u.Dj)(t.height,e.height);return eo(eo({},e),{},{x:n(l),y:a(l),width:i(l),height:s(l)})}if("horizontal"===o){var c=(0,u.Dj)(0,e.height)(l);return eo(eo({},e),{},{y:e.y+e.height-c,height:c})}var f=(0,u.Dj)(0,e.width)(l);return eo(eo({},e),{},{width:f})});return l>0&&(t.current=s),n.createElement(i.W,null,n.createElement(ep,{props:r,data:s,showLabels:!b}))})}function ey(e){var{data:r,isAnimationActive:t}=e,a=(0,n.useRef)(null);return t&&r&&r.length&&(null==a.current||a.current!==r)?n.createElement(ed,{previousRectanglesRef:a,props:e}):n.createElement(ep,{props:e,data:r,showLabels:!0})}var ev=(e,r)=>{var t=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:t,errorVal:(0,p.kr)(e,r)}};class eb extends n.PureComponent{render(){var{hide:e,data:r,dataKey:t,className:l,xAxisId:s,yAxisId:c,needClip:f,background:p,id:d,layout:y}=this.props;if(e)return null;var v=(0,a.$)("recharts-bar",l),b=(0,u.uy)(d)?this.id:d;return n.createElement(i.W,{className:v},f&&n.createElement("defs",null,n.createElement(A.Q,{clipPathId:b,xAxisId:s,yAxisId:c})),n.createElement(i.W,{className:"recharts-bar-rectangles",clipPath:f?"url(#clipPath-".concat(b,")"):null},n.createElement(ef,{data:r,dataKey:t,background:p,allOtherBarProps:this.props}),n.createElement(ey,this.props)),n.createElement(o._,{direction:"horizontal"===y?"y":"x"},this.props.children))}constructor(){super(...arguments),el(this,"id",(0,u.NF)("recharts-bar-"))}}var eh={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!f.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function em(e){var r,{xAxisId:t,yAxisId:a,hide:i,legendType:o,minPointSize:s,activeBar:u,animationBegin:f,animationDuration:d,animationEasing:y,isAnimationActive:v}=(0,q.e)(e,eh),{needClip:b}=(0,A.l)(t,a),h=(0,S.WX)(),m=(0,$.r)(),g=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:s,stackId:(0,p.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,s,e.stackId]),x=(0,c.aS)(e.children,l.f),O=(0,E.G)(e=>U(e,t,a,m,g,x));if("vertical"!==h&&"horizontal"!==h)return null;var j=null==O?void 0:O[0];return r=null==j||null==j.height||null==j.width?0:"vertical"===h?j.height/2:j.width/2,n.createElement(w.zk,{xAxisId:t,yAxisId:a,data:O,dataPointFormatter:ev,errorBarOffset:r},n.createElement(eb,ea({},e,{layout:h,needClip:b,data:O,xAxisId:t,yAxisId:a,hide:i,legendType:o,minPointSize:s,activeBar:u,animationBegin:f,animationDuration:d,animationEasing:y,isAnimationActive:v})))}function eg(e){var{layout:r,barSettings:{dataKey:t,minPointSize:n},pos:a,bandSize:i,xAxis:o,yAxis:l,xAxisTicks:s,yAxisTicks:c,stackedData:f,dataStartIndex:d,displayedData:y,offset:v,cells:b}=e,h="horizontal"===r?l:o,m=f?h.scale.domain():null,g=(0,p.DW)({numericAxis:h});return y.map((e,y)=>{f?x=(0,p._f)(f[d+y],m):Array.isArray(x=(0,p.kr)(e,t))||(x=[g,x]);var h=O(n,0)(x[1],y);if("horizontal"===r){var x,j,P,E,z,k,w,[A,S]=[l.scale(x[0]),l.scale(x[1])];j=(0,p.y2)({axis:o,ticks:s,bandSize:i,offset:a.offset,entry:e,index:y}),P=null!=(w=null!=S?S:A)?w:void 0,E=a.size;var I=A-S;if(z=(0,u.M8)(I)?0:I,k={x:j,y:v.top,width:E,height:v.height},Math.abs(h)>0&&Math.abs(z)<Math.abs(h)){var M=(0,u.sA)(z||h)*(Math.abs(h)-Math.abs(z));P-=M,z+=M}}else{var[C,D]=[o.scale(x[0]),o.scale(x[1])];if(j=C,P=(0,p.y2)({axis:l,ticks:c,bandSize:i,offset:a.offset,entry:e,index:y}),E=D-C,z=a.size,k={x:v.left,y:P,width:v.width,height:z},Math.abs(h)>0&&Math.abs(E)<Math.abs(h)){var K=(0,u.sA)(E||h)*(Math.abs(h)-Math.abs(E));E+=K}}return eo(eo({},e),{},{x:j,y:P,width:E,height:z,value:f?x:x[1],payload:e,background:k,tooltipPosition:{x:j+E/2,y:P+z/2}},b&&b[y]&&b[y].props)})}class ex extends n.PureComponent{render(){return n.createElement(w._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(k,null),n.createElement(Y.A,{legendPayload:eu(this.props)}),n.createElement(P.r,{fn:ec,args:this.props}),n.createElement(em,this.props))}}el(ex,"displayName","Bar"),el(ex,"defaultProps",eh)}}]);