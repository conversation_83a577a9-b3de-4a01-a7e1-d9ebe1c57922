"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[125],{1243:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},16785:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},33109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},38564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},64683:(e,t,r)=>{r.d(t,{s:()=>I});var n=r(12115),a=r(47650),l=r(15679),i=r(52596),c=r(20241),o=r.n(c),u=r(72790),s=r(9795),p=r(43597);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class y extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,a=32/6,l=32/3,i=e.inactive?r:e.color,c=null!=t?t:e.type;if("none"===c)return null;if("plainline"===c)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===c)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(l,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*l,",").concat(16,"\n            H").concat(32,"M").concat(2*l,",").concat(16,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(l,",").concat(16),className:"recharts-legend-icon"});if("rect"===c)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var o=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete o.legendIcon,n.cloneElement(e.legendIcon,o)}return n.createElement(s.i,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:c})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:a,inactiveColor:l,iconType:c,itemSorter:s}=this.props,d={x:0,y:0,width:32,height:32},f={display:"horizontal"===r?"inline-block":"block",marginRight:10},y={display:"inline-block",verticalAlign:"middle",marginRight:4};return(s?o()(e,s):e).map((e,r)=>{var o=e.formatter||a,s=(0,i.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var g=e.inactive?l:e.color,m=o?o(e.value,e,r):e.value;return n.createElement("li",h({className:s,style:f,key:"legend-item-".concat(r)},(0,p.XC)(this.props,e,r)),n.createElement(u.u,{width:t,height:t,viewBox:d,style:y,"aria-label":"".concat(m," legend icon")},this.renderIcon(e,c)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},m))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}f(y,"displayName","Legend"),f(y,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var g=r(16377),m=r(2494),v=r(81971),b=r(35803),O=r(77918),k=r(97238),j=r(32634),w=["contextPayload"];function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z(e){return e.value}function M(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)r=l[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,w),a=(0,m.s)(t,e.payloadUniqBy,z),l=A(A({},r),{},{payload:a});return n.isValidElement(e.content)?n.cloneElement(e.content,l):"function"==typeof e.content?n.createElement(e.content,l):n.createElement(y,l)}function N(e){var t=(0,v.j)();return(0,n.useEffect)(()=>{t((0,j.h1)(e))},[t,e]),null}function S(e){var t=(0,v.j)();return(0,n.useEffect)(()=>(t((0,j.hx)(e)),()=>{t((0,j.hx)({width:0,height:0}))}),[t,e]),null}function D(e){var t=(0,v.G)(b.g0),r=(0,l.M)(),i=(0,k.Kp)(),{width:c,height:o,wrapperStyle:u,portal:s}=e,[p,h]=(0,O.V)([t]),d=(0,k.yi)(),f=(0,k.rY)(),y=d-(i.left||0)-(i.right||0),g=I.getWidthOrHeight(e.layout,o,c,y),m=s?u:A(A({position:"absolute",width:(null==g?void 0:g.width)||c||"auto",height:(null==g?void 0:g.height)||o||"auto"},function(e,t,r,n,a,l){var i,c,{layout:o,align:u,verticalAlign:s}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(i="center"===u&&"vertical"===o?{left:((n||0)-l.width)/2}:"right"===u?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(c="middle"===s?{top:((a||0)-l.height)/2}:"bottom"===s?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),A(A({},i),c)}(u,e,i,d,f,p)),u),j=null!=s?s:r;if(null==j)return null;var w=n.createElement("div",{className:"recharts-legend-wrapper",style:m,ref:h},n.createElement(N,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),n.createElement(S,{width:p.width,height:p.height}),n.createElement(M,P({},e,g,{margin:i,chartWidth:d,chartHeight:f,contextPayload:t})));return(0,a.createPortal)(w,j)}class I extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,g.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(D,this.props)}}x(I,"displayName","Legend"),x(I,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},72713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);