
"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  LayoutDashboard,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  Settings,
  Menu,
  X,
  Home,
  Search,
  Bell,
  User
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { usePathname } from "next/navigation"

const mobileNavItems = [
  { name: "Dashboard", icon: LayoutDashboard, href: "/dashboard", badge: null },
  { name: "Employees", icon: Users, href: "/dashboard/employees", badge: "124" },
  { name: "Payroll", icon: DollarSign, href: "/dashboard/payroll", badge: null },
  { name: "Performance", icon: TrendingUp, href: "/dashboard/performance", badge: "5" },
  { name: "Leave", icon: Calendar, href: "/dashboard/leave", badge: "8" },
  { name: "Settings", icon: Settings, href: "/dashboard/settings", badge: null },
]

const quickActions = [
  { name: "Search", icon: Search, action: "search" },
  { name: "Notifications", icon: Bell, action: "notifications", badge: "3" },
  { name: "Profile", icon: User, action: "profile" },
]

interface MobileNavProps {
  className?: string
}

export function MobileNav({ className }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeItem, setActiveItem] = useState("/dashboard")
  const pathname = usePathname()

  useEffect(() => {
    setActiveItem(pathname)
  }, [pathname])

  useEffect(() => {
    // Close mobile nav when route changes
    setIsOpen(false)
  }, [pathname])

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'search':
        // Implement search functionality
        console.log('Search triggered')
        break
      case 'notifications':
        // Implement notifications
        console.log('Notifications triggered')
        break
      case 'profile':
        // Implement profile
        console.log('Profile triggered')
        break
    }
    setIsOpen(false)
  }

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(true)}
              className="h-10 w-10"
            >
              <Menu className="h-6 w-6" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-foreground">PeopleNest</h1>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {quickActions.map((action) => (
              <Button
                key={action.name}
                variant="ghost"
                size="icon"
                onClick={() => handleQuickAction(action.action)}
                className="h-10 w-10 relative"
              >
                <action.icon className="h-5 w-5" />
                {action.badge && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {action.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Navigation Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
              className="lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
            />
            
            {/* Navigation Panel */}
            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ type: "spring", damping: 30, stiffness: 300 }}
              className="lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-white shadow-xl"
            >
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                      <Home className="h-6 w-6 text-primary-foreground" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-foreground">PeopleNest</h2>
                      <p className="text-sm text-muted-foreground">HRMS Dashboard</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                    className="h-10 w-10"
                  >
                    <X className="h-6 w-6" />
                  </Button>
                </div>

                {/* Navigation Items */}
                <div className="flex-1 overflow-y-auto py-6">
                  <nav className="px-6 space-y-2">
                    {mobileNavItems.map((item, index) => {
                      const isActive = activeItem === item.href
                      return (
                        <motion.div
                          key={item.name}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Link
                            href={item.href}
                            onClick={() => setActiveItem(item.href)}
                            className={`
                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                              ${isActive
                                ? 'bg-primary/10 text-primary border border-primary/20'
                                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                              }
                            `}
                          >
                            <item.icon className={`h-5 w-5 ${isActive ? 'text-primary' : 'text-muted-foreground'}`} />
                            <span className="font-medium">{item.name}</span>
                            {item.badge && (
                              <Badge 
                                variant={isActive ? "default" : "secondary"} 
                                className="ml-auto"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </Link>
                        </motion.div>
                      )
                    })}
                  </nav>
                </div>

                {/* Footer */}
                <div className="p-6 border-t border-border">
                  <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                    <div className="w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">John Doe</p>
                      <p className="text-xs text-muted-foreground">HR Manager</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Bottom Navigation for Mobile */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2">
        <div className="flex items-center justify-around">
          {mobileNavItems.slice(0, 4).map((item) => {
            const isActive = activeItem === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setActiveItem(item.href)}
                className={`
                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative
                  ${isActive
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground'
                  }
                `}
              >
                <item.icon className="h-5 w-5" />
                <span className="text-xs font-medium">{item.name}</span>
                {item.badge && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {item.badge}
                  </Badge>
                )}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"
                  />
                )}
              </Link>
            )
          })}
        </div>
      </div>
    </>
  )
}

