(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[275],{24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>n});var t=a(95155),l=a(12115),r=a(55863),i=a(59434);let n=l.forwardRef((e,s)=>{let{className:a,value:l,...n}=e;return(0,t.jsx)(r.bL,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...n,children:(0,t.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});n.displayName=r.bL.displayName},34906:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eO});var t=a(95155),l=a(12115),r=a(17859),i=a(19145),n=a(53904),c=a(91788),d=a(84616),o=a(12486),m=a(57434),x=a(55670),h=a(54416),p=a(47924),u=a(66932),j=a(85213),f=a(95488),g=a(17580),y=a(14186),N=a(33109),v=a(54653),b=a(15968),w=a(5623),C=a(38564),k=a(28883),A=a(17576),S=a(4516),E=a(69074),R=a(55868),D=a(92657),I=a(13717),P=a(62525),F=a(51154),Z=a(30285),L=a(62523),B=a(66695),z=a(91394),T=a(8619),Y=a(58829),W=a(59434);function $(e){let{children:s,onClick:a,variant:i="primary",size:n="md",disabled:c=!1,className:d}=e,[o,m]=(0,l.useState)(!1),x=()=>{m(!0),"vibrate"in navigator&&navigator.vibrate(10)},h=()=>{m(!1),!c&&a&&a()};return(0,t.jsxs)(r.P.button,{className:(0,W.cn)("relative overflow-hidden rounded-lg font-medium transition-all duration-200 select-none",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70",ghost:"bg-transparent text-muted-foreground hover:bg-muted active:bg-muted/80"}[i],{sm:"px-4 py-2 text-sm min-h-[40px]",md:"px-6 py-3 text-base min-h-[48px]",lg:"px-8 py-4 text-lg min-h-[56px]"}[n],c&&"opacity-50 cursor-not-allowed",d),onTouchStart:x,onTouchEnd:h,onMouseDown:x,onMouseUp:h,onMouseLeave:()=>m(!1),whileTap:{scale:.98},disabled:c,children:[(0,t.jsx)(r.P.div,{className:"absolute inset-0 bg-white/20 rounded-lg",initial:{scale:0,opacity:0},animate:o?{scale:1,opacity:1}:{scale:0,opacity:0},transition:{duration:.2}}),(0,t.jsx)("span",{className:"relative z-10",children:s})]})}function O(e){let{children:s,onSwipeLeft:a,onSwipeRight:l,swipeThreshold:i=100,className:n}=e,c=(0,T.d)(0),d=(0,Y.G)(c,[-200,-100,0,100,200],[.5,.8,1,.8,.5]),o=(0,Y.G)(c,[-200,-100,0,100,200],["#ef4444","#f87171","#ffffff","#10b981","#059669"]);return(0,t.jsxs)(r.P.div,{className:(0,W.cn)("relative bg-white rounded-lg shadow-sm border border-gray-200",n),style:{x:c,opacity:d,backgroundColor:o},drag:"x",dragConstraints:{left:-200,right:200},dragElastic:.2,onDragEnd:(e,s)=>{let t=s.offset.x;t>i&&l?l():t<-i&&a&&a(),c.set(0)},whileDrag:{scale:1.02},children:[s,(0,t.jsx)(r.P.div,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white font-medium",style:{opacity:(0,Y.G)(c,[-200,-100,0],[1,.5,0])},children:"Delete"}),(0,t.jsx)(r.P.div,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white font-medium",style:{opacity:(0,Y.G)(c,[0,100,200],[0,.5,1])},children:"Archive"})]})}function _(e){let{children:s,onRefresh:a,refreshThreshold:i=80,className:n}=e,[c,d]=(0,l.useState)(!1),[o,m]=(0,l.useState)(0),x=(0,l.useRef)(null),h=async()=>{let e=x.current;if(e){if(delete e.dataset.startY,o>=i&&!c){d(!0);try{await a()}finally{d(!1)}}m(0)}},p=Math.min(o/i,1);return(0,t.jsxs)("div",{ref:x,className:(0,W.cn)("relative overflow-auto",n),onTouchStart:e=>{let s=x.current;if(s&&0===s.scrollTop){let a=e.touches[0];s.dataset.startY=a.clientY.toString()}},onTouchMove:e=>{let s=x.current;if(s&&s.dataset.startY&&0===s.scrollTop){let a=e.touches[0],t=parseInt(s.dataset.startY),l=Math.max(0,a.clientY-t);l>0&&(e.preventDefault(),m(Math.min(l,1.5*i)))}},onTouchEnd:h,children:[(0,t.jsxs)(r.P.div,{className:"absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 text-primary",style:{height:o},initial:{opacity:0},animate:{opacity:+(o>0)},children:[(0,t.jsx)(r.P.div,{className:"flex items-center space-x-2",animate:{rotate:c?360:180*p},transition:{duration:+!!c,repeat:c?1/0:0,ease:"linear"},children:(0,t.jsx)("div",{className:"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"})}),(0,t.jsx)("span",{className:"ml-2 text-sm font-medium",children:c?"Refreshing...":o>=i?"Release to refresh":"Pull to refresh"})]}),(0,t.jsx)(r.P.div,{style:{paddingTop:o},transition:{type:"spring",damping:20,stiffness:300},children:s})]})}function q(e){let{icon:s,onClick:a,position:l="bottom-right",className:i}=e;return(0,t.jsx)(r.P.button,{className:(0,W.cn)("fixed z-50 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center",{"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","bottom-center":"bottom-6 left-1/2 transform -translate-x-1/2"}[l],i),onClick:a,whileTap:{scale:.9},whileHover:{scale:1.1},initial:{scale:0},animate:{scale:1},transition:{type:"spring",damping:15,stiffness:300},children:s})}var M=a(15452);let U=M.bL;M.l9;let G=M.ZL;M.bm;let V=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(M.hJ,{ref:s,className:(0,W.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});V.displayName=M.hJ.displayName;let H=l.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(G,{children:[(0,t.jsx)(V,{}),(0,t.jsxs)(M.UC,{ref:s,className:(0,W.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r,children:[l,(0,t.jsxs)(M.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});H.displayName=M.UC.displayName;let J=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,W.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};J.displayName="DialogHeader";let K=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(M.hE,{ref:s,className:(0,W.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});K.displayName=M.hE.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(M.VY,{ref:s,className:(0,W.cn)("text-sm text-muted-foreground",a),...l})}).displayName=M.VY.displayName;var X=a(62177),Q=a(48778),ee=a(71153),es=a(71007),ea=a(4229),et=a(29869),el=a(19420),er=a(40968);let ei=(0,a(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),en=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(er.b,{ref:s,className:(0,W.cn)(ei(),a),...l})});en.displayName=er.b.displayName;var ec=a(22697),ed=a(66474),eo=a(47863),em=a(5196);let ex=ec.bL;ec.YJ;let eh=ec.WT,ep=l.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(ec.l9,{ref:s,className:(0,W.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...r,children:[l,(0,t.jsx)(ec.In,{asChild:!0,children:(0,t.jsx)(ed.A,{className:"h-4 w-4 opacity-50"})})]})});ep.displayName=ec.l9.displayName;let eu=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(ec.PP,{ref:s,className:(0,W.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,t.jsx)(eo.A,{className:"h-4 w-4"})})});eu.displayName=ec.PP.displayName;let ej=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(ec.wn,{ref:s,className:(0,W.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,t.jsx)(ed.A,{className:"h-4 w-4"})})});ej.displayName=ec.wn.displayName;let ef=l.forwardRef((e,s)=>{let{className:a,children:l,position:r="popper",...i}=e;return(0,t.jsx)(ec.ZL,{children:(0,t.jsxs)(ec.UC,{ref:s,className:(0,W.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...i,children:[(0,t.jsx)(eu,{}),(0,t.jsx)(ec.LM,{className:(0,W.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,t.jsx)(ej,{})]})})});ef.displayName=ec.UC.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(ec.JU,{ref:s,className:(0,W.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=ec.JU.displayName;let eg=l.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsxs)(ec.q7,{ref:s,className:(0,W.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(ec.VF,{children:(0,t.jsx)(em.A,{className:"h-4 w-4"})})}),(0,t.jsx)(ec.p4,{children:l})]})});eg.displayName=ec.q7.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(ec.wv,{ref:s,className:(0,W.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=ec.wv.displayName;var ey=a(26126),eN=a(56671);let ev=ee.Ik({firstName:ee.Yj().min(2,"First name must be at least 2 characters"),lastName:ee.Yj().min(2,"Last name must be at least 2 characters"),email:ee.Yj().email("Invalid email address"),personalEmail:ee.Yj().email("Invalid email address").optional().or(ee.eu("")),phone:ee.Yj().min(10,"Phone number must be at least 10 digits"),dateOfBirth:ee.Yj().optional(),gender:ee.k5(["male","female","other","prefer_not_to_say"]).optional(),address:ee.Ik({street:ee.Yj().optional(),city:ee.Yj().optional(),state:ee.Yj().optional(),zipCode:ee.Yj().optional(),country:ee.Yj().optional()}).optional(),emergencyContact:ee.Ik({name:ee.Yj().optional(),relationship:ee.Yj().optional(),phone:ee.Yj().optional(),email:ee.Yj().email().optional().or(ee.eu(""))}).optional(),departmentId:ee.Yj().min(1,"Department is required"),positionId:ee.Yj().min(1,"Position is required"),managerId:ee.Yj().optional(),employeeType:ee.k5(["full_time","part_time","contract","intern"]),hireDate:ee.Yj().min(1,"Hire date is required"),salary:ee.ai().min(0,"Salary must be positive").optional(),currency:ee.Yj().default("USD"),workLocation:ee.k5(["office","remote","hybrid"]),skills:ee.YO(ee.Yj()).optional(),certifications:ee.YO(ee.Ik({name:ee.Yj(),issuer:ee.Yj(),issueDate:ee.Yj().optional(),expiryDate:ee.Yj().optional()})).optional()});function eb(e){var s,a,i,n,c,d,o,m,x,p;let{employee:u,onSubmit:j,onCancel:f,isLoading:g=!1,departments:y=[],positions:N=[],managers:v=[]}=e,[b,w]=(0,l.useState)((null==u?void 0:u.departmentId)||""),[C,A]=(0,l.useState)((null==u?void 0:u.skills)||[]),[D,I]=(0,l.useState)(""),[P,T]=(0,l.useState)((null==u?void 0:u.certifications)||[]),[Y,W]=(0,l.useState)((null==u?void 0:u.avatar)||null),[$,O]=(0,l.useState)(null),{register:_,handleSubmit:q,formState:{errors:M,isSubmitting:U},setValue:G,watch:V,reset:H}=(0,X.mN)({resolver:(0,Q.u)(ev),defaultValues:u?{...u,address:u.address||{},emergencyContact:u.emergencyContact||{},skills:u.skills||[],certifications:u.certifications||[]}:{currency:"USD",workLocation:"office",employeeType:"full_time"}}),J=V("departmentId");(0,l.useEffect)(()=>{J&&w(J)},[J]);let K=N.filter(e=>e.departmentId===b),ee=v.filter(e=>e.departmentId===b),er=async e=>{try{let s={...e,skills:C,certifications:P,profileImage:$};await j(s),eN.oR.success(u?"Employee updated successfully":"Employee created successfully")}catch(e){eN.oR.error("Failed to save employee"),console.error("Form submission error:",e)}},ei=()=>{D.trim()&&!C.includes(D.trim())&&(A([...C,D.trim()]),I(""))},ec=e=>{A(C.filter(s=>s!==e))},ed=()=>{T([...P,{name:"",issuer:"",issueDate:"",expiryDate:""}])},eo=(e,s,a)=>{let t=[...P];t[e]={...t[e],[s]:a},T(t)},em=e=>{T(P.filter((s,a)=>a!==e))};return(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-4xl mx-auto",children:(0,t.jsxs)("form",{onSubmit:q(er),className:"space-y-6",children:[(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(B.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(es.A,{className:"h-5 w-5"}),u?"Edit Employee":"Add New Employee"]}),(0,t.jsx)(B.BT,{children:u?"Update employee information":"Enter employee details to create a new profile"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(Z.$,{type:"button",variant:"outline",onClick:f,disabled:U||g,children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,t.jsxs)(Z.$,{type:"submit",disabled:U||g,className:"min-w-[120px]",children:[U||g?(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),u?"Update":"Create"]})]})]})})}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Profile Photo"})}),(0,t.jsx)(B.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(z.eu,{className:"h-20 w-20",children:[(0,t.jsx)(z.BK,{src:Y||void 0}),(0,t.jsx)(z.q5,{children:(0,t.jsx)(es.A,{className:"h-8 w-8"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"profile-image",className:"cursor-pointer",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted",children:[(0,t.jsx)(et.A,{className:"h-4 w-4"}),"Upload Photo"]})}),(0,t.jsx)("input",{id:"profile-image",type:"file",accept:"image/*",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(a){O(a);let e=new FileReader;e.onload=e=>{var s;W(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(a)}},className:"hidden"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"JPG, PNG or GIF. Max size 5MB."})]})]})})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Personal Information"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"firstName",children:"First Name *"}),(0,t.jsx)(L.p,{id:"firstName",..._("firstName"),error:null==(s=M.firstName)?void 0:s.message,leftIcon:(0,t.jsx)(es.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"lastName",children:"Last Name *"}),(0,t.jsx)(L.p,{id:"lastName",..._("lastName"),error:null==(a=M.lastName)?void 0:a.message,leftIcon:(0,t.jsx)(es.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"email",children:"Work Email *"}),(0,t.jsx)(L.p,{id:"email",type:"email",..._("email"),error:null==(i=M.email)?void 0:i.message,leftIcon:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"personalEmail",children:"Personal Email"}),(0,t.jsx)(L.p,{id:"personalEmail",type:"email",..._("personalEmail"),error:null==(n=M.personalEmail)?void 0:n.message,leftIcon:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"phone",children:"Phone Number *"}),(0,t.jsx)(L.p,{id:"phone",..._("phone"),error:null==(c=M.phone)?void 0:c.message,leftIcon:(0,t.jsx)(el.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,t.jsx)(L.p,{id:"dateOfBirth",type:"date",..._("dateOfBirth"),error:null==(d=M.dateOfBirth)?void 0:d.message,leftIcon:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"gender",children:"Gender"}),(0,t.jsxs)(ex,{onValueChange:e=>G("gender",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select gender"})}),(0,t.jsxs)(ef,{children:[(0,t.jsx)(eg,{value:"male",children:"Male"}),(0,t.jsx)(eg,{value:"female",children:"Female"}),(0,t.jsx)(eg,{value:"other",children:"Other"}),(0,t.jsx)(eg,{value:"prefer_not_to_say",children:"Prefer not to say"})]})]})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Address Information"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.street",children:"Street Address"}),(0,t.jsx)(L.p,{id:"address.street",..._("address.street"),leftIcon:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.city",children:"City"}),(0,t.jsx)(L.p,{id:"address.city",..._("address.city")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.state",children:"State/Province"}),(0,t.jsx)(L.p,{id:"address.state",..._("address.state")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.zipCode",children:"ZIP/Postal Code"}),(0,t.jsx)(L.p,{id:"address.zipCode",..._("address.zipCode")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.country",children:"Country"}),(0,t.jsx)(L.p,{id:"address.country",..._("address.country")})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Emergency Contact"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.name",children:"Contact Name"}),(0,t.jsx)(L.p,{id:"emergencyContact.name",..._("emergencyContact.name"),leftIcon:(0,t.jsx)(es.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.relationship",children:"Relationship"}),(0,t.jsx)(L.p,{id:"emergencyContact.relationship",..._("emergencyContact.relationship")})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.phone",children:"Phone Number"}),(0,t.jsx)(L.p,{id:"emergencyContact.phone",..._("emergencyContact.phone"),leftIcon:(0,t.jsx)(el.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.email",children:"Email"}),(0,t.jsx)(L.p,{id:"emergencyContact.email",type:"email",..._("emergencyContact.email"),leftIcon:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Employment Information"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"departmentId",children:"Department *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("departmentId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select department"})}),(0,t.jsx)(ef,{children:y.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.name},e.id))})]}),M.departmentId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:M.departmentId.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"positionId",children:"Position *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("positionId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select position"})}),(0,t.jsx)(ef,{children:K.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.title},e.id))})]}),M.positionId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:M.positionId.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"managerId",children:"Manager"}),(0,t.jsxs)(ex,{onValueChange:e=>G("managerId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select manager"})}),(0,t.jsx)(ef,{children:ee.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.name},e.id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"employeeType",children:"Employment Type *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("employeeType",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select employment type"})}),(0,t.jsxs)(ef,{children:[(0,t.jsx)(eg,{value:"full_time",children:"Full Time"}),(0,t.jsx)(eg,{value:"part_time",children:"Part Time"}),(0,t.jsx)(eg,{value:"contract",children:"Contract"}),(0,t.jsx)(eg,{value:"intern",children:"Intern"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"hireDate",children:"Hire Date *"}),(0,t.jsx)(L.p,{id:"hireDate",type:"date",..._("hireDate"),error:null==(o=M.hireDate)?void 0:o.message,leftIcon:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"workLocation",children:"Work Location *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("workLocation",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select work location"})}),(0,t.jsxs)(ef,{children:[(0,t.jsx)(eg,{value:"office",children:"Office"}),(0,t.jsx)(eg,{value:"remote",children:"Remote"}),(0,t.jsx)(eg,{value:"hybrid",children:"Hybrid"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"salary",children:"Annual Salary"}),(0,t.jsx)(L.p,{id:"salary",type:"number",..._("salary",{valueAsNumber:!0}),error:null==(m=M.salary)?void 0:m.message,leftIcon:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Skills & Competencies"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(L.p,{placeholder:"Add a skill...",value:D,onChange:e=>I(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),ei())}),(0,t.jsx)(Z.$,{type:"button",onClick:ei,variant:"outline",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map((e,s)=>(0,t.jsxs)(ey.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)(h.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>ec(e)})]},s))})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(B.ZB,{className:"text-lg",children:"Certifications"}),(0,t.jsx)(Z.$,{type:"button",onClick:ed,variant:"outline",size:"sm",children:"Add Certification"})]})}),(0,t.jsx)(B.Wu,{className:"space-y-4",children:P.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"font-medium",children:["Certification ",s+1]}),(0,t.jsx)(Z.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>em(s),children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)(L.p,{placeholder:"Certification name",value:e.name,onChange:e=>eo(s,"name",e.target.value)}),(0,t.jsx)(L.p,{placeholder:"Issuing organization",value:e.issuer,onChange:e=>eo(s,"issuer",e.target.value)}),(0,t.jsx)(L.p,{type:"date",placeholder:"Issue date",value:e.issueDate,onChange:e=>eo(s,"issueDate",e.target.value)}),(0,t.jsx)(L.p,{type:"date",placeholder:"Expiry date",value:e.expiryDate,onChange:e=>eo(s,"expiryDate",e.target.value)})]})]},s))})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Address Information"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.street",children:"Street Address"}),(0,t.jsx)(L.p,{id:"address.street",..._("address.street"),leftIcon:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.city",children:"City"}),(0,t.jsx)(L.p,{id:"address.city",..._("address.city")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.state",children:"State/Province"}),(0,t.jsx)(L.p,{id:"address.state",..._("address.state")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.zipCode",children:"ZIP/Postal Code"}),(0,t.jsx)(L.p,{id:"address.zipCode",..._("address.zipCode")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"address.country",children:"Country"}),(0,t.jsx)(L.p,{id:"address.country",..._("address.country")})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Emergency Contact"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.name",children:"Contact Name"}),(0,t.jsx)(L.p,{id:"emergencyContact.name",..._("emergencyContact.name"),leftIcon:(0,t.jsx)(es.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.relationship",children:"Relationship"}),(0,t.jsx)(L.p,{id:"emergencyContact.relationship",..._("emergencyContact.relationship")})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.phone",children:"Phone Number"}),(0,t.jsx)(L.p,{id:"emergencyContact.phone",..._("emergencyContact.phone"),leftIcon:(0,t.jsx)(el.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"emergencyContact.email",children:"Email"}),(0,t.jsx)(L.p,{id:"emergencyContact.email",type:"email",..._("emergencyContact.email"),leftIcon:(0,t.jsx)(k.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Employment Information"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"departmentId",children:"Department *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("departmentId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select department"})}),(0,t.jsx)(ef,{children:y.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.name},e.id))})]}),M.departmentId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:M.departmentId.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"positionId",children:"Position *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("positionId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select position"})}),(0,t.jsx)(ef,{children:K.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.title},e.id))})]}),M.positionId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:M.positionId.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"managerId",children:"Manager"}),(0,t.jsxs)(ex,{onValueChange:e=>G("managerId",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select manager"})}),(0,t.jsx)(ef,{children:ee.map(e=>(0,t.jsx)(eg,{value:e.id,children:e.name},e.id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"employeeType",children:"Employment Type *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("employeeType",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select employment type"})}),(0,t.jsxs)(ef,{children:[(0,t.jsx)(eg,{value:"full_time",children:"Full Time"}),(0,t.jsx)(eg,{value:"part_time",children:"Part Time"}),(0,t.jsx)(eg,{value:"contract",children:"Contract"}),(0,t.jsx)(eg,{value:"intern",children:"Intern"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"hireDate",children:"Hire Date *"}),(0,t.jsx)(L.p,{id:"hireDate",type:"date",..._("hireDate"),error:null==(x=M.hireDate)?void 0:x.message,leftIcon:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"workLocation",children:"Work Location *"}),(0,t.jsxs)(ex,{onValueChange:e=>G("workLocation",e),children:[(0,t.jsx)(ep,{children:(0,t.jsx)(eh,{placeholder:"Select work location"})}),(0,t.jsxs)(ef,{children:[(0,t.jsx)(eg,{value:"office",children:"Office"}),(0,t.jsx)(eg,{value:"remote",children:"Remote"}),(0,t.jsx)(eg,{value:"hybrid",children:"Hybrid"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(en,{htmlFor:"salary",children:"Annual Salary"}),(0,t.jsx)(L.p,{id:"salary",type:"number",..._("salary",{valueAsNumber:!0}),error:null==(p=M.salary)?void 0:p.message,leftIcon:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsx)(B.ZB,{className:"text-lg",children:"Skills & Competencies"})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(L.p,{placeholder:"Add a skill...",value:D,onChange:e=>I(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),ei())}),(0,t.jsx)(Z.$,{type:"button",onClick:ei,variant:"outline",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map((e,s)=>(0,t.jsxs)(ey.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)(h.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>ec(e)})]},s))})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(B.ZB,{className:"text-lg",children:"Certifications"}),(0,t.jsx)(Z.$,{type:"button",onClick:ed,variant:"outline",size:"sm",children:"Add Certification"})]})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[P.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"font-medium",children:["Certification ",s+1]}),(0,t.jsx)(Z.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>em(s),children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)(L.p,{placeholder:"Certification name",value:e.name,onChange:e=>eo(s,"name",e.target.value)}),(0,t.jsx)(L.p,{placeholder:"Issuing organization",value:e.issuer,onChange:e=>eo(s,"issuer",e.target.value)}),(0,t.jsx)(L.p,{type:"date",placeholder:"Issue date",value:e.issueDate,onChange:e=>eo(s,"issueDate",e.target.value)}),(0,t.jsx)(L.p,{type:"date",placeholder:"Expiry date",value:e.expiryDate,onChange:e=>eo(s,"expiryDate",e.target.value)})]})]},s)),0===P.length&&(0,t.jsx)("p",{className:"text-muted-foreground text-center py-4",children:"No certifications added yet."})]})]})]})})}var ew=a(85339),eC=a(36683),ek=a(16785),eA=a(69037),eS=a(75525),eE=a(30064);let eR=eE.bL,eD=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eE.B8,{ref:s,className:(0,W.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});eD.displayName=eE.B8.displayName;let eI=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eE.l9,{ref:s,className:(0,W.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});eI.displayName=eE.l9.displayName;let eP=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eE.UC,{ref:s,className:(0,W.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});eP.displayName=eE.UC.displayName;var eF=a(24944);let eZ=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001/api";class eL{async makeRequest(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=localStorage.getItem("authToken"),t=await fetch("".concat(eZ).concat(e),{headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)},...s.headers},...s});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP error! status: ".concat(t.status))}return{data:await t.json()}}catch(e){return console.error("API request failed:",e),{error:e instanceof Error?e.message:"An unexpected error occurred"}}}async getEmployees(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&""!==t&&s.append(a,t.toString())});let a=s.toString();return this.makeRequest("/employees".concat(a?"?".concat(a):""))}async getEmployeeById(e){return this.makeRequest("/employees/".concat(e))}async getEmployeeProfile(e){return this.makeRequest("/employees/".concat(e,"/profile"))}async createEmployee(e){return this.makeRequest("/employees",{method:"POST",body:JSON.stringify(e)})}async updateEmployee(e,s){return this.makeRequest("/employees/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteEmployee(e){return this.makeRequest("/employees/".concat(e),{method:"DELETE"})}async uploadEmployeeAvatar(e,s){let a=new FormData;a.append("avatar",s);let t=localStorage.getItem("authToken");try{let s=await fetch("".concat(eZ,"/employees/").concat(e,"/avatar"),{method:"POST",headers:{...t&&{Authorization:"Bearer ".concat(t)}},body:a});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"HTTP error! status: ".concat(s.status))}return{data:await s.json()}}catch(e){return console.error("Avatar upload failed:",e),{error:e instanceof Error?e.message:"Avatar upload failed"}}}async bulkUpdateEmployees(e,s){return this.makeRequest("/employees/bulk-update",{method:"POST",body:JSON.stringify({employeeIds:e,updates:s})})}async exportEmployees(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&""!==t&&s.append(a,t.toString())});let a=s.toString();return this.makeRequest("/employees/export".concat(a?"?".concat(a):""))}async getDepartments(){return this.makeRequest("/departments")}async getPositions(){return this.makeRequest("/positions")}async getManagers(){return this.makeRequest("/employees/managers")}async searchEmployees(e){return this.getEmployees({search:e,limit:50}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}async getEmployeesByDepartment(e){return this.getEmployees({department:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}async getEmployeesByManager(e){return this.getEmployees({manager:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}}let eB=new eL,ez=function(e,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return e.error?(a&&eN.oR.error(e.error),null):(s&&e.data&&eN.oR.success(s),e.data||null)},eT=async(e,s)=>{try{return s(!0),await e()}catch(e){return console.error("Operation failed:",e),eN.oR.error(e instanceof Error?e.message:"Operation failed"),null}finally{s(!1)}};function eY(e){var s;let{employeeId:a,onEdit:i,onClose:n}=e,[d,o]=(0,l.useState)(null),[m,x]=(0,l.useState)(!0),[h,p]=(0,l.useState)("overview");(0,l.useEffect)(()=>{u()},[a]);let u=async()=>{x(!0);try{let e=await eB.getEmployeeProfile(a),s=ez(e,void 0,!0);s&&o(s)}catch(e){console.error("Failed to load employee profile:",e),eN.oR.error("Failed to load employee profile")}finally{x(!1)}},j=async()=>{try{eN.oR.success("Profile exported successfully")}catch(e){eN.oR.error("Failed to export profile")}},f=async()=>{try{eN.oR.success("Profile link copied to clipboard")}catch(e){eN.oR.error("Failed to share profile")}};if(m)return(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsx)(F.A,{className:"h-8 w-8 animate-spin"})});if(!d)return(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(ew.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Employee Not Found"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"The requested employee profile could not be loaded."})]})});let g="".concat(d.firstName," ").concat(d.lastName),N=d.recentReviews.length>0?d.recentReviews.reduce((e,s)=>e+s.rating,0)/d.recentReviews.length:0;return(0,t.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-6xl mx-auto space-y-6",children:[(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsxs)(z.eu,{className:"h-20 w-20",children:[(0,t.jsx)(z.BK,{src:d.avatar,alt:g}),(0,t.jsxs)(z.q5,{className:"text-lg",children:[d.firstName[0],d.lastName[0]]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:g}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground",children:d.positionId}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:d.departmentId})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:d.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(el.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:d.phone})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Joined ",new Date(d.hireDate).toLocaleDateString()]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ey.E,{variant:"active"===d.status?"default":"secondary",children:d.status}),(0,t.jsx)(ey.E,{variant:"outline",children:d.employeeType.replace("_"," ")}),(0,t.jsx)(ey.E,{variant:"outline",children:d.workLocation})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:f,children:[(0,t.jsx)(eC.A,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:j,children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Export"]}),i&&(0,t.jsxs)(Z.$,{size:"sm",onClick:i,children:[(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,t.jsx)(Z.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-yellow-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Performance"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[N.toFixed(1),"/5.0"]})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ek.A,{className:"h-5 w-5 text-blue-500 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Active Goals"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:d.activeGoals.length})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eA.A,{className:"h-5 w-5 text-green-500 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Skills"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:(null==(s=d.skills)?void 0:s.length)||0})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-purple-500 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tenure"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[Math.floor((Date.now()-new Date(d.hireDate).getTime())/31536e6)," years"]})]})]})})})]}),(0,t.jsxs)(eR,{value:h,onValueChange:p,children:[(0,t.jsxs)(eD,{className:"grid w-full grid-cols-5",children:[(0,t.jsx)(eI,{value:"overview",children:"Overview"}),(0,t.jsx)(eI,{value:"skills",children:"Skills"}),(0,t.jsx)(eI,{value:"performance",children:"Performance"}),(0,t.jsx)(eI,{value:"goals",children:"Goals"}),(0,t.jsx)(eI,{value:"documents",children:"Documents"})]}),(0,t.jsxs)(eP,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsxs)(B.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(es.A,{className:"h-5 w-5"}),"Personal Information"]})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Date of Birth"}),(0,t.jsx)("p",{className:"font-medium",children:d.dateOfBirth?new Date(d.dateOfBirth).toLocaleDateString():"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Gender"}),(0,t.jsx)("p",{className:"font-medium",children:d.gender||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Personal Email"}),(0,t.jsx)("p",{className:"font-medium",children:d.personalEmail||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:d.phone})]})]}),d.address&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Address"}),(0,t.jsx)("p",{className:"font-medium",children:[d.address.street,d.address.city,d.address.state,d.address.zipCode,d.address.country].filter(Boolean).join(", ")||"Not provided"})]})]})]}),(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsxs)(B.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),"Employment Details"]})}),(0,t.jsx)(B.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Employee ID"}),(0,t.jsx)("p",{className:"font-medium",children:d.id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Hire Date"}),(0,t.jsx)("p",{className:"font-medium",children:new Date(d.hireDate).toLocaleDateString()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Employment Type"}),(0,t.jsx)("p",{className:"font-medium",children:d.employeeType.replace("_"," ")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Work Location"}),(0,t.jsx)("p",{className:"font-medium",children:d.workLocation})]}),d.salary&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Salary"}),(0,t.jsxs)("p",{className:"font-medium",children:[d.currency," ",d.salary.toLocaleString()]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manager"}),(0,t.jsx)("p",{className:"font-medium",children:d.managerId||"Not assigned"})]})]})})]})]}),d.emergencyContact&&(0,t.jsxs)(B.Zp,{children:[(0,t.jsx)(B.aR,{children:(0,t.jsxs)(B.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(eS.A,{className:"h-5 w-5"}),"Emergency Contact"]})}),(0,t.jsx)(B.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Name"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.name||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Relationship"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.relationship||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.phone||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Email"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.email||"Not provided"})]})]})})]})]}),(0,t.jsx)(eP,{value:"skills",className:"space-y-6",children:(0,t.jsxs)(B.Zp,{children:[(0,t.jsxs)(B.aR,{children:[(0,t.jsx)(B.ZB,{children:"Skills & Competencies"}),(0,t.jsx)(B.BT,{children:"Employee skills and proficiency levels"})]}),(0,t.jsx)(B.Wu,{children:d.skills&&d.skills.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.skills.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(ey.E,{variant:"outline",children:e.category})]}),(0,t.jsx)(eF.k,{value:20*e.level,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Level ",e.level,"/5"]})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No skills recorded yet."})})]})}),(0,t.jsx)(eP,{value:"performance",className:"space-y-6",children:(0,t.jsxs)(B.Zp,{children:[(0,t.jsxs)(B.aR,{children:[(0,t.jsx)(B.ZB,{children:"Performance Reviews"}),(0,t.jsx)(B.BT,{children:"Recent performance evaluations and feedback"})]}),(0,t.jsx)(B.Wu,{children:d.recentReviews&&d.recentReviews.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.recentReviews.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"font-medium",children:["Review by ",e.reviewer]}),(0,t.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((s,a)=>(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(a<e.rating?"text-yellow-500 fill-current":"text-muted-foreground")},a))})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(e.reviewDate).toLocaleDateString()})]}),(0,t.jsx)("p",{className:"text-foreground",children:e.comments})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No performance reviews yet."})})]})}),(0,t.jsx)(eP,{value:"goals",className:"space-y-6",children:(0,t.jsxs)(B.Zp,{children:[(0,t.jsxs)(B.aR,{children:[(0,t.jsx)(B.ZB,{children:"Active Goals"}),(0,t.jsx)(B.BT,{children:"Current objectives and progress tracking"})]}),(0,t.jsx)(B.Wu,{children:d.activeGoals&&d.activeGoals.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.activeGoals.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)(ey.E,{variant:"on_track"===e.status?"default":"secondary",children:e.status.replace("_"," ")})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm mb-3",children:e.description}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)(eF.k,{value:e.progress,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No active goals set."})})]})}),(0,t.jsx)(eP,{value:"documents",className:"space-y-6",children:(0,t.jsxs)(B.Zp,{children:[(0,t.jsxs)(B.aR,{children:[(0,t.jsx)(B.ZB,{children:"Documents & Certifications"}),(0,t.jsx)(B.BT,{children:"Employee documents and certifications"})]}),(0,t.jsx)(B.Wu,{children:d.certifications&&d.certifications.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.certifications.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)(ey.E,{variant:"outline",children:e.issuer})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-muted-foreground",children:[e.issueDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Issued:"})," ",new Date(e.issueDate).toLocaleDateString()]}),e.expiryDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Expires:"})," ",new Date(e.expiryDate).toLocaleDateString()]})]})]},s))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No certifications recorded."})})]})})]})]})}var eW=a(92262);let e$=["All","active","on-leave","inactive"];function eO(){let[e,s]=(0,l.useState)([]),[a,T]=(0,l.useState)([]),[Y,W]=(0,l.useState)([]),[M,G]=(0,l.useState)([]),[V,X]=(0,l.useState)(!0),[Q,ee]=(0,l.useState)(0),[es,ea]=(0,l.useState)(1),[et,el]=(0,l.useState)(1),[er,ei]=(0,l.useState)(""),[en,ec]=(0,l.useState)("All"),[ed,eo]=(0,l.useState)("All"),[em,ex]=(0,l.useState)("grid"),[eh,ep]=(0,l.useState)("firstName"),[eu,ej]=(0,l.useState)("asc"),[ef,eg]=(0,l.useState)([]),[ev,ew]=(0,l.useState)(!1),[eC,ek]=(0,l.useState)("all"),[eA,eS]=(0,l.useState)([0,2e5]),[eE,eR]=(0,l.useState)(!1),[eD,eI]=(0,l.useState)(!1),[eP,eF]=(0,l.useState)(!1),[eZ,eL]=(0,l.useState)(!1),[eO,e_]=(0,l.useState)(null),[eq,eM]=(0,l.useState)(null);(0,l.useEffect)(()=>{eU()},[]),(0,l.useEffect)(()=>{eG()},[er,en,ed,eh,eu,es]);let eU=async()=>{X(!0);try{let[e,s,a]=await Promise.all([eB.getDepartments(),eB.getPositions(),eB.getManagers()]),t=ez(e),l=ez(s),r=ez(a);t&&T([{id:"All",name:"All Departments"},...t]),l&&W(l),r&&G(r),await eG()}catch(e){console.error("Failed to load initial data:",e),eN.oR.error("Failed to load employee data")}finally{X(!1)}},eG=async()=>{let e={page:es,limit:20,search:er||void 0,department:"All"!==en?en:void 0,status:"All"!==ed?ed.toLowerCase():void 0,sortBy:eh,sortOrder:eu},a=ez(await eB.getEmployees(e));a&&(s(a.employees),ee(a.total),el(a.totalPages))},eV=e.filter(e=>{let s=!er||e.name.toLowerCase().includes(er.toLowerCase())||e.email.toLowerCase().includes(er.toLowerCase())||e.department.toLowerCase().includes(er.toLowerCase()),a="All"===en||e.department===en,t="All"===ed||e.status===ed.toLowerCase(),l=!0;"high"===eC?l=e.performance>=4.5:"medium"===eC?l=e.performance>=3.5&&e.performance<4.5:"low"===eC&&(l=e.performance<3.5);let r=e.salary>=eA[0]&&e.salary<=eA[1];return s&&a&&t&&l&&r}).sort((e,s)=>{let a=0;switch(eh){case"name":a=e.name.localeCompare(s.name);break;case"department":a=e.department.localeCompare(s.department);break;case"performance":a=e.performance-s.performance;break;case"startDate":a=new Date(e.startDate).getTime()-new Date(s.startDate).getTime()}return"asc"===eu?a:-a}),eH=e=>{switch(e){case"active":return"success";case"on-leave":return"warning";case"inactive":return"destructive";default:return"secondary"}},eJ=e=>e>=4.5?"text-green-600 dark:text-green-400":e>=4?"text-blue-600 dark:text-blue-400":e>=3.5?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400",eK=e=>{eg(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},eX=e=>{eM(e),eF(!0)},eQ=()=>{eM(null),eF(!0)},e0=async e=>{confirm("Are you sure you want to delete this employee?")&&ez(await eB.deleteEmployee(e),"Employee deleted successfully")&&await eG()},e4=async e=>{try{switch(e){case"delete":confirm("Are you sure you want to delete ".concat(ef.length," employees?"))&&eN.oR.success("".concat(ef.length," employees deleted"));break;case"export":let s=await eB.exportEmployees({search:ef.join(",")});ez(s,"Export started");break;default:console.log("Performing ".concat(e," on employees:"),ef)}}catch(e){eN.oR.error("Bulk action failed")}finally{eg([]),eI(!1)}},e2=e=>{eh===e?ej("asc"===eu?"desc":"asc"):(ep(e),ej("asc"))},e1=async()=>{await eT(eG,eR)},e5=e=>{e0(e)},e6=e=>{eN.oR.info("Archive functionality coming soon")},e3=async e=>{try{if(eq){let s=await eB.updateEmployee(eq.id,e);ez(s,"Employee updated successfully")&&(eF(!1),eM(null),await eG())}else{let s=await eB.createEmployee(e);ez(s,"Employee created successfully")&&(eF(!1),await eG())}}catch(e){console.error("Form submission error:",e),eN.oR.error("Failed to save employee")}};return(0,t.jsx)(_,{onRefresh:e1,className:"flex-1",children:(0,t.jsxs)("div",{className:"space-y-6 p-6",children:[(0,t.jsx)(eW.Y,{title:"Employee Management",subtitle:"Managing ".concat(Q," employees across ").concat(a.length-1," departments"),actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[ef.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 mr-4",children:[(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:[ef.length," selected"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:()=>eI(!eD),children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Bulk Actions"]})]}),(0,t.jsxs)($,{variant:"secondary",size:"sm",onClick:e1,disabled:eE,children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2 ".concat(eE?"animate-spin":"")}),eE?"Refreshing...":"Refresh"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,t.jsxs)(Z.$,{size:"sm",onClick:eQ,children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Add Employee"]})]})}),eD&&ef.length>0&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:["Bulk Actions for ",ef.length," employees:"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:()=>e4("send-email"),children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Send Email"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:()=>e4("export-data"),children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Export Data"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",onClick:()=>e4("update-status"),children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Update Status"]})]})]}),(0,t.jsx)(Z.$,{variant:"ghost",size:"sm",onClick:()=>eI(!1),children:(0,t.jsx)(h.A,{className:"w-4 h-4"})})]})}),(0,t.jsx)(B.Zp,{children:(0,t.jsxs)(B.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(L.p,{placeholder:"Search employees by name, email, position, or department...",value:er,onChange:e=>ei(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("select",{value:en,onChange:e=>ec(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by department",children:a.map(e=>(0,t.jsxs)("option",{value:e,children:[e," Department"]},e))}),(0,t.jsx)("select",{value:ed,onChange:e=>eo(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by status",children:e$.map(e=>(0,t.jsx)("option",{value:e,children:"All"===e?"All Status":e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,t.jsx)(Z.$,{variant:"outline",size:"icon",onClick:()=>ew(!ev),className:ev?"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800":"",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})}),(0,t.jsx)(Z.$,{variant:"outline",size:"icon",onClick:()=>e2(eh),children:"asc"===eu?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]}),ev&&(0,t.jsx)(r.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Sort By"}),(0,t.jsxs)("select",{value:eh,onChange:e=>ep(e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Sort by field",children:[(0,t.jsx)("option",{value:"name",children:"Name"}),(0,t.jsx)("option",{value:"department",children:"Department"}),(0,t.jsx)("option",{value:"performance",children:"Performance"}),(0,t.jsx)("option",{value:"startDate",children:"Start Date"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Performance Level"}),(0,t.jsxs)("select",{value:eC,onChange:e=>ek(e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by performance level",children:[(0,t.jsx)("option",{value:"all",children:"All Performance Levels"}),(0,t.jsx)("option",{value:"high",children:"High (4.5+)"}),(0,t.jsx)("option",{value:"medium",children:"Medium (3.5-4.4)"}),(0,t.jsx)("option",{value:"low",children:"Low (<3.5)"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Salary Range"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(L.p,{type:"number",placeholder:"Min",value:eA[0],onChange:e=>eS([parseInt(e.target.value)||0,eA[1]]),className:"w-20"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"-"}),(0,t.jsx)(L.p,{type:"number",placeholder:"Max",value:eA[1],onChange:e=>eS([eA[0],parseInt(e.target.value)||2e5]),className:"w-20"})]})]})]})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Employees"})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.filter(e=>"active"===e.status).length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Active"})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.filter(e=>"on-leave"===e.status).length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"On Leave"})]})]})})}),(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:(e.reduce((e,s)=>e+s.performance,0)/e.length).toFixed(1)}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Performance"})]})]})})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",eV.length," of ",e.length," employees"]}),ef.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("span",{className:"text-sm text-blue-600 font-medium",children:[ef.length," selected"]}),(0,t.jsx)(Z.$,{variant:"ghost",size:"sm",onClick:()=>eg([]),className:"text-blue-600 hover:text-blue-700",children:"Clear selection"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(Z.$,{variant:"grid"===em?"default":"outline",size:"sm",onClick:()=>ex("grid"),children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Grid"]}),(0,t.jsxs)(Z.$,{variant:"list"===em?"default":"outline",size:"sm",onClick:()=>ex("list"),children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"List"]})]})]}),"grid"===em&&(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:eV.map((e,s)=>(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsx)(O,{onSwipeLeft:()=>e5(e.id),onSwipeRight:()=>e6(e.id),className:"hover:shadow-lg transition-all cursor-pointer ".concat(ef.includes(e.id)?"ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20":""),children:(0,t.jsxs)(B.Zp,{className:"border-0 shadow-none bg-transparent",children:[(0,t.jsx)(B.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"checkbox",checked:ef.includes(e.id),onChange:()=>eK(e.id),className:"absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select ".concat(e.name)}),(0,t.jsxs)(z.eu,{className:"h-12 w-12 ml-6",children:[(0,t.jsx)(z.BK,{src:e.avatar,alt:e.name}),(0,t.jsx)(z.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-foreground",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.position})]})]}),(0,t.jsx)(Z.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})}),(0,t.jsxs)(B.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(ey.E,{variant:eH(e.status),children:e.status.replace("-"," ")}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(eJ(e.performance))}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat(eJ(e.performance)),children:e.performance})]})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"truncate",children:e.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.department})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.location})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Started ",new Date(e.startDate).toLocaleDateString()]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["$",e.salary.toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"View"]}),(0,t.jsxs)(Z.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})]})]})})},e.id))}),"list"===em&&(0,t.jsx)(B.Zp,{children:(0,t.jsx)(B.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:(0,t.jsx)("input",{type:"checkbox",checked:ef.length===eV.length&&eV.length>0,onChange:()=>{ef.length===eV.length?eg([]):eg(eV.map(e=>e.id))},className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select all employees"})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>e2("name"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Employee"}),"name"===eh&&("asc"===eu?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>e2("department"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Department"}),"department"===eh&&("asc"===eu?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>e2("performance"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Performance"}),"performance"===eh&&("asc"===eu?(0,t.jsx)(j.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Salary"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:eV.map((e,s)=>(0,t.jsxs)(r.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50 ".concat(ef.includes(e.id)?"bg-blue-50 dark:bg-blue-950/20":""),children:[(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsx)("input",{type:"checkbox",checked:ef.includes(e.id),onChange:()=>eK(e.id),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select ".concat(e.name)})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(z.eu,{className:"h-10 w-10",children:[(0,t.jsx)(z.BK,{src:e.avatar,alt:e.name}),(0,t.jsx)(z.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]})]})}),(0,t.jsx)("td",{className:"py-4 px-6 text-foreground",children:e.position}),(0,t.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsx)(ey.E,{variant:eH(e.status),children:e.status.replace("-"," ")})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(eJ(e.performance))}),(0,t.jsx)("span",{className:"font-medium ".concat(eJ(e.performance)),children:e.performance})]})}),(0,t.jsxs)("td",{className:"py-4 px-6 text-foreground font-medium",children:["$",e.salary.toLocaleString()]}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(Z.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(Z.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(Z.$,{variant:"ghost",size:"icon",className:"h-8 w-8 text-red-600",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}),(0,t.jsx)(q,{icon:(0,t.jsx)(d.A,{className:"h-6 w-6"}),onClick:eQ,position:"bottom-right",className:"lg:hidden"}),(0,t.jsx)(U,{open:eP,onOpenChange:eF,children:(0,t.jsxs)(H,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(J,{children:(0,t.jsx)(K,{children:eq?"Edit Employee":"Add New Employee"})}),(0,t.jsx)(eb,{employee:eq,onSubmit:e3,onCancel:()=>{eF(!1),eM(null)},departments:a.filter(e=>"All"!==e.id),positions:Y,managers:M})]})}),(0,t.jsx)(U,{open:eZ,onOpenChange:eL,children:(0,t.jsxs)(H,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(J,{children:(0,t.jsx)(K,{children:"Employee Profile"})}),eO&&(0,t.jsx)(eY,{employeeId:eO,onEdit:()=>{let s=e.find(e=>e.id===eO);s&&(eL(!1),eX(s))},onClose:()=>{eL(!1),e_(null)}})]})}),V&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 flex items-center space-x-3",children:[(0,t.jsx)(F.A,{className:"h-6 w-6 animate-spin"}),(0,t.jsx)("span",{children:"Loading employees..."})]})})]})})}},37877:(e,s,a)=>{Promise.resolve().then(a.bind(a,34906))}},e=>{var s=s=>e(e.s=s);e.O(0,[706,352,289,274,262,441,684,358],()=>s(37877)),_N_E=e.O()}]);