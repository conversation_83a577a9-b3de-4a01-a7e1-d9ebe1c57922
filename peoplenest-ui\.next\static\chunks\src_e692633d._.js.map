{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        error: \"border-red-500 focus-visible:ring-red-500\",\n        success: \"border-green-500 focus-visible:ring-green-500\",\n      },\n      size: {\n        default: \"h-10\",\n        sm: \"h-9 px-2 text-xs\",\n        lg: \"h-11 px-4\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n  error?: string\n  label?: string\n  helperText?: string\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type, \n    variant, \n    size, \n    leftIcon, \n    rightIcon, \n    error, \n    label, \n    helperText,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || React.useId()\n    const hasError = !!error\n    const finalVariant = hasError ? \"error\" : variant\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-foreground mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              inputVariants({ variant: finalVariant, size, className }),\n              leftIcon && \"pl-10\",\n              rightIcon && \"pr-10\"\n            )}\n            ref={ref}\n            id={inputId}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {(error || helperText) && (\n          <p className={cn(\n            \"mt-1 text-xs\",\n            hasError ? \"text-destructive\" : \"text-muted-foreground\"\n          )}>\n            {error || helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,2VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAaF,MAAM,sBAAQ,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAC3B,CAAC,EACC,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,EAAE,EACF,GAAG,OACJ,EAAE;;IACD,MAAM,UAAU,MAAM,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAChC,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,eAAe,WAAW,UAAU;IAE1C,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;4BAAE,SAAS;4BAAc;4BAAM;wBAAU,IACvD,YAAY,SACZ,aAAa;wBAEf,KAAK;wBACL,IAAI;wBACH,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,CAAC,SAAS,UAAU,mBACnB,6LAAC;gBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,gBACA,WAAW,qBAAqB;0BAE/B,SAAS;;;;;;;;;;;;AAKpB;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-md\",\n        outlined: \"border-2\",\n        ghost: \"border-transparent shadow-none\",\n      },\n      padding: {\n        none: \"\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {\n  hover?: boolean\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, hover = false, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        cardVariants({ variant, padding }),\n        hover && \"transition-shadow hover:shadow-md\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { \n  Card, \n  CardHeader, \n  CardFooter, \n  CardTitle, \n  CardDescription, \n  CardContent,\n  cardVariants \n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AASF,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,KAAK,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aAAa;YAAE;YAAS;QAAQ,IAChC,SAAS,qCACT;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/touch-friendly.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef, useEffect, ReactNode } from \"react\"\nimport { motion, PanInfo, useMotionValue, useTransform } from \"framer-motion\"\nimport { cn } from \"@/lib/utils\"\n\n// Touch-friendly button with haptic feedback\ninterface TouchButtonProps {\n  children: ReactNode\n  onClick?: () => void\n  variant?: \"primary\" | \"secondary\" | \"ghost\"\n  size?: \"sm\" | \"md\" | \"lg\"\n  disabled?: boolean\n  className?: string\n}\n\nexport function TouchButton({ \n  children, \n  onClick, \n  variant = \"primary\", \n  size = \"md\", \n  disabled = false,\n  className \n}: TouchButtonProps) {\n  const [isPressed, setIsPressed] = useState(false)\n\n  const handleTouchStart = () => {\n    setIsPressed(true)\n    // Haptic feedback for supported devices\n    if ('vibrate' in navigator) {\n      navigator.vibrate(10)\n    }\n  }\n\n  const handleTouchEnd = () => {\n    setIsPressed(false)\n    if (!disabled && onClick) {\n      onClick()\n    }\n  }\n\n  const baseClasses = \"relative overflow-hidden rounded-lg font-medium transition-all duration-200 select-none\"\n  \n  const variantClasses = {\n    primary: \"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80\",\n    secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70\",\n    ghost: \"bg-transparent text-muted-foreground hover:bg-muted active:bg-muted/80\"\n  }\n  \n  const sizeClasses = {\n    sm: \"px-4 py-2 text-sm min-h-[40px]\",\n    md: \"px-6 py-3 text-base min-h-[48px]\",\n    lg: \"px-8 py-4 text-lg min-h-[56px]\"\n  }\n\n  return (\n    <motion.button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        disabled && \"opacity-50 cursor-not-allowed\",\n        className\n      )}\n      onTouchStart={handleTouchStart}\n      onTouchEnd={handleTouchEnd}\n      onMouseDown={handleTouchStart}\n      onMouseUp={handleTouchEnd}\n      onMouseLeave={() => setIsPressed(false)}\n      whileTap={{ scale: 0.98 }}\n      disabled={disabled}\n    >\n      <motion.div\n        className=\"absolute inset-0 bg-white/20 rounded-lg\"\n        initial={{ scale: 0, opacity: 0 }}\n        animate={isPressed ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}\n        transition={{ duration: 0.2 }}\n      />\n      <span className=\"relative z-10\">{children}</span>\n    </motion.button>\n  )\n}\n\n// Swipeable card component\ninterface SwipeableCardProps {\n  children: ReactNode\n  onSwipeLeft?: () => void\n  onSwipeRight?: () => void\n  swipeThreshold?: number\n  className?: string\n}\n\nexport function SwipeableCard({ \n  children, \n  onSwipeLeft, \n  onSwipeRight, \n  swipeThreshold = 100,\n  className \n}: SwipeableCardProps) {\n  const x = useMotionValue(0)\n  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0.5, 0.8, 1, 0.8, 0.5])\n  const backgroundColor = useTransform(\n    x,\n    [-200, -100, 0, 100, 200],\n    [\"#ef4444\", \"#f87171\", \"#ffffff\", \"#10b981\", \"#059669\"]\n  )\n\n  const handleDragEnd = (event: any, info: PanInfo) => {\n    const offset = info.offset.x\n    \n    if (offset > swipeThreshold && onSwipeRight) {\n      onSwipeRight()\n    } else if (offset < -swipeThreshold && onSwipeLeft) {\n      onSwipeLeft()\n    }\n    \n    // Reset position\n    x.set(0)\n  }\n\n  return (\n    <motion.div\n      className={cn(\"relative bg-white rounded-lg shadow-sm border border-gray-200\", className)}\n      style={{ x, opacity, backgroundColor }}\n      drag=\"x\"\n      dragConstraints={{ left: -200, right: 200 }}\n      dragElastic={0.2}\n      onDragEnd={handleDragEnd}\n      whileDrag={{ scale: 1.02 }}\n    >\n      {children}\n      \n      {/* Swipe indicators */}\n      <motion.div\n        className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white font-medium\"\n        style={{ opacity: useTransform(x, [-200, -100, 0], [1, 0.5, 0]) }}\n      >\n        Delete\n      </motion.div>\n      \n      <motion.div\n        className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-white font-medium\"\n        style={{ opacity: useTransform(x, [0, 100, 200], [0, 0.5, 1]) }}\n      >\n        Archive\n      </motion.div>\n    </motion.div>\n  )\n}\n\n// Pull-to-refresh component\ninterface PullToRefreshProps {\n  children: ReactNode\n  onRefresh: () => Promise<void>\n  refreshThreshold?: number\n  className?: string\n}\n\nexport function PullToRefresh({ \n  children, \n  onRefresh, \n  refreshThreshold = 80,\n  className \n}: PullToRefreshProps) {\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const [pullDistance, setPullDistance] = useState(0)\n  const containerRef = useRef<HTMLDivElement>(null)\n\n  const handleTouchStart = (e: React.TouchEvent) => {\n    const container = containerRef.current\n    if (container && container.scrollTop === 0) {\n      const touch = e.touches[0]\n      container.dataset.startY = touch.clientY.toString()\n    }\n  }\n\n  const handleTouchMove = (e: React.TouchEvent) => {\n    const container = containerRef.current\n    if (container && container.dataset.startY && container.scrollTop === 0) {\n      const touch = e.touches[0]\n      const startY = parseInt(container.dataset.startY)\n      const currentY = touch.clientY\n      const distance = Math.max(0, currentY - startY)\n      \n      if (distance > 0) {\n        e.preventDefault()\n        setPullDistance(Math.min(distance, refreshThreshold * 1.5))\n      }\n    }\n  }\n\n  const handleTouchEnd = async () => {\n    const container = containerRef.current\n    if (container) {\n      delete container.dataset.startY\n      \n      if (pullDistance >= refreshThreshold && !isRefreshing) {\n        setIsRefreshing(true)\n        try {\n          await onRefresh()\n        } finally {\n          setIsRefreshing(false)\n        }\n      }\n      \n      setPullDistance(0)\n    }\n  }\n\n  const refreshProgress = Math.min(pullDistance / refreshThreshold, 1)\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"relative overflow-auto\", className)}\n      onTouchStart={handleTouchStart}\n      onTouchMove={handleTouchMove}\n      onTouchEnd={handleTouchEnd}\n    >\n      {/* Pull indicator */}\n      <motion.div\n        className=\"absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 text-primary\"\n        style={{ height: pullDistance }}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: pullDistance > 0 ? 1 : 0 }}\n      >\n        <motion.div\n          className=\"flex items-center space-x-2\"\n          animate={{ \n            rotate: isRefreshing ? 360 : refreshProgress * 180 \n          }}\n          transition={{ \n            duration: isRefreshing ? 1 : 0,\n            repeat: isRefreshing ? Infinity : 0,\n            ease: \"linear\"\n          }}\n        >\n          <div className=\"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full\" />\n        </motion.div>\n        <span className=\"ml-2 text-sm font-medium\">\n          {isRefreshing ? \"Refreshing...\" : pullDistance >= refreshThreshold ? \"Release to refresh\" : \"Pull to refresh\"}\n        </span>\n      </motion.div>\n      \n      <motion.div\n        style={{ paddingTop: pullDistance }}\n        transition={{ type: \"spring\", damping: 20, stiffness: 300 }}\n      >\n        {children}\n      </motion.div>\n    </div>\n  )\n}\n\n// Touch-friendly input with better mobile UX\ninterface TouchInputProps {\n  placeholder?: string\n  value?: string\n  onChange?: (value: string) => void\n  type?: \"text\" | \"email\" | \"password\" | \"number\" | \"tel\"\n  autoComplete?: string\n  className?: string\n}\n\nexport function TouchInput({ \n  placeholder, \n  value, \n  onChange, \n  type = \"text\",\n  autoComplete,\n  className \n}: TouchInputProps) {\n  const [isFocused, setIsFocused] = useState(false)\n\n  return (\n    <motion.div\n      className={cn(\n        \"relative border border-gray-200 rounded-lg transition-all duration-200\",\n        isFocused && \"border-blue-500 ring-2 ring-blue-100\",\n        className\n      )}\n      whileTap={{ scale: 0.99 }}\n    >\n      <input\n        type={type}\n        placeholder={placeholder}\n        value={value}\n        onChange={(e) => onChange?.(e.target.value)}\n        autoComplete={autoComplete}\n        className=\"w-full px-4 py-3 text-base bg-transparent rounded-lg outline-none min-h-[48px]\"\n        onFocus={() => setIsFocused(true)}\n        onBlur={() => setIsFocused(false)}\n        // Prevent zoom on iOS\n        style={{ fontSize: '16px' }}\n      />\n    </motion.div>\n  )\n}\n\n// Floating Action Button\ninterface FABProps {\n  icon: ReactNode\n  onClick: () => void\n  position?: \"bottom-right\" | \"bottom-left\" | \"bottom-center\"\n  className?: string\n}\n\nexport function FloatingActionButton({ \n  icon, \n  onClick, \n  position = \"bottom-right\",\n  className \n}: FABProps) {\n  const positionClasses = {\n    \"bottom-right\": \"bottom-6 right-6\",\n    \"bottom-left\": \"bottom-6 left-6\",\n    \"bottom-center\": \"bottom-6 left-1/2 transform -translate-x-1/2\"\n  }\n\n  return (\n    <motion.button\n      className={cn(\n        \"fixed z-50 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center\",\n        positionClasses[position],\n        className\n      )}\n      onClick={onClick}\n      whileTap={{ scale: 0.9 }}\n      whileHover={{ scale: 1.1 }}\n      initial={{ scale: 0 }}\n      animate={{ scale: 1 }}\n      transition={{ type: \"spring\", damping: 15, stiffness: 300 }}\n    >\n      {icon}\n    </motion.button>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAgBO,SAAS,YAAY,EAC1B,QAAQ,EACR,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACQ;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB;QACvB,aAAa;QACb,wCAAwC;QACxC,IAAI,aAAa,WAAW;YAC1B,UAAU,OAAO,CAAC;QACpB;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa;QACb,IAAI,CAAC,YAAY,SAAS;YACxB;QACF;IACF;IAEA,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,YAAY,iCACZ;QAEF,cAAc;QACd,YAAY;QACZ,aAAa;QACb,WAAW;QACX,cAAc,IAAM,aAAa;QACjC,UAAU;YAAE,OAAO;QAAK;QACxB,UAAU;;0BAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,SAAS,YAAY;oBAAE,OAAO;oBAAG,SAAS;gBAAE,IAAI;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBACvE,YAAY;oBAAE,UAAU;gBAAI;;;;;;0BAE9B,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAGvC;GAjEgB;KAAA;AA4ET,SAAS,cAAc,EAC5B,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,iBAAiB,GAAG,EACpB,SAAS,EACU;;IACnB,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK,CAAC;QAAK;QAAG;QAAK;KAAI,EAAE;QAAC;QAAK;QAAK;QAAG;QAAK;KAAI;IAClF,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EACjC,GACA;QAAC,CAAC;QAAK,CAAC;QAAK;QAAG;QAAK;KAAI,EACzB;QAAC;QAAW;QAAW;QAAW;QAAW;KAAU;IAGzD,MAAM,gBAAgB,CAAC,OAAY;QACjC,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC;QAE5B,IAAI,SAAS,kBAAkB,cAAc;YAC3C;QACF,OAAO,IAAI,SAAS,CAAC,kBAAkB,aAAa;YAClD;QACF;QAEA,iBAAiB;QACjB,EAAE,GAAG,CAAC;IACR;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC/E,OAAO;YAAE;YAAG;YAAS;QAAgB;QACrC,MAAK;QACL,iBAAiB;YAAE,MAAM,CAAC;YAAK,OAAO;QAAI;QAC1C,aAAa;QACb,WAAW;QACX,WAAW;YAAE,OAAO;QAAK;;YAExB;0BAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,SAAS,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;wBAAC,CAAC;wBAAK,CAAC;wBAAK;qBAAE,EAAE;wBAAC;wBAAG;wBAAK;qBAAE;gBAAE;0BACjE;;;;;;0BAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,SAAS,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;wBAAC;wBAAG;wBAAK;qBAAI,EAAE;wBAAC;wBAAG;wBAAK;qBAAE;gBAAE;0BAC/D;;;;;;;;;;;;AAKP;IAxDgB;;QAOJ,qLAAA,CAAA,iBAAc;QACR,+KAAA,CAAA,eAAY;QACJ,+KAAA,CAAA,eAAY;QAkCZ,+KAAA,CAAA,eAAY;QAOZ,+KAAA,CAAA,eAAY;;;MAlDtB;AAkET,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,mBAAmB,EAAE,EACrB,SAAS,EACU;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,aAAa,UAAU,SAAS,KAAK,GAAG;YAC1C,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;YAC1B,UAAU,OAAO,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ;QACnD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,aAAa,UAAU,OAAO,CAAC,MAAM,IAAI,UAAU,SAAS,KAAK,GAAG;YACtE,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;YAC1B,MAAM,SAAS,SAAS,UAAU,OAAO,CAAC,MAAM;YAChD,MAAM,WAAW,MAAM,OAAO;YAC9B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,WAAW;YAExC,IAAI,WAAW,GAAG;gBAChB,EAAE,cAAc;gBAChB,gBAAgB,KAAK,GAAG,CAAC,UAAU,mBAAmB;YACxD;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,WAAW;YACb,OAAO,UAAU,OAAO,CAAC,MAAM;YAE/B,IAAI,gBAAgB,oBAAoB,CAAC,cAAc;gBACrD,gBAAgB;gBAChB,IAAI;oBACF,MAAM;gBACR,SAAU;oBACR,gBAAgB;gBAClB;YACF;YAEA,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,KAAK,GAAG,CAAC,eAAe,kBAAkB;IAElE,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACxC,cAAc;QACd,aAAa;QACb,YAAY;;0BAGZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAa;gBAC9B,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS,eAAe,IAAI,IAAI;gBAAE;;kCAE7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ,eAAe,MAAM,kBAAkB;wBACjD;wBACA,YAAY;4BACV,UAAU,eAAe,IAAI;4BAC7B,QAAQ,eAAe,WAAW;4BAClC,MAAM;wBACR;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAK,WAAU;kCACb,eAAe,kBAAkB,gBAAgB,mBAAmB,uBAAuB;;;;;;;;;;;;0BAIhG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE,YAAY;gBAAa;gBAClC,YAAY;oBAAE,MAAM;oBAAU,SAAS;oBAAI,WAAW;gBAAI;0BAEzD;;;;;;;;;;;;AAIT;IA9FgB;MAAA;AA0GT,SAAS,WAAW,EACzB,WAAW,EACX,KAAK,EACL,QAAQ,EACR,OAAO,MAAM,EACb,YAAY,EACZ,SAAS,EACO;;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,aAAa,wCACb;QAEF,UAAU;YAAE,OAAO;QAAK;kBAExB,cAAA,6LAAC;YACC,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;YAC1C,cAAc;YACd,WAAU;YACV,SAAS,IAAM,aAAa;YAC5B,QAAQ,IAAM,aAAa;YAC3B,sBAAsB;YACtB,OAAO;gBAAE,UAAU;YAAO;;;;;;;;;;;AAIlC;IAjCgB;MAAA;AA2CT,SAAS,qBAAqB,EACnC,IAAI,EACJ,OAAO,EACP,WAAW,cAAc,EACzB,SAAS,EACA;IACT,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;IACnB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mHACA,eAAe,CAAC,SAAS,EACzB;QAEF,SAAS;QACT,UAAU;YAAE,OAAO;QAAI;QACvB,YAAY;YAAE,OAAO;QAAI;QACzB,SAAS;YAAE,OAAO;QAAE;QACpB,SAAS;YAAE,OAAO;QAAE;QACpB,YAAY;YAAE,MAAM;YAAU,SAAS;YAAI,WAAW;QAAI;kBAEzD;;;;;;AAGP;MA7BgB", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/employees/EmployeeForm.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { motion } from \"framer-motion\"\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Briefcase,\n  DollarSign,\n  Building,\n  Users,\n  Save,\n  X,\n  Upload,\n  AlertCircle,\n  CheckCircle,\n  Loader2\n} from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { toast } from \"sonner\"\n\nconst employeeSchema = z.object({\n  firstName: z.string().min(2, \"First name must be at least 2 characters\"),\n  lastName: z.string().min(2, \"Last name must be at least 2 characters\"),\n  email: z.string().email(\"Invalid email address\"),\n  personalEmail: z.string().email(\"Invalid email address\").optional().or(z.literal(\"\")),\n  phone: z.string().min(10, \"Phone number must be at least 10 digits\"),\n  dateOfBirth: z.string().optional(),\n  gender: z.enum([\"male\", \"female\", \"other\", \"prefer_not_to_say\"]).optional(),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().optional()\n  }).optional(),\n  emergencyContact: z.object({\n    name: z.string().optional(),\n    relationship: z.string().optional(),\n    phone: z.string().optional(),\n    email: z.string().email().optional().or(z.literal(\"\"))\n  }).optional(),\n  departmentId: z.string().min(1, \"Department is required\"),\n  positionId: z.string().min(1, \"Position is required\"),\n  managerId: z.string().optional(),\n  employeeType: z.enum([\"full_time\", \"part_time\", \"contract\", \"intern\"]),\n  hireDate: z.string().min(1, \"Hire date is required\"),\n  salary: z.number().min(0, \"Salary must be positive\").optional(),\n  currency: z.string().default(\"USD\"),\n  workLocation: z.enum([\"office\", \"remote\", \"hybrid\"]),\n  skills: z.array(z.string()).optional(),\n  certifications: z.array(z.object({\n    name: z.string(),\n    issuer: z.string(),\n    issueDate: z.string().optional(),\n    expiryDate: z.string().optional()\n  })).optional()\n})\n\ntype EmployeeFormData = z.infer<typeof employeeSchema>\n\ninterface EmployeeFormProps {\n  employee?: any\n  onSubmit: (data: EmployeeFormData) => Promise<void>\n  onCancel: () => void\n  isLoading?: boolean\n  departments: Array<{ id: string; name: string }>\n  positions: Array<{ id: string; title: string; departmentId: string }>\n  managers: Array<{ id: string; name: string; departmentId: string }>\n}\n\nexport function EmployeeForm({\n  employee,\n  onSubmit,\n  onCancel,\n  isLoading = false,\n  departments = [],\n  positions = [],\n  managers = []\n}: EmployeeFormProps) {\n  const [selectedDepartment, setSelectedDepartment] = useState(employee?.departmentId || \"\")\n  const [skills, setSkills] = useState<string[]>(employee?.skills || [])\n  const [newSkill, setNewSkill] = useState(\"\")\n  const [certifications, setCertifications] = useState(employee?.certifications || [])\n  const [profileImage, setProfileImage] = useState<string | null>(employee?.avatar || null)\n  const [imageFile, setImageFile] = useState<File | null>(null)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    setValue,\n    watch,\n    reset\n  } = useForm<EmployeeFormData>({\n    resolver: zodResolver(employeeSchema),\n    defaultValues: employee ? {\n      ...employee,\n      address: employee.address || {},\n      emergencyContact: employee.emergencyContact || {},\n      skills: employee.skills || [],\n      certifications: employee.certifications || []\n    } : {\n      currency: \"USD\",\n      workLocation: \"office\",\n      employeeType: \"full_time\"\n    }\n  })\n\n  const watchedDepartment = watch(\"departmentId\")\n\n  useEffect(() => {\n    if (watchedDepartment) {\n      setSelectedDepartment(watchedDepartment)\n    }\n  }, [watchedDepartment])\n\n  const filteredPositions = positions.filter(pos => pos.departmentId === selectedDepartment)\n  const filteredManagers = managers.filter(mgr => mgr.departmentId === selectedDepartment)\n\n  const handleFormSubmit = async (data: EmployeeFormData) => {\n    try {\n      const formData = {\n        ...data,\n        skills,\n        certifications,\n        profileImage: imageFile\n      }\n      await onSubmit(formData)\n      toast.success(employee ? \"Employee updated successfully\" : \"Employee created successfully\")\n    } catch (error) {\n      toast.error(\"Failed to save employee\")\n      console.error(\"Form submission error:\", error)\n    }\n  }\n\n  const addSkill = () => {\n    if (newSkill.trim() && !skills.includes(newSkill.trim())) {\n      setSkills([...skills, newSkill.trim()])\n      setNewSkill(\"\")\n    }\n  }\n\n  const removeSkill = (skillToRemove: string) => {\n    setSkills(skills.filter(skill => skill !== skillToRemove))\n  }\n\n  const addCertification = () => {\n    setCertifications([...certifications, {\n      name: \"\",\n      issuer: \"\",\n      issueDate: \"\",\n      expiryDate: \"\"\n    }])\n  }\n\n  const updateCertification = (index: number, field: string, value: string) => {\n    const updated = [...certifications]\n    updated[index] = { ...updated[index], [field]: value }\n    setCertifications(updated)\n  }\n\n  const removeCertification = (index: number) => {\n    setCertifications(certifications.filter((_, i) => i !== index))\n  }\n\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      setImageFile(file)\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        setProfileImage(e.target?.result as string)\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"max-w-4xl mx-auto\"\n    >\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n        {/* Header */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <User className=\"h-5 w-5\" />\n                  {employee ? \"Edit Employee\" : \"Add New Employee\"}\n                </CardTitle>\n                <CardDescription>\n                  {employee ? \"Update employee information\" : \"Enter employee details to create a new profile\"}\n                </CardDescription>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isSubmitting || isLoading}\n                >\n                  <X className=\"h-4 w-4 mr-2\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  disabled={isSubmitting || isLoading}\n                  className=\"min-w-[120px]\"\n                >\n                  {isSubmitting || isLoading ? (\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  ) : (\n                    <Save className=\"h-4 w-4 mr-2\" />\n                  )}\n                  {employee ? \"Update\" : \"Create\"}\n                </Button>\n              </div>\n            </div>\n          </CardHeader>\n        </Card>\n\n        {/* Profile Image */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Profile Photo</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex items-center gap-4\">\n              <Avatar className=\"h-20 w-20\">\n                <AvatarImage src={profileImage || undefined} />\n                <AvatarFallback>\n                  <User className=\"h-8 w-8\" />\n                </AvatarFallback>\n              </Avatar>\n              <div>\n                <Label htmlFor=\"profile-image\" className=\"cursor-pointer\">\n                  <div className=\"flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted\">\n                    <Upload className=\"h-4 w-4\" />\n                    Upload Photo\n                  </div>\n                </Label>\n                <input\n                  id=\"profile-image\"\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageUpload}\n                  className=\"hidden\"\n                />\n                <p className=\"text-xs text-muted-foreground mt-1\">\n                  JPG, PNG or GIF. Max size 5MB.\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Personal Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Personal Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"firstName\">First Name *</Label>\n                <Input\n                  id=\"firstName\"\n                  {...register(\"firstName\")}\n                  error={errors.firstName?.message}\n                  leftIcon={<User className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"lastName\">Last Name *</Label>\n                <Input\n                  id=\"lastName\"\n                  {...register(\"lastName\")}\n                  error={errors.lastName?.message}\n                  leftIcon={<User className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"email\">Work Email *</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  {...register(\"email\")}\n                  error={errors.email?.message}\n                  leftIcon={<Mail className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"personalEmail\">Personal Email</Label>\n                <Input\n                  id=\"personalEmail\"\n                  type=\"email\"\n                  {...register(\"personalEmail\")}\n                  error={errors.personalEmail?.message}\n                  leftIcon={<Mail className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"phone\">Phone Number *</Label>\n                <Input\n                  id=\"phone\"\n                  {...register(\"phone\")}\n                  error={errors.phone?.message}\n                  leftIcon={<Phone className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"dateOfBirth\">Date of Birth</Label>\n                <Input\n                  id=\"dateOfBirth\"\n                  type=\"date\"\n                  {...register(\"dateOfBirth\")}\n                  error={errors.dateOfBirth?.message}\n                  leftIcon={<Calendar className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"gender\">Gender</Label>\n                <Select onValueChange={(value) => setValue(\"gender\", value as any)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select gender\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"male\">Male</SelectItem>\n                    <SelectItem value=\"female\">Female</SelectItem>\n                    <SelectItem value=\"other\">Other</SelectItem>\n                    <SelectItem value=\"prefer_not_to_say\">Prefer not to say</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Address Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Address Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"address.street\">Street Address</Label>\n              <Input\n                id=\"address.street\"\n                {...register(\"address.street\")}\n                leftIcon={<MapPin className=\"h-4 w-4\" />}\n              />\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <Label htmlFor=\"address.city\">City</Label>\n                <Input id=\"address.city\" {...register(\"address.city\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.state\">State/Province</Label>\n                <Input id=\"address.state\" {...register(\"address.state\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.zipCode\">ZIP/Postal Code</Label>\n                <Input id=\"address.zipCode\" {...register(\"address.zipCode\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.country\">Country</Label>\n                <Input id=\"address.country\" {...register(\"address.country\")} />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Emergency Contact */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Emergency Contact</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"emergencyContact.name\">Contact Name</Label>\n                <Input\n                  id=\"emergencyContact.name\"\n                  {...register(\"emergencyContact.name\")}\n                  leftIcon={<User className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"emergencyContact.relationship\">Relationship</Label>\n                <Input\n                  id=\"emergencyContact.relationship\"\n                  {...register(\"emergencyContact.relationship\")}\n                />\n              </div>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"emergencyContact.phone\">Phone Number</Label>\n                <Input\n                  id=\"emergencyContact.phone\"\n                  {...register(\"emergencyContact.phone\")}\n                  leftIcon={<Phone className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"emergencyContact.email\">Email</Label>\n                <Input\n                  id=\"emergencyContact.email\"\n                  type=\"email\"\n                  {...register(\"emergencyContact.email\")}\n                  leftIcon={<Mail className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Employment Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Employment Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"departmentId\">Department *</Label>\n                <Select onValueChange={(value) => setValue(\"departmentId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select department\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {departments.map((dept) => (\n                      <SelectItem key={dept.id} value={dept.id}>\n                        {dept.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors.departmentId && (\n                  <p className=\"text-xs text-red-600 mt-1\">{errors.departmentId.message}</p>\n                )}\n              </div>\n              <div>\n                <Label htmlFor=\"positionId\">Position *</Label>\n                <Select onValueChange={(value) => setValue(\"positionId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select position\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {filteredPositions.map((pos) => (\n                      <SelectItem key={pos.id} value={pos.id}>\n                        {pos.title}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors.positionId && (\n                  <p className=\"text-xs text-red-600 mt-1\">{errors.positionId.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"managerId\">Manager</Label>\n                <Select onValueChange={(value) => setValue(\"managerId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select manager\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {filteredManagers.map((mgr) => (\n                      <SelectItem key={mgr.id} value={mgr.id}>\n                        {mgr.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"employeeType\">Employment Type *</Label>\n                <Select onValueChange={(value) => setValue(\"employeeType\", value as any)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select employment type\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"full_time\">Full Time</SelectItem>\n                    <SelectItem value=\"part_time\">Part Time</SelectItem>\n                    <SelectItem value=\"contract\">Contract</SelectItem>\n                    <SelectItem value=\"intern\">Intern</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"hireDate\">Hire Date *</Label>\n                <Input\n                  id=\"hireDate\"\n                  type=\"date\"\n                  {...register(\"hireDate\")}\n                  error={errors.hireDate?.message}\n                  leftIcon={<Calendar className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"workLocation\">Work Location *</Label>\n                <Select onValueChange={(value) => setValue(\"workLocation\", value as any)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select work location\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"office\">Office</SelectItem>\n                    <SelectItem value=\"remote\">Remote</SelectItem>\n                    <SelectItem value=\"hybrid\">Hybrid</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"salary\">Annual Salary</Label>\n                <Input\n                  id=\"salary\"\n                  type=\"number\"\n                  {...register(\"salary\", { valueAsNumber: true })}\n                  error={errors.salary?.message}\n                  leftIcon={<DollarSign className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Skills */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Skills & Competencies</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"flex gap-2\">\n              <Input\n                placeholder=\"Add a skill...\"\n                value={newSkill}\n                onChange={(e) => setNewSkill(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}\n              />\n              <Button type=\"button\" onClick={addSkill} variant=\"outline\">\n                Add\n              </Button>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {skills.map((skill, index) => (\n                <Badge key={index} variant=\"secondary\" className=\"flex items-center gap-1\">\n                  {skill}\n                  <X\n                    className=\"h-3 w-3 cursor-pointer\"\n                    onClick={() => removeSkill(skill)}\n                  />\n                </Badge>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Certifications */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <CardTitle className=\"text-lg\">Certifications</CardTitle>\n              <Button type=\"button\" onClick={addCertification} variant=\"outline\" size=\"sm\">\n                Add Certification\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {certifications.map((cert, index) => (\n              <div key={index} className=\"p-4 border rounded-lg space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"font-medium\">Certification {index + 1}</h4>\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => removeCertification(index)}\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  <Input\n                    placeholder=\"Certification name\"\n                    value={cert.name}\n                    onChange={(e) => updateCertification(index, 'name', e.target.value)}\n                  />\n                  <Input\n                    placeholder=\"Issuing organization\"\n                    value={cert.issuer}\n                    onChange={(e) => updateCertification(index, 'issuer', e.target.value)}\n                  />\n                  <Input\n                    type=\"date\"\n                    placeholder=\"Issue date\"\n                    value={cert.issueDate}\n                    onChange={(e) => updateCertification(index, 'issueDate', e.target.value)}\n                  />\n                  <Input\n                    type=\"date\"\n                    placeholder=\"Expiry date\"\n                    value={cert.expiryDate}\n                    onChange={(e) => updateCertification(index, 'expiryDate', e.target.value)}\n                  />\n                </div>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n\n        {/* Address Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Address Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"address.street\">Street Address</Label>\n              <Input\n                id=\"address.street\"\n                {...register(\"address.street\")}\n                leftIcon={<MapPin className=\"h-4 w-4\" />}\n              />\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <Label htmlFor=\"address.city\">City</Label>\n                <Input id=\"address.city\" {...register(\"address.city\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.state\">State/Province</Label>\n                <Input id=\"address.state\" {...register(\"address.state\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.zipCode\">ZIP/Postal Code</Label>\n                <Input id=\"address.zipCode\" {...register(\"address.zipCode\")} />\n              </div>\n              <div>\n                <Label htmlFor=\"address.country\">Country</Label>\n                <Input id=\"address.country\" {...register(\"address.country\")} />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Emergency Contact */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Emergency Contact</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"emergencyContact.name\">Contact Name</Label>\n                <Input\n                  id=\"emergencyContact.name\"\n                  {...register(\"emergencyContact.name\")}\n                  leftIcon={<User className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"emergencyContact.relationship\">Relationship</Label>\n                <Input\n                  id=\"emergencyContact.relationship\"\n                  {...register(\"emergencyContact.relationship\")}\n                />\n              </div>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"emergencyContact.phone\">Phone Number</Label>\n                <Input\n                  id=\"emergencyContact.phone\"\n                  {...register(\"emergencyContact.phone\")}\n                  leftIcon={<Phone className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"emergencyContact.email\">Email</Label>\n                <Input\n                  id=\"emergencyContact.email\"\n                  type=\"email\"\n                  {...register(\"emergencyContact.email\")}\n                  leftIcon={<Mail className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Employment Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Employment Information</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"departmentId\">Department *</Label>\n                <Select onValueChange={(value) => setValue(\"departmentId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select department\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {departments.map((dept) => (\n                      <SelectItem key={dept.id} value={dept.id}>\n                        {dept.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors.departmentId && (\n                  <p className=\"text-xs text-red-600 mt-1\">{errors.departmentId.message}</p>\n                )}\n              </div>\n              <div>\n                <Label htmlFor=\"positionId\">Position *</Label>\n                <Select onValueChange={(value) => setValue(\"positionId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select position\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {filteredPositions.map((pos) => (\n                      <SelectItem key={pos.id} value={pos.id}>\n                        {pos.title}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors.positionId && (\n                  <p className=\"text-xs text-red-600 mt-1\">{errors.positionId.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"managerId\">Manager</Label>\n                <Select onValueChange={(value) => setValue(\"managerId\", value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select manager\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {filteredManagers.map((mgr) => (\n                      <SelectItem key={mgr.id} value={mgr.id}>\n                        {mgr.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"employeeType\">Employment Type *</Label>\n                <Select onValueChange={(value) => setValue(\"employeeType\", value as any)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select employment type\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"full_time\">Full Time</SelectItem>\n                    <SelectItem value=\"part_time\">Part Time</SelectItem>\n                    <SelectItem value=\"contract\">Contract</SelectItem>\n                    <SelectItem value=\"intern\">Intern</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"hireDate\">Hire Date *</Label>\n                <Input\n                  id=\"hireDate\"\n                  type=\"date\"\n                  {...register(\"hireDate\")}\n                  error={errors.hireDate?.message}\n                  leftIcon={<Calendar className=\"h-4 w-4\" />}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"workLocation\">Work Location *</Label>\n                <Select onValueChange={(value) => setValue(\"workLocation\", value as any)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select work location\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"office\">Office</SelectItem>\n                    <SelectItem value=\"remote\">Remote</SelectItem>\n                    <SelectItem value=\"hybrid\">Hybrid</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"salary\">Annual Salary</Label>\n                <Input\n                  id=\"salary\"\n                  type=\"number\"\n                  {...register(\"salary\", { valueAsNumber: true })}\n                  error={errors.salary?.message}\n                  leftIcon={<DollarSign className=\"h-4 w-4\" />}\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Skills */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Skills & Competencies</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"flex gap-2\">\n              <Input\n                placeholder=\"Add a skill...\"\n                value={newSkill}\n                onChange={(e) => setNewSkill(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}\n              />\n              <Button type=\"button\" onClick={addSkill} variant=\"outline\">\n                Add\n              </Button>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {skills.map((skill, index) => (\n                <Badge key={index} variant=\"secondary\" className=\"flex items-center gap-1\">\n                  {skill}\n                  <X\n                    className=\"h-3 w-3 cursor-pointer\"\n                    onClick={() => removeSkill(skill)}\n                  />\n                </Badge>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Certifications */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <CardTitle className=\"text-lg\">Certifications</CardTitle>\n              <Button type=\"button\" onClick={addCertification} variant=\"outline\" size=\"sm\">\n                Add Certification\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {certifications.map((cert, index) => (\n              <div key={index} className=\"p-4 border rounded-lg space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"font-medium\">Certification {index + 1}</h4>\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => removeCertification(index)}\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                  <Input\n                    placeholder=\"Certification name\"\n                    value={cert.name}\n                    onChange={(e) => updateCertification(index, 'name', e.target.value)}\n                  />\n                  <Input\n                    placeholder=\"Issuing organization\"\n                    value={cert.issuer}\n                    onChange={(e) => updateCertification(index, 'issuer', e.target.value)}\n                  />\n                  <Input\n                    type=\"date\"\n                    placeholder=\"Issue date\"\n                    value={cert.issueDate}\n                    onChange={(e) => updateCertification(index, 'issueDate', e.target.value)}\n                  />\n                  <Input\n                    type=\"date\"\n                    placeholder=\"Expiry date\"\n                    value={cert.expiryDate}\n                    onChange={(e) => updateCertification(index, 'expiryDate', e.target.value)}\n                  />\n                </div>\n              </div>\n            ))}\n            {certifications.length === 0 && (\n              <p className=\"text-muted-foreground text-center py-4\">No certifications added yet.</p>\n            )}\n          </CardContent>\n        </Card>\n      </form>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;;;AAjCA;;;;;;;;;;;;;;;AAmCA,MAAM,iBAAiB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC9B,WAAW,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC7B,UAAU,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC5B,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;IACxB,eAAe,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC,yBAAyB,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oJAAA,CAAA,UAAS,AAAD,EAAE;IACjF,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;IAC1B,aAAa,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAChC,QAAQ,CAAA,GAAA,oJAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAQ;QAAU;QAAS;KAAoB,EAAE,QAAQ;IACzE,SAAS,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;QAChB,QAAQ,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QAC3B,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QACzB,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QAC1B,SAAS,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QAC5B,SAAS,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC9B,GAAG,QAAQ;IACX,kBAAkB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;QACzB,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QACzB,cAAc,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QACjC,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QAC1B,OAAO,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oJAAA,CAAA,UAAS,AAAD,EAAE;IACpD,GAAG,QAAQ;IACX,cAAc,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAChC,YAAY,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC9B,WAAW,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC9B,cAAc,CAAA,GAAA,oJAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAa;QAAY;KAAS;IACrE,UAAU,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC5B,QAAQ,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG,2BAA2B,QAAQ;IAC7D,UAAU,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,OAAO,CAAC;IAC7B,cAAc,CAAA,GAAA,oJAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;QAAU;KAAS;IACnD,QAAQ,CAAA,GAAA,oJAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,KAAK,QAAQ;IACpC,gBAAgB,CAAA,GAAA,oJAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;QAC/B,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD;QACb,QAAQ,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD;QACf,WAAW,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QAC9B,YAAY,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACjC,IAAI,QAAQ;AACd;AAcO,SAAS,aAAa,EAC3B,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EACjB,cAAc,EAAE,EAChB,YAAY,EAAE,EACd,WAAW,EAAE,EACK;;IAClB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,gBAAgB;IACvF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,UAAU,UAAU,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,kBAAkB,EAAE;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,UAAU,UAAU;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAExD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,QAAQ,EACR,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,WAAW;YACxB,GAAG,QAAQ;YACX,SAAS,SAAS,OAAO,IAAI,CAAC;YAC9B,kBAAkB,SAAS,gBAAgB,IAAI,CAAC;YAChD,QAAQ,SAAS,MAAM,IAAI,EAAE;YAC7B,gBAAgB,SAAS,cAAc,IAAI,EAAE;QAC/C,IAAI;YACF,UAAU;YACV,cAAc;YACd,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,MAAM;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,mBAAmB;gBACrB,sBAAsB;YACxB;QACF;iCAAG;QAAC;KAAkB;IAEtB,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;IACvE,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;IAErE,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,IAAI;gBACP;gBACA;gBACA,cAAc;YAChB;YACA,MAAM,SAAS;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,WAAW,kCAAkC;QAC7D,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,WAAW;QACf,IAAI,SAAS,IAAI,MAAM,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI,KAAK;YACxD,UAAU;mBAAI;gBAAQ,SAAS,IAAI;aAAG;YACtC,YAAY;QACd;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,UAAU;IAC7C;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;eAAI;YAAgB;gBACpC,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,YAAY;YACd;SAAE;IACJ;IAEA,MAAM,sBAAsB,CAAC,OAAe,OAAe;QACzD,MAAM,UAAU;eAAI;SAAe;QACnC,OAAO,CAAC,MAAM,GAAG;YAAE,GAAG,OAAO,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QACrD,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,eAAe,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC1D;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,aAAa;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,gBAAgB,EAAE,MAAM,EAAE;YAC5B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAK,UAAU,aAAa;YAAmB,WAAU;;8BAExD,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,WAAW,kBAAkB;;;;;;;sDAEhC,6LAAC,mIAAA,CAAA,kBAAe;sDACb,WAAW,gCAAgC;;;;;;;;;;;;8CAGhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,gBAAgB;;8DAE1B,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGhC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,gBAAgB;4CAC1B,WAAU;;gDAET,gBAAgB,0BACf,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAEjB,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,gBAAgB;;;;;;0DAClC,6LAAC,qIAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;0DACvC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIlC,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1D,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,YAAY;oDACzB,OAAO,OAAO,SAAS,EAAE;oDACzB,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,WAAW;oDACxB,OAAO,OAAO,QAAQ,EAAE;oDACxB,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,QAAQ;oDACrB,OAAO,OAAO,KAAK,EAAE;oDACrB,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,gBAAgB;oDAC7B,OAAO,OAAO,aAAa,EAAE;oDAC7B,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,QAAQ;oDACrB,OAAO,OAAO,KAAK,EAAE;oDACrB,wBAAU,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG/B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,cAAc;oDAC3B,OAAO,OAAO,WAAW,EAAE;oDAC3B,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGlC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,UAAU;;sEACnD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;8EAC1B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,iBAAiB;4CAC9B,wBAAU,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAgB,GAAG,SAAS,eAAe;;;;;;;;;;;;sDAEvD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAiB,GAAG,SAAS,gBAAgB;;;;;;;;;;;;sDAEzD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAmB,GAAG,SAAS,kBAAkB;;;;;;;;;;;;sDAE7D,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAmB,GAAG,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOnE,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAwB;;;;;;8DACvC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,wBAAwB;oDACrC,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgC;;;;;;8DAC/C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,gCAAgC;;;;;;;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,yBAAyB;oDACtC,wBAAU,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG/B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,yBAAyB;oDACtC,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,qIAAA,CAAA,aAAU;oEAAe,OAAO,KAAK,EAAE;8EACrC,KAAK,IAAI;mEADK,KAAK,EAAE;;;;;;;;;;;;;;;;gDAM7B,OAAO,YAAY,kBAClB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;sDAGzE,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,cAAc;;sEACvD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,kBAAkB,GAAG,CAAC,CAAC,oBACtB,6LAAC,qIAAA,CAAA,aAAU;oEAAc,OAAO,IAAI,EAAE;8EACnC,IAAI,KAAK;mEADK,IAAI,EAAE;;;;;;;;;;;;;;;;gDAM5B,OAAO,UAAU,kBAChB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAKzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,aAAa;;sEACtD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,iBAAiB,GAAG,CAAC,CAAC,oBACrB,6LAAC,qIAAA,CAAA,aAAU;oEAAc,OAAO,IAAI,EAAE;8EACnC,IAAI,IAAI;mEADM,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;sDAO/B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,WAAW;oDACxB,OAAO,OAAO,QAAQ,EAAE;oDACxB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGlC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,UAAU;wDAAE,eAAe;oDAAK,EAAE;oDAC/C,OAAO,OAAO,MAAM,EAAE;oDACtB,wBAAU,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,UAAU;;;;;;sDAEzE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAS;4CAAU,SAAQ;sDAAU;;;;;;;;;;;;8CAI7D,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,oIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAY,WAAU;;gDAC9C;8DACD,6LAAC,+LAAA,CAAA,IAAC;oDACA,WAAU;oDACV,SAAS,IAAM,YAAY;;;;;;;2CAJnB;;;;;;;;;;;;;;;;;;;;;;8BAapB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;kDAC/B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;wCAAkB,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;sCAKjF,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDAAc;wDAAe,QAAQ;;;;;;;8DACnD,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO,KAAK,IAAI;oDAChB,UAAU,CAAC,IAAM,oBAAoB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;8DAEpE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO,KAAK,MAAM;oDAClB,UAAU,CAAC,IAAM,oBAAoB,OAAO,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;8DAEtE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,SAAS;oDACrB,UAAU,CAAC,IAAM,oBAAoB,OAAO,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;8DAEzE,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO,KAAK,UAAU;oDACtB,UAAU,CAAC,IAAM,oBAAoB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;mCAjCpE;;;;;;;;;;;;;;;;8BA0ChB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,iBAAiB;4CAC9B,wBAAU,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAgB,GAAG,SAAS,eAAe;;;;;;;;;;;;sDAEvD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAiB,GAAG,SAAS,gBAAgB;;;;;;;;;;;;sDAEzD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAmB,GAAG,SAAS,kBAAkB;;;;;;;;;;;;sDAE7D,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAmB,GAAG,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOnE,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAwB;;;;;;8DACvC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,wBAAwB;oDACrC,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgC;;;;;;8DAC/C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,gCAAgC;;;;;;;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,yBAAyB;oDACtC,wBAAU,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG/B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAyB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,yBAAyB;oDACtC,wBAAU,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,qIAAA,CAAA,aAAU;oEAAe,OAAO,KAAK,EAAE;8EACrC,KAAK,IAAI;mEADK,KAAK,EAAE;;;;;;;;;;;;;;;;gDAM7B,OAAO,YAAY,kBAClB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;sDAGzE,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,cAAc;;sEACvD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,kBAAkB,GAAG,CAAC,CAAC,oBACtB,6LAAC,qIAAA,CAAA,aAAU;oEAAc,OAAO,IAAI,EAAE;8EACnC,IAAI,KAAK;mEADK,IAAI,EAAE;;;;;;;;;;;;;;;;gDAM5B,OAAO,UAAU,kBAChB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAKzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,aAAa;;sEACtD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,iBAAiB,GAAG,CAAC,CAAC,oBACrB,6LAAC,qIAAA,CAAA,aAAU;oEAAc,OAAO,IAAI,EAAE;8EACnC,IAAI,IAAI;mEADM,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;sDAO/B,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,WAAW;oDACxB,OAAO,OAAO,QAAQ,EAAE;oDACxB,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGlC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;sEACzD,6LAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8EACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,6LAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;8DACxB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,UAAU;wDAAE,eAAe;oDAAK,EAAE;oDAC/C,OAAO,OAAO,MAAM,EAAE;oDACtB,wBAAU,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,cAAc,IAAI,UAAU;;;;;;sDAEzE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAS;4CAAU,SAAQ;sDAAU;;;;;;;;;;;;8CAI7D,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,oIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAY,WAAU;;gDAC9C;8DACD,6LAAC,+LAAA,CAAA,IAAC;oDACA,WAAU;oDACV,SAAS,IAAM,YAAY;;;;;;;2CAJnB;;;;;;;;;;;;;;;;;;;;;;8BAapB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;kDAC/B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;wCAAkB,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;sCAKjF,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;4DAAc;4DAAe,QAAQ;;;;;;;kEACnD,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,oBAAoB;kEAEnC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,KAAK,IAAI;wDAChB,UAAU,CAAC,IAAM,oBAAoB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;kEAEpE,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,KAAK,MAAM;wDAClB,UAAU,CAAC,IAAM,oBAAoB,OAAO,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;kEAEtE,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO,KAAK,SAAS;wDACrB,UAAU,CAAC,IAAM,oBAAoB,OAAO,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;kEAEzE,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO,KAAK,UAAU;wDACtB,UAAU,CAAC,IAAM,oBAAoB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;uCAjCpE;;;;;gCAsCX,eAAe,MAAM,KAAK,mBACzB,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpE;GAx0BgB;;QAuBV,iKAAA,CAAA,UAAO;;;KAvBG", "debugId": null}}, {"offset": {"line": 3888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/lib/api/employeeApi.ts"], "sourcesContent": ["import { toast } from \"sonner\"\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'\n\ninterface ApiResponse<T> {\n  data?: T\n  error?: string\n  message?: string\n}\n\ninterface Employee {\n  id: string\n  firstName: string\n  lastName: string\n  email: string\n  personalEmail?: string\n  phone: string\n  dateOfBirth?: string\n  gender?: string\n  address?: {\n    street?: string\n    city?: string\n    state?: string\n    zipCode?: string\n    country?: string\n  }\n  emergencyContact?: {\n    name?: string\n    relationship?: string\n    phone?: string\n    email?: string\n  }\n  departmentId: string\n  positionId: string\n  managerId?: string\n  employeeType: 'full_time' | 'part_time' | 'contract' | 'intern'\n  hireDate: string\n  salary?: number\n  currency: string\n  workLocation: 'office' | 'remote' | 'hybrid'\n  skills?: string[]\n  certifications?: Array<{\n    name: string\n    issuer: string\n    issueDate?: string\n    expiryDate?: string\n  }>\n  status: 'active' | 'inactive' | 'terminated'\n  avatar?: string\n  createdAt: string\n  updatedAt: string\n}\n\ninterface EmployeeFilters {\n  page?: number\n  limit?: number\n  search?: string\n  department?: string\n  status?: string\n  role?: string\n  manager?: string\n  sortBy?: string\n  sortOrder?: 'asc' | 'desc'\n}\n\ninterface EmployeeListResponse {\n  employees: Employee[]\n  total: number\n  page: number\n  limit: number\n  totalPages: number\n}\n\nclass EmployeeApiService {\n  private async makeRequest<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const token = localStorage.getItem('authToken')\n      \n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...(token && { Authorization: `Bearer ${token}` }),\n          ...options.headers,\n        },\n        ...options,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const data = await response.json()\n      return { data }\n    } catch (error) {\n      console.error('API request failed:', error)\n      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'\n      return { error: errorMessage }\n    }\n  }\n\n  async getEmployees(filters: EmployeeFilters = {}): Promise<ApiResponse<EmployeeListResponse>> {\n    const queryParams = new URLSearchParams()\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        queryParams.append(key, value.toString())\n      }\n    })\n\n    const queryString = queryParams.toString()\n    const endpoint = `/employees${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<EmployeeListResponse>(endpoint)\n  }\n\n  async getEmployeeById(id: string): Promise<ApiResponse<Employee>> {\n    return this.makeRequest<Employee>(`/employees/${id}`)\n  }\n\n  async getEmployeeProfile(id: string): Promise<ApiResponse<Employee & {\n    skills: any[]\n    recentReviews: any[]\n    activeGoals: any[]\n  }>> {\n    return this.makeRequest(`/employees/${id}/profile`)\n  }\n\n  async createEmployee(employeeData: Omit<Employee, 'id' | 'createdAt' | 'updatedAt' | 'status'>): Promise<ApiResponse<Employee>> {\n    return this.makeRequest<Employee>('/employees', {\n      method: 'POST',\n      body: JSON.stringify(employeeData),\n    })\n  }\n\n  async updateEmployee(id: string, employeeData: Partial<Employee>): Promise<ApiResponse<Employee>> {\n    return this.makeRequest<Employee>(`/employees/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(employeeData),\n    })\n  }\n\n  async deleteEmployee(id: string): Promise<ApiResponse<void>> {\n    return this.makeRequest<void>(`/employees/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async uploadEmployeeAvatar(id: string, file: File): Promise<ApiResponse<{ avatarUrl: string }>> {\n    const formData = new FormData()\n    formData.append('avatar', file)\n\n    const token = localStorage.getItem('authToken')\n    \n    try {\n      const response = await fetch(`${API_BASE_URL}/employees/${id}/avatar`, {\n        method: 'POST',\n        headers: {\n          ...(token && { Authorization: `Bearer ${token}` }),\n        },\n        body: formData,\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)\n      }\n\n      const data = await response.json()\n      return { data }\n    } catch (error) {\n      console.error('Avatar upload failed:', error)\n      const errorMessage = error instanceof Error ? error.message : 'Avatar upload failed'\n      return { error: errorMessage }\n    }\n  }\n\n  async bulkUpdateEmployees(employeeIds: string[], updates: Partial<Employee>): Promise<ApiResponse<{ updated: number }>> {\n    return this.makeRequest<{ updated: number }>('/employees/bulk-update', {\n      method: 'POST',\n      body: JSON.stringify({ employeeIds, updates }),\n    })\n  }\n\n  async exportEmployees(filters: EmployeeFilters = {}): Promise<ApiResponse<{ downloadUrl: string }>> {\n    const queryParams = new URLSearchParams()\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        queryParams.append(key, value.toString())\n      }\n    })\n\n    const queryString = queryParams.toString()\n    const endpoint = `/employees/export${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ downloadUrl: string }>(endpoint)\n  }\n\n  async getDepartments(): Promise<ApiResponse<Array<{ id: string; name: string }>>> {\n    return this.makeRequest<Array<{ id: string; name: string }>>('/departments')\n  }\n\n  async getPositions(): Promise<ApiResponse<Array<{ id: string; title: string; departmentId: string }>>> {\n    return this.makeRequest<Array<{ id: string; title: string; departmentId: string }>>('/positions')\n  }\n\n  async getManagers(): Promise<ApiResponse<Array<{ id: string; name: string; departmentId: string }>>> {\n    return this.makeRequest<Array<{ id: string; name: string; departmentId: string }>>('/employees/managers')\n  }\n\n  // Helper methods for common operations\n  async searchEmployees(query: string): Promise<ApiResponse<Employee[]>> {\n    return this.getEmployees({ search: query, limit: 50 }).then(response => ({\n      ...response,\n      data: response.data?.employees || []\n    }))\n  }\n\n  async getEmployeesByDepartment(departmentId: string): Promise<ApiResponse<Employee[]>> {\n    return this.getEmployees({ department: departmentId }).then(response => ({\n      ...response,\n      data: response.data?.employees || []\n    }))\n  }\n\n  async getEmployeesByManager(managerId: string): Promise<ApiResponse<Employee[]>> {\n    return this.getEmployees({ manager: managerId }).then(response => ({\n      ...response,\n      data: response.data?.employees || []\n    }))\n  }\n}\n\n// Create and export a singleton instance\nexport const employeeApi = new EmployeeApiService()\n\n// Export types for use in components\nexport type { Employee, EmployeeFilters, EmployeeListResponse, ApiResponse }\n\n// Helper function to handle API responses with toast notifications\nexport const handleApiResponse = <T>(\n  response: ApiResponse<T>,\n  successMessage?: string,\n  showErrorToast = true\n): T | null => {\n  if (response.error) {\n    if (showErrorToast) {\n      toast.error(response.error)\n    }\n    return null\n  }\n  \n  if (successMessage && response.data) {\n    toast.success(successMessage)\n  }\n  \n  return response.data || null\n}\n\n// Helper function for async operations with loading states\nexport const withLoading = async <T>(\n  operation: () => Promise<T>,\n  setLoading: (loading: boolean) => void\n): Promise<T | null> => {\n  try {\n    setLoading(true)\n    return await operation()\n  } catch (error) {\n    console.error('Operation failed:', error)\n    toast.error(error instanceof Error ? error.message : 'Operation failed')\n    return null\n  } finally {\n    setLoading(false)\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAuExD,MAAM;IACJ,MAAc,YACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;gBACzD,SAAS;oBACP,gBAAgB;oBAChB,GAAI,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC,CAAC;oBACjD,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO;gBAAE,OAAO;YAAa;QAC/B;IACF;IAEA,MAAM,aAAa,UAA2B,CAAC,CAAC,EAA8C;QAC5F,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,cAAc,YAAY,QAAQ;QACxC,MAAM,WAAW,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEpE,OAAO,IAAI,CAAC,WAAW,CAAuB;IAChD;IAEA,MAAM,gBAAgB,EAAU,EAAkC;QAChE,OAAO,IAAI,CAAC,WAAW,CAAW,CAAC,WAAW,EAAE,IAAI;IACtD;IAEA,MAAM,mBAAmB,EAAU,EAI/B;QACF,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC;IACpD;IAEA,MAAM,eAAe,YAAyE,EAAkC;QAC9H,OAAO,IAAI,CAAC,WAAW,CAAW,cAAc;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,EAAU,EAAE,YAA+B,EAAkC;QAChG,OAAO,IAAI,CAAC,WAAW,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,eAAe,EAAU,EAA8B;QAC3D,OAAO,IAAI,CAAC,WAAW,CAAO,CAAC,WAAW,EAAE,IAAI,EAAE;YAChD,QAAQ;QACV;IACF;IAEA,MAAM,qBAAqB,EAAU,EAAE,IAAU,EAA+C;QAC9F,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,MAAM,QAAQ,aAAa,OAAO,CAAC;QAEnC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBACP,GAAI,SAAS;wBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;oBAAC,CAAC;gBACnD;gBACA,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC/E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO;gBAAE,OAAO;YAAa;QAC/B;IACF;IAEA,MAAM,oBAAoB,WAAqB,EAAE,OAA0B,EAA6C;QACtH,OAAO,IAAI,CAAC,WAAW,CAAsB,0BAA0B;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAa;YAAQ;QAC9C;IACF;IAEA,MAAM,gBAAgB,UAA2B,CAAC,CAAC,EAAiD;QAClG,MAAM,cAAc,IAAI;QAExB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,YAAY,MAAM,CAAC,KAAK,MAAM,QAAQ;YACxC;QACF;QAEA,MAAM,cAAc,YAAY,QAAQ;QACxC,MAAM,WAAW,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE3E,OAAO,IAAI,CAAC,WAAW,CAA0B;IACnD;IAEA,MAAM,iBAA4E;QAChF,OAAO,IAAI,CAAC,WAAW,CAAsC;IAC/D;IAEA,MAAM,eAAiG;QACrG,OAAO,IAAI,CAAC,WAAW,CAA6D;IACtF;IAEA,MAAM,cAA+F;QACnG,OAAO,IAAI,CAAC,WAAW,CAA4D;IACrF;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,KAAa,EAAoC;QACrE,OAAO,IAAI,CAAC,YAAY,CAAC;YAAE,QAAQ;YAAO,OAAO;QAAG,GAAG,IAAI,CAAC,CAAA,WAAY,CAAC;gBACvE,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,EAAE,aAAa,EAAE;YACtC,CAAC;IACH;IAEA,MAAM,yBAAyB,YAAoB,EAAoC;QACrF,OAAO,IAAI,CAAC,YAAY,CAAC;YAAE,YAAY;QAAa,GAAG,IAAI,CAAC,CAAA,WAAY,CAAC;gBACvE,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,EAAE,aAAa,EAAE;YACtC,CAAC;IACH;IAEA,MAAM,sBAAsB,SAAiB,EAAoC;QAC/E,OAAO,IAAI,CAAC,YAAY,CAAC;YAAE,SAAS;QAAU,GAAG,IAAI,CAAC,CAAA,WAAY,CAAC;gBACjE,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,EAAE,aAAa,EAAE;YACtC,CAAC;IACH;AACF;AAGO,MAAM,cAAc,IAAI;AAMxB,MAAM,oBAAoB,CAC/B,UACA,gBACA,iBAAiB,IAAI;IAErB,IAAI,SAAS,KAAK,EAAE;QAClB,IAAI,gBAAgB;YAClB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,KAAK;QAC5B;QACA,OAAO;IACT;IAEA,IAAI,kBAAkB,SAAS,IAAI,EAAE;QACnC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,OAAO,SAAS,IAAI,IAAI;AAC1B;AAGO,MAAM,cAAc,OACzB,WACA;IAEA,IAAI;QACF,WAAW;QACX,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACrD,OAAO;IACT,SAAU;QACR,WAAW;IACb;AACF", "debugId": null}}, {"offset": {"line": 4193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/employees/EmployeeProfile.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Briefcase,\n  Building,\n  Users,\n  Edit,\n  Download,\n  Share,\n  MoreHorizontal,\n  Star,\n  Award,\n  Target,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  FileText,\n  MessageSquare,\n  Settings,\n  Shield,\n  AlertCircle,\n  CheckCircle,\n  Loader2\n} from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { employeeApi, type Employee, handleApiResponse } from \"@/lib/api/employeeApi\"\nimport { toast } from \"sonner\"\n\ninterface EmployeeProfileProps {\n  employeeId: string\n  onEdit?: () => void\n  onClose?: () => void\n}\n\ninterface EmployeeProfileData extends Employee {\n  skills: Array<{\n    id: string\n    name: string\n    level: number\n    category: string\n  }>\n  recentReviews: Array<{\n    id: string\n    reviewDate: string\n    rating: number\n    reviewer: string\n    comments: string\n  }>\n  activeGoals: Array<{\n    id: string\n    title: string\n    description: string\n    progress: number\n    dueDate: string\n    status: string\n  }>\n}\n\nexport function EmployeeProfile({ employeeId, onEdit, onClose }: EmployeeProfileProps) {\n  const [employee, setEmployee] = useState<EmployeeProfileData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [activeTab, setActiveTab] = useState(\"overview\")\n\n  useEffect(() => {\n    loadEmployeeProfile()\n  }, [employeeId])\n\n  const loadEmployeeProfile = async () => {\n    setLoading(true)\n    try {\n      const response = await employeeApi.getEmployeeProfile(employeeId)\n      const profileData = handleApiResponse(response, undefined, true)\n      if (profileData) {\n        setEmployee(profileData as EmployeeProfileData)\n      }\n    } catch (error) {\n      console.error(\"Failed to load employee profile:\", error)\n      toast.error(\"Failed to load employee profile\")\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportProfile = async () => {\n    try {\n      // Implementation for exporting employee profile\n      toast.success(\"Profile exported successfully\")\n    } catch (error) {\n      toast.error(\"Failed to export profile\")\n    }\n  }\n\n  const handleShareProfile = async () => {\n    try {\n      // Implementation for sharing employee profile\n      toast.success(\"Profile link copied to clipboard\")\n    } catch (error) {\n      toast.error(\"Failed to share profile\")\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (!employee) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-foreground mb-2\">Employee Not Found</h3>\n          <p className=\"text-muted-foreground\">The requested employee profile could not be loaded.</p>\n        </div>\n      </div>\n    )\n  }\n\n  const fullName = `${employee.firstName} ${employee.lastName}`\n  const averageRating = employee.recentReviews.length > 0 \n    ? employee.recentReviews.reduce((sum, review) => sum + review.rating, 0) / employee.recentReviews.length \n    : 0\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"max-w-6xl mx-auto space-y-6\"\n    >\n      {/* Header */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-4\">\n              <Avatar className=\"h-20 w-20\">\n                <AvatarImage src={employee.avatar} alt={fullName} />\n                <AvatarFallback className=\"text-lg\">\n                  {employee.firstName[0]}{employee.lastName[0]}\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"space-y-2\">\n                <div>\n                  <h1 className=\"text-2xl font-bold text-foreground\">{fullName}</h1>\n                  <p className=\"text-lg text-muted-foreground\">{employee.positionId}</p>\n                  <p className=\"text-sm text-muted-foreground\">{employee.departmentId}</p>\n                </div>\n                <div className=\"flex items-center space-x-4 text-sm text-muted-foreground\">\n                  <div className=\"flex items-center space-x-1\">\n                    <Mail className=\"h-4 w-4\" />\n                    <span>{employee.email}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <Phone className=\"h-4 w-4\" />\n                    <span>{employee.phone}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <Calendar className=\"h-4 w-4\" />\n                    <span>Joined {new Date(employee.hireDate).toLocaleDateString()}</span>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Badge variant={employee.status === 'active' ? 'default' : 'secondary'}>\n                    {employee.status}\n                  </Badge>\n                  <Badge variant=\"outline\">{employee.employeeType.replace('_', ' ')}</Badge>\n                  <Badge variant=\"outline\">{employee.workLocation}</Badge>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handleShareProfile}>\n                <Share className=\"h-4 w-4 mr-2\" />\n                Share\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleExportProfile}>\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export\n              </Button>\n              {onEdit && (\n                <Button size=\"sm\" onClick={onEdit}>\n                  <Edit className=\"h-4 w-4 mr-2\" />\n                  Edit\n                </Button>\n              )}\n              <Button variant=\"ghost\" size=\"icon\">\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Star className=\"h-5 w-5 text-yellow-500\" />\n              <div>\n                <p className=\"text-sm text-muted-foreground\">Performance</p>\n                <p className=\"text-lg font-semibold\">{averageRating.toFixed(1)}/5.0</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Target className=\"h-5 w-5 text-blue-500 dark:text-blue-400\" />\n              <div>\n                <p className=\"text-sm text-muted-foreground\">Active Goals</p>\n                <p className=\"text-lg font-semibold\">{employee.activeGoals.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Award className=\"h-5 w-5 text-green-500 dark:text-green-400\" />\n              <div>\n                <p className=\"text-sm text-muted-foreground\">Skills</p>\n                <p className=\"text-lg font-semibold\">{employee.skills?.length || 0}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Clock className=\"h-5 w-5 text-purple-500 dark:text-purple-400\" />\n              <div>\n                <p className=\"text-sm text-muted-foreground\">Tenure</p>\n                <p className=\"text-lg font-semibold\">\n                  {Math.floor((Date.now() - new Date(employee.hireDate).getTime()) / (1000 * 60 * 60 * 24 * 365))} years\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Detailed Information Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid w-full grid-cols-5\">\n          <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n          <TabsTrigger value=\"skills\">Skills</TabsTrigger>\n          <TabsTrigger value=\"performance\">Performance</TabsTrigger>\n          <TabsTrigger value=\"goals\">Goals</TabsTrigger>\n          <TabsTrigger value=\"documents\">Documents</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"overview\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Personal Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <User className=\"h-5 w-5\" />\n                  Personal Information\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <p className=\"text-muted-foreground\">Date of Birth</p>\n                    <p className=\"font-medium\">\n                      {employee.dateOfBirth ? new Date(employee.dateOfBirth).toLocaleDateString() : 'Not provided'}\n                    </p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Gender</p>\n                    <p className=\"font-medium\">{employee.gender || 'Not specified'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Personal Email</p>\n                    <p className=\"font-medium\">{employee.personalEmail || 'Not provided'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Phone</p>\n                    <p className=\"font-medium\">{employee.phone}</p>\n                  </div>\n                </div>\n                {employee.address && (\n                  <div>\n                    <p className=\"text-muted-foreground text-sm\">Address</p>\n                    <p className=\"font-medium\">\n                      {[\n                        employee.address.street,\n                        employee.address.city,\n                        employee.address.state,\n                        employee.address.zipCode,\n                        employee.address.country\n                      ].filter(Boolean).join(', ') || 'Not provided'}\n                    </p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Employment Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Briefcase className=\"h-5 w-5\" />\n                  Employment Details\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <p className=\"text-muted-foreground\">Employee ID</p>\n                    <p className=\"font-medium\">{employee.id}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Hire Date</p>\n                    <p className=\"font-medium\">{new Date(employee.hireDate).toLocaleDateString()}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Employment Type</p>\n                    <p className=\"font-medium\">{employee.employeeType.replace('_', ' ')}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Work Location</p>\n                    <p className=\"font-medium\">{employee.workLocation}</p>\n                  </div>\n                  {employee.salary && (\n                    <div>\n                      <p className=\"text-muted-foreground\">Salary</p>\n                      <p className=\"font-medium\">{employee.currency} {employee.salary.toLocaleString()}</p>\n                    </div>\n                  )}\n                  <div>\n                    <p className=\"text-muted-foreground\">Manager</p>\n                    <p className=\"font-medium\">{employee.managerId || 'Not assigned'}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Emergency Contact */}\n          {employee.emergencyContact && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Shield className=\"h-5 w-5\" />\n                  Emergency Contact\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                  <div>\n                    <p className=\"text-muted-foreground\">Name</p>\n                    <p className=\"font-medium\">{employee.emergencyContact.name || 'Not provided'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Relationship</p>\n                    <p className=\"font-medium\">{employee.emergencyContact.relationship || 'Not provided'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Phone</p>\n                    <p className=\"font-medium\">{employee.emergencyContact.phone || 'Not provided'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">Email</p>\n                    <p className=\"font-medium\">{employee.emergencyContact.email || 'Not provided'}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"skills\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Skills & Competencies</CardTitle>\n              <CardDescription>Employee skills and proficiency levels</CardDescription>\n            </CardHeader>\n            <CardContent>\n              {employee.skills && employee.skills.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {employee.skills.map((skill) => (\n                    <div key={skill.id} className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"font-medium\">{skill.name}</span>\n                        <Badge variant=\"outline\">{skill.category}</Badge>\n                      </div>\n                      <Progress value={skill.level * 20} className=\"h-2\" />\n                      <p className=\"text-xs text-muted-foreground\">Level {skill.level}/5</p>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-muted-foreground text-center py-8\">No skills recorded yet.</p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"performance\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Performance Reviews</CardTitle>\n              <CardDescription>Recent performance evaluations and feedback</CardDescription>\n            </CardHeader>\n            <CardContent>\n              {employee.recentReviews && employee.recentReviews.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {employee.recentReviews.map((review) => (\n                    <div key={review.id} className=\"p-4 border rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"font-medium\">Review by {review.reviewer}</span>\n                          <div className=\"flex items-center\">\n                            {[...Array(5)].map((_, i) => (\n                              <Star\n                                key={i}\n                                className={`h-4 w-4 ${\n                                  i < review.rating ? 'text-yellow-500 fill-current' : 'text-muted-foreground'\n                                }`}\n                              />\n                            ))}\n                          </div>\n                        </div>\n                        <span className=\"text-sm text-muted-foreground\">\n                          {new Date(review.reviewDate).toLocaleDateString()}\n                        </span>\n                      </div>\n                      <p className=\"text-foreground\">{review.comments}</p>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-muted-foreground text-center py-8\">No performance reviews yet.</p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"goals\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Active Goals</CardTitle>\n              <CardDescription>Current objectives and progress tracking</CardDescription>\n            </CardHeader>\n            <CardContent>\n              {employee.activeGoals && employee.activeGoals.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {employee.activeGoals.map((goal) => (\n                    <div key={goal.id} className=\"p-4 border rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium\">{goal.title}</h4>\n                        <Badge variant={goal.status === 'on_track' ? 'default' : 'secondary'}>\n                          {goal.status.replace('_', ' ')}\n                        </Badge>\n                      </div>\n                      <p className=\"text-muted-foreground text-sm mb-3\">{goal.description}</p>\n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span>Progress</span>\n                          <span>{goal.progress}%</span>\n                        </div>\n                        <Progress value={goal.progress} className=\"h-2\" />\n                        <p className=\"text-xs text-muted-foreground\">\n                          Due: {new Date(goal.dueDate).toLocaleDateString()}\n                        </p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-muted-foreground text-center py-8\">No active goals set.</p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"documents\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Documents & Certifications</CardTitle>\n              <CardDescription>Employee documents and certifications</CardDescription>\n            </CardHeader>\n            <CardContent>\n              {employee.certifications && employee.certifications.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {employee.certifications.map((cert, index) => (\n                    <div key={index} className=\"p-4 border rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium\">{cert.name}</h4>\n                        <Badge variant=\"outline\">{cert.issuer}</Badge>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-4 text-sm text-muted-foreground\">\n                        {cert.issueDate && (\n                          <div>\n                            <span className=\"text-muted-foreground\">Issued:</span> {new Date(cert.issueDate).toLocaleDateString()}\n                          </div>\n                        )}\n                        {cert.expiryDate && (\n                          <div>\n                            <span className=\"text-muted-foreground\">Expires:</span> {new Date(cert.expiryDate).toLocaleDateString()}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-muted-foreground text-center py-8\">No certifications recorded.</p>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAvCA;;;;;;;;;;;;AAuEO,SAAS,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAwB;;IACnF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB;QAC1B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YACtD,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,WAAW;YAC3D,IAAI,aAAa;gBACf,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,gDAAgD;YAChD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,8CAA8C;YAC9C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,MAAM,WAAW,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;IAC7D,MAAM,gBAAgB,SAAS,aAAa,CAAC,MAAM,GAAG,IAClD,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE,KAAK,SAAS,aAAa,CAAC,MAAM,GACtG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAGV,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,SAAS,MAAM;gDAAE,KAAK;;;;;;0DACxC,6LAAC,qIAAA,CAAA,iBAAc;gDAAC,WAAU;;oDACvB,SAAS,SAAS,CAAC,EAAE;oDAAE,SAAS,QAAQ,CAAC,EAAE;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAE,WAAU;kEAAiC,SAAS,UAAU;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAiC,SAAS,YAAY;;;;;;;;;;;;0DAErE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAM,SAAS,KAAK;;;;;;;;;;;;kEAEvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,SAAS,KAAK;;;;;;;;;;;;kEAEvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;;oEAAK;oEAAQ,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAS,SAAS,MAAM,KAAK,WAAW,YAAY;kEACxD,SAAS,MAAM;;;;;;kEAElB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,SAAS,YAAY,CAAC,OAAO,CAAC,KAAK;;;;;;kEAC7D,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAIrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAGtC,wBACC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAS;;0DACzB,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;;oDAAyB,cAAc,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKvE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAyB,SAAS,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKzE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAyB,SAAS,MAAM,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKzE,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;;oDACV,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,SAAS,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,KAAK,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5G,6LAAC,mIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAc;;;;;;0CACjC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAQ;;;;;;0CAC3B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAGjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIhC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFACV,SAAS,WAAW,GAAG,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;0EAGlF,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFAAe,SAAS,MAAM,IAAI;;;;;;;;;;;;0EAEjD,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFAAe,SAAS,aAAa,IAAI;;;;;;;;;;;;0EAExD,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFAAe,SAAS,KAAK;;;;;;;;;;;;;;;;;;oDAG7C,SAAS,OAAO,kBACf,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EACV;oEACC,SAAS,OAAO,CAAC,MAAM;oEACvB,SAAS,OAAO,CAAC,IAAI;oEACrB,SAAS,OAAO,CAAC,KAAK;oEACtB,SAAS,OAAO,CAAC,OAAO;oEACxB,SAAS,OAAO,CAAC,OAAO;iEACzB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,+MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIrC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,SAAS,EAAE;;;;;;;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;sEAE5E,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,SAAS,YAAY,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sEAEjE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,SAAS,YAAY;;;;;;;;;;;;wDAElD,SAAS,MAAM,kBACd,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAe,SAAS,QAAQ;wEAAC;wEAAE,SAAS,MAAM,CAAC,cAAc;;;;;;;;;;;;;sEAGlF,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAe,SAAS,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ3D,SAAS,gBAAgB,kBACxB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIlC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAe,SAAS,gBAAgB,CAAC,IAAI,IAAI;;;;;;;;;;;;8DAEhE,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAe,SAAS,gBAAgB,CAAC,YAAY,IAAI;;;;;;;;;;;;8DAExE,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAe,SAAS,gBAAgB,CAAC,KAAK,IAAI;;;;;;;;;;;;8DAEjE,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAe,SAAS,gBAAgB,CAAC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3E,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACT,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,kBAC3C,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,sBACpB,6LAAC;gDAAmB,WAAU;;kEAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAe,MAAM,IAAI;;;;;;0EACzC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,MAAM,QAAQ;;;;;;;;;;;;kEAE1C,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,OAAO,MAAM,KAAK,GAAG;wDAAI,WAAU;;;;;;kEAC7C,6LAAC;wDAAE,WAAU;;4DAAgC;4DAAO,MAAM,KAAK;4DAAC;;;;;;;;+CANxD,MAAM,EAAE;;;;;;;;;6DAWtB,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;kCAM9D,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCACzC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACT,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,kBACzD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,uBAC3B,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EAAc;4EAAW,OAAO,QAAQ;;;;;;;kFACxD,6LAAC;wEAAI,WAAU;kFACZ;+EAAI,MAAM;yEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;gFAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,OAAO,MAAM,GAAG,iCAAiC,yBACrD;+EAHG;;;;;;;;;;;;;;;;0EAQb,6LAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;;;;;;;;;;;;kEAGnD,6LAAC;wDAAE,WAAU;kEAAmB,OAAO,QAAQ;;;;;;;+CAnBvC,OAAO,EAAE;;;;;;;;;6DAwBvB,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;kCAM9D,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACT,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,kBACrD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;gDAAkB,WAAU;;kEAC3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAe,KAAK,KAAK;;;;;;0EACvC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,MAAM,KAAK,aAAa,YAAY;0EACtD,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kEAG9B,6LAAC;wDAAE,WAAU;kEAAsC,KAAK,WAAW;;;;;;kEACnE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,KAAK,QAAQ;4EAAC;;;;;;;;;;;;;0EAEvB,6LAAC,uIAAA,CAAA,WAAQ;gEAAC,OAAO,KAAK,QAAQ;gEAAE,WAAU;;;;;;0EAC1C,6LAAC;gEAAE,WAAU;;oEAAgC;oEACrC,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;;+CAf3C,KAAK,EAAE;;;;;;;;;6DAsBrB,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;kCAM9D,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACT,SAAS,cAAc,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,kBAC3D,6LAAC;wCAAI,WAAU;kDACZ,SAAS,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAe,KAAK,IAAI;;;;;;0EACtC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,KAAK,MAAM;;;;;;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,SAAS,kBACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;oEAAc;oEAAE,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;4DAGtG,KAAK,UAAU,kBACd,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;oEAAe;oEAAE,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;+CAbnG;;;;;;;;;6DAqBd,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;GA/cgB;KAAA", "debugId": null}}, {"offset": {"line": 6012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport {\n  Search,\n  Bell,\n  MessageSquare,\n  Settings,\n  User,\n  LogOut,\n  Moon,\n  Sun,\n  Globe,\n  ChevronDown,\n  Plus,\n  Filter,\n  Download,\n  RefreshCw\n} from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { cn } from \"@/lib/utils\"\n\ninterface HeaderProps {\n  title?: string\n  subtitle?: string\n  actions?: React.ReactNode\n  className?: string\n}\n\nexport function Header({ title, subtitle, actions, className }: HeaderProps) {\n  const [showNotifications, setShowNotifications] = useState(false)\n  const [showProfile, setShowProfile] = useState(false)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  const notifications = [\n    {\n      id: 1,\n      title: \"New Employee Onboarding\",\n      message: \"<PERSON> has completed her onboarding checklist\",\n      time: \"2 minutes ago\",\n      unread: true,\n      type: \"success\"\n    },\n    {\n      id: 2,\n      title: \"Leave Request Pending\",\n      message: \"Mike <PERSON> has requested 3 days of annual leave\",\n      time: \"1 hour ago\",\n      unread: true,\n      type: \"warning\"\n    },\n    {\n      id: 3,\n      title: \"Performance Review Due\",\n      message: \"5 performance reviews are due this week\",\n      time: \"3 hours ago\",\n      unread: false,\n      type: \"info\"\n    }\n  ]\n\n  const quickActions = [\n    { name: \"Add Employee\", icon: Plus, action: () => {} },\n    { name: \"Export Data\", icon: Download, action: () => {} },\n    { name: \"Refresh\", icon: RefreshCw, action: () => {} },\n    { name: \"Filter\", icon: Filter, action: () => {} },\n  ]\n\n  return (\n    <header className={cn(\n      \"sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm\",\n      className\n    )}>\n      <div className=\"flex h-16 items-center justify-between px-6\">\n        {/* Left Section - Title and Breadcrumb */}\n        <div className=\"flex items-center space-x-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-xl font-semibold text-foreground\">{title}</h1>\n            )}\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground\">{subtitle}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-8\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              type=\"text\"\n              placeholder=\"Search employees, documents, or anything...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors\"\n            />\n          </div>\n        </div>\n\n        {/* Right Section - Actions and Profile */}\n        <div className=\"flex items-center space-x-3\">\n          {/* Quick Actions */}\n          {actions && (\n            <div className=\"flex items-center space-x-2 mr-4\">\n              {actions}\n            </div>\n          )}\n\n          {/* Default Quick Actions */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {quickActions.map((action) => (\n              <Button\n                key={action.name}\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={action.action}\n                className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n                title={action.name}\n              >\n                <action.icon className=\"h-4 w-4\" />\n              </Button>\n            ))}\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"h-9 w-9 text-muted-foreground hover:text-foreground relative\"\n            >\n              <Bell className=\"h-4 w-4\" />\n              {notifications.some(n => n.unread) && (\n                <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full\" />\n              )}\n            </Button>\n\n            <AnimatePresence>\n              {showNotifications && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-80 z-50\"\n                >\n                  <Card className=\"shadow-lg border-border\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-border\">\n                        <div className=\"flex items-center justify-between\">\n                          <h3 className=\"font-semibold text-foreground\">Notifications</h3>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {notifications.filter(n => n.unread).length} new\n                          </Badge>\n                        </div>\n                      </div>\n                      <div className=\"max-h-80 overflow-y-auto\">\n                        {notifications.map((notification) => (\n                          <div\n                            key={notification.id}\n                            className={cn(\n                              \"p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors\",\n                              notification.unread && \"bg-primary/5\"\n                            )}\n                          >\n                            <div className=\"flex items-start space-x-3\">\n                              <div className={cn(\n                                \"w-2 h-2 rounded-full mt-2 flex-shrink-0\",\n                                notification.type === \"success\" && \"bg-green-500\",\n                                notification.type === \"warning\" && \"bg-yellow-500\",\n                                notification.type === \"info\" && \"bg-primary\"\n                              )} />\n                              <div className=\"flex-1 min-w-0\">\n                                <p className=\"text-sm font-medium text-foreground\">\n                                  {notification.title}\n                                </p>\n                                <p className=\"text-sm text-muted-foreground mt-1\">\n                                  {notification.message}\n                                </p>\n                                <p className=\"text-xs text-muted-foreground/70 mt-2\">\n                                  {notification.time}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                      <div className=\"p-3 border-t border-border\">\n                        <Button variant=\"ghost\" className=\"w-full text-sm\">\n                          View all notifications\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Messages */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-9 w-9 text-muted-foreground hover:text-foreground\"\n          >\n            <MessageSquare className=\"h-4 w-4\" />\n          </Button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\">\n            <Button\n              variant=\"ghost\"\n              onClick={() => setShowProfile(!showProfile)}\n              className=\"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80\"\n            >\n              <Avatar className=\"h-7 w-7\">\n                <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                <AvatarFallback>JD</AvatarFallback>\n              </Avatar>\n              <ChevronDown className=\"h-3 w-3\" />\n            </Button>\n\n            <AnimatePresence>\n              {showProfile && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"absolute right-0 mt-2 w-56 z-50\"\n                >\n                  <Card className=\"shadow-lg border-gray-200\">\n                    <CardContent className=\"p-0\">\n                      <div className=\"p-4 border-b border-gray-200\">\n                        <div className=\"flex items-center space-x-3\">\n                          <Avatar className=\"h-10 w-10\">\n                            <AvatarImage src=\"/avatars/user.jpg\" alt=\"User\" />\n                            <AvatarFallback>JD</AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"font-medium text-foreground\">John Doe</p>\n                            <p className=\"text-sm text-muted-foreground\">HR Manager</p>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"py-2\">\n                        {[\n                          { icon: User, label: \"Profile\", href: \"/dashboard\" },\n                          { icon: Settings, label: \"Settings\", href: \"/dashboard\" },\n                          { icon: Moon, label: \"Dark Mode\", href: \"#\" },\n                          { icon: Globe, label: \"Language\", href: \"#\" },\n                        ].map((item) => (\n                          <button\n                            key={item.label}\n                            className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors\"\n                          >\n                            <item.icon className=\"h-4 w-4\" />\n                            <span>{item.label}</span>\n                          </button>\n                        ))}\n                        <hr className=\"my-2\" />\n                        <button className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors\">\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign out</span>\n                        </button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;AAkCO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAe;;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAe,MAAM,6MAAA,CAAA,WAAQ;YAAE,QAAQ,KAAO;QAAE;QACxD;YAAE,MAAM;YAAW,MAAM,mNAAA,CAAA,YAAS;YAAE,QAAQ,KAAO;QAAE;QACrD;YAAE,MAAM;YAAU,MAAM,yMAAA,CAAA,SAAM;YAAE,QAAQ,KAAO;QAAE;KAClD;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,kFACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;4BACE,uBACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;4BAExD,0BACC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;8BAMpD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;wBAEZ,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,MAAK;oCACL,SAAS,OAAO,MAAM;oCACtB,WAAU;oCACV,OAAO,OAAO,IAAI;8CAElB,cAAA,6LAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;mCAPlB,OAAO,IAAI;;;;;;;;;;sCAatB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,mBAC/B,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAIpB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAgC;;;;;;8EAC9C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAClC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;wEAAC;;;;;;;;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;gEAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA,aAAa,MAAM,IAAI;0EAGzB,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2CACA,aAAa,IAAI,KAAK,aAAa,gBACnC,aAAa,IAAI,KAAK,aAAa,iBACnC,aAAa,IAAI,KAAK,UAAU;;;;;;sFAElC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FACV,aAAa,KAAK;;;;;;8FAErB,6LAAC;oFAAE,WAAU;8FACV,aAAa,OAAO;;;;;;8FAEvB,6LAAC;oFAAE,WAAU;8FACV,aAAa,IAAI;;;;;;;;;;;;;;;;;;+DArBnB,aAAa,EAAE;;;;;;;;;;kEA4B1B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYjE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAI3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAoB,KAAI;;;;;;8DACzC,6LAAC,qIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;sDAElB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAGzB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCAC1C,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAK;wCACvC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,6LAAC,qIAAA,CAAA,cAAW;4EAAC,KAAI;4EAAoB,KAAI;;;;;;sFACzC,6LAAC,qIAAA,CAAA,iBAAc;sFAAC;;;;;;;;;;;;8EAElB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA8B;;;;;;sFAC3C,6LAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAI,WAAU;;4DACZ;gEACC;oEAAE,MAAM,qMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAW,MAAM;gEAAa;gEACnD;oEAAE,MAAM,6MAAA,CAAA,WAAQ;oEAAE,OAAO;oEAAY,MAAM;gEAAa;gEACxD;oEAAE,MAAM,qMAAA,CAAA,OAAI;oEAAE,OAAO;oEAAa,MAAM;gEAAI;gEAC5C;oEAAE,MAAM,uMAAA,CAAA,QAAK;oEAAE,OAAO;oEAAY,MAAM;gEAAI;6DAC7C,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;oEAEC,WAAU;;sFAEV,6LAAC,KAAK,IAAI;4EAAC,WAAU;;;;;;sFACrB,6LAAC;sFAAM,KAAK,KAAK;;;;;;;mEAJZ,KAAK,KAAK;;;;;0EAOnB,6LAAC;gEAAG,WAAU;;;;;;0EACd,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;GAzPgB;KAAA", "debugId": null}}, {"offset": {"line": 6698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/dashboard/employees/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Search,\n  Filter,\n  Plus,\n  Download,\n  MoreHorizontal,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Briefcase,\n  Star,\n  Edit,\n  Trash2,\n  Eye,\n  Grid3X3,\n  List,\n  SortAsc,\n  SortDesc,\n  Users,\n  UserCheck,\n  UserX,\n  Clock,\n  DollarSign,\n  TrendingUp,\n  FileText,\n  Send,\n  CheckSquare,\n  X,\n  RefreshCw,\n  Settings,\n  Loader2,\n  AlertCircle\n} from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { TouchButton, SwipeableCard, PullToRefresh, FloatingActionButton } from \"@/components/ui/touch-friendly\"\nimport { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { EmployeeForm } from \"@/components/employees/EmployeeForm\"\nimport { EmployeeProfile } from \"@/components/employees/EmployeeProfile\"\nimport { employeeApi, type Employee, type EmployeeFilters, handleApiResponse, withLoading } from \"@/lib/api/employeeApi\"\nimport { toast } from \"sonner\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Header } from \"@/components/layout/header\"\n\n// Mock employee data\nconst employees = [\n  {\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"Senior Software Engineer\",\n    department: \"Engineering\",\n    location: \"San Francisco, CA\",\n    startDate: \"2022-03-15\",\n    status: \"active\",\n    avatar: \"/avatars/sarah.jpg\",\n    performance: 4.8,\n    salary: 125000\n  },\n  {\n    id: 2,\n    name: \"Mike Chen\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"Product Manager\",\n    department: \"Product\",\n    location: \"New York, NY\",\n    startDate: \"2021-08-20\",\n    status: \"active\",\n    avatar: \"/avatars/mike.jpg\",\n    performance: 4.6,\n    salary: 115000\n  },\n  {\n    id: 3,\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"UX Designer\",\n    department: \"Design\",\n    location: \"Austin, TX\",\n    startDate: \"2023-01-10\",\n    status: \"active\",\n    avatar: \"/avatars/emily.jpg\",\n    performance: 4.9,\n    salary: 95000\n  },\n  {\n    id: 4,\n    name: \"David Wilson\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"DevOps Engineer\",\n    department: \"Engineering\",\n    location: \"Seattle, WA\",\n    startDate: \"2022-11-05\",\n    status: \"on-leave\",\n    avatar: \"/avatars/david.jpg\",\n    performance: 4.4,\n    salary: 110000\n  },\n  {\n    id: 5,\n    name: \"Lisa Rodriguez\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"HR Manager\",\n    department: \"Human Resources\",\n    location: \"Los Angeles, CA\",\n    startDate: \"2020-06-12\",\n    status: \"active\",\n    avatar: \"/avatars/lisa.jpg\",\n    performance: 4.7,\n    salary: 105000\n  },\n  {\n    id: 6,\n    name: \"James Thompson\",\n    email: \"<EMAIL>\",\n    phone: \"+****************\",\n    position: \"Sales Director\",\n    department: \"Sales\",\n    location: \"Chicago, IL\",\n    startDate: \"2019-04-18\",\n    status: \"active\",\n    avatar: \"/avatars/james.jpg\",\n    performance: 4.5,\n    salary: 135000\n  }\n]\n\nconst departments = [\"All\", \"Engineering\", \"Product\", \"Design\", \"Human Resources\", \"Sales\", \"Marketing\", \"Finance\"]\nconst statuses = [\"All\", \"active\", \"on-leave\", \"inactive\"]\n\nexport default function EmployeesPage() {\n  // Data state\n  const [employees, setEmployees] = useState<Employee[]>([])\n  const [departments, setDepartments] = useState<Array<{ id: string; name: string }>>([])\n  const [positions, setPositions] = useState<Array<{ id: string; title: string; departmentId: string }>>([])\n  const [managers, setManagers] = useState<Array<{ id: string; name: string; departmentId: string }>>([])\n  const [loading, setLoading] = useState(true)\n  const [totalEmployees, setTotalEmployees] = useState(0)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [totalPages, setTotalPages] = useState(1)\n\n  // Filter and search state\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedDepartment, setSelectedDepartment] = useState(\"All\")\n  const [selectedStatus, setSelectedStatus] = useState(\"All\")\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\")\n  const [sortBy, setSortBy] = useState<\"firstName\" | \"department\" | \"hireDate\">(\"firstName\")\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\")\n  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])\n  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)\n  const [performanceFilter, setPerformanceFilter] = useState<\"all\" | \"high\" | \"medium\" | \"low\">(\"all\")\n  const [salaryRange, setSalaryRange] = useState<[number, number]>([0, 200000])\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const [showBulkActions, setShowBulkActions] = useState(false)\n\n  // Modal state\n  const [showEmployeeForm, setShowEmployeeForm] = useState(false)\n  const [showEmployeeProfile, setShowEmployeeProfile] = useState(false)\n  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null)\n  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)\n\n  // Load initial data\n  useEffect(() => {\n    loadInitialData()\n  }, [])\n\n  // Load employees when filters change\n  useEffect(() => {\n    loadEmployees()\n  }, [searchQuery, selectedDepartment, selectedStatus, sortBy, sortOrder, currentPage])\n\n  const loadInitialData = async () => {\n    setLoading(true)\n    try {\n      const [deptResponse, posResponse, mgrResponse] = await Promise.all([\n        employeeApi.getDepartments(),\n        employeeApi.getPositions(),\n        employeeApi.getManagers()\n      ])\n\n      const deptData = handleApiResponse(deptResponse)\n      const posData = handleApiResponse(posResponse)\n      const mgrData = handleApiResponse(mgrResponse)\n\n      if (deptData) setDepartments([{ id: \"All\", name: \"All Departments\" }, ...deptData])\n      if (posData) setPositions(posData)\n      if (mgrData) setManagers(mgrData)\n\n      await loadEmployees()\n    } catch (error) {\n      console.error(\"Failed to load initial data:\", error)\n      toast.error(\"Failed to load employee data\")\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadEmployees = async () => {\n    const filters: EmployeeFilters = {\n      page: currentPage,\n      limit: 20,\n      search: searchQuery || undefined,\n      department: selectedDepartment !== \"All\" ? selectedDepartment : undefined,\n      status: selectedStatus !== \"All\" ? selectedStatus.toLowerCase() as any : undefined,\n      sortBy: sortBy,\n      sortOrder: sortOrder\n    }\n\n    const response = await employeeApi.getEmployees(filters)\n    const data = handleApiResponse(response)\n\n    if (data) {\n      setEmployees(data.employees)\n      setTotalEmployees(data.total)\n      setTotalPages(data.totalPages)\n    }\n  }\n\n  // Filter and sort employees\n  const filteredAndSortedEmployees = employees\n    .filter((employee) => {\n      // Search filter\n      const matchesSearch = !searchQuery ||\n        employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        employee.department.toLowerCase().includes(searchQuery.toLowerCase())\n\n      // Department filter\n      const matchesDepartment = selectedDepartment === \"All\" || employee.department === selectedDepartment\n\n      // Status filter\n      const matchesStatus = selectedStatus === \"All\" || employee.status === selectedStatus.toLowerCase()\n\n      // Performance filter\n      let matchesPerformance = true\n      if (performanceFilter === \"high\") matchesPerformance = employee.performance >= 4.5\n      else if (performanceFilter === \"medium\") matchesPerformance = employee.performance >= 3.5 && employee.performance < 4.5\n      else if (performanceFilter === \"low\") matchesPerformance = employee.performance < 3.5\n\n      // Salary range filter\n      const matchesSalary = employee.salary >= salaryRange[0] && employee.salary <= salaryRange[1]\n\n      return matchesSearch && matchesDepartment && matchesStatus && matchesPerformance && matchesSalary\n    })\n    .sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name)\n          break\n        case \"department\":\n          comparison = a.department.localeCompare(b.department)\n          break\n        case \"performance\":\n          comparison = a.performance - b.performance\n          break\n        case \"startDate\":\n          comparison = new Date(a.startDate).getTime() - new Date(b.startDate).getTime()\n          break\n      }\n      return sortOrder === \"asc\" ? comparison : -comparison\n    })\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"active\": return \"success\"\n      case \"on-leave\": return \"warning\"\n      case \"inactive\": return \"destructive\"\n      default: return \"secondary\"\n    }\n  }\n\n  const getPerformanceColor = (rating: number) => {\n    if (rating >= 4.5) return \"text-green-600 dark:text-green-400\"\n    if (rating >= 4.0) return \"text-blue-600 dark:text-blue-400\"\n    if (rating >= 3.5) return \"text-yellow-600 dark:text-yellow-400\"\n    return \"text-red-600 dark:text-red-400\"\n  }\n\n  const handleSelectAll = () => {\n    if (selectedEmployees.length === filteredAndSortedEmployees.length) {\n      setSelectedEmployees([])\n    } else {\n      setSelectedEmployees(filteredAndSortedEmployees.map(emp => emp.id))\n    }\n  }\n\n  const handleSelectEmployee = (id: string) => {\n    setSelectedEmployees(prev =>\n      prev.includes(id)\n        ? prev.filter(empId => empId !== id)\n        : [...prev, id]\n    )\n  }\n\n  const handleViewEmployee = (employeeId: string) => {\n    setSelectedEmployeeId(employeeId)\n    setShowEmployeeProfile(true)\n  }\n\n  const handleEditEmployee = (employee: Employee) => {\n    setEditingEmployee(employee)\n    setShowEmployeeForm(true)\n  }\n\n  const handleAddEmployee = () => {\n    setEditingEmployee(null)\n    setShowEmployeeForm(true)\n  }\n\n  const handleDeleteEmployee = async (employeeId: string) => {\n    if (confirm(\"Are you sure you want to delete this employee?\")) {\n      const response = await employeeApi.deleteEmployee(employeeId)\n      if (handleApiResponse(response, \"Employee deleted successfully\")) {\n        await loadEmployees()\n      }\n    }\n  }\n\n  const handleBulkAction = async (action: string) => {\n    try {\n      switch (action) {\n        case 'delete':\n          if (confirm(`Are you sure you want to delete ${selectedEmployees.length} employees?`)) {\n            // Implement bulk delete\n            toast.success(`${selectedEmployees.length} employees deleted`)\n          }\n          break\n        case 'export':\n          const response = await employeeApi.exportEmployees({\n            search: selectedEmployees.join(',')\n          })\n          if (handleApiResponse(response, \"Export started\")) {\n            // Handle export\n          }\n          break\n        default:\n          console.log(`Performing ${action} on employees:`, selectedEmployees)\n      }\n    } catch (error) {\n      toast.error(\"Bulk action failed\")\n    } finally {\n      setSelectedEmployees([])\n      setShowBulkActions(false)\n    }\n  }\n\n  const toggleSort = (field: typeof sortBy) => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\n    } else {\n      setSortBy(field)\n      setSortOrder(\"asc\")\n    }\n  }\n\n  const handleRefresh = async () => {\n    await withLoading(loadEmployees, setIsRefreshing)\n  }\n\n  const handleSwipeLeft = (employeeId: string) => {\n    handleDeleteEmployee(employeeId)\n  }\n\n  const handleSwipeRight = (employeeId: string) => {\n    // Implement archive functionality\n    toast.info(\"Archive functionality coming soon\")\n  }\n\n  const handleEmployeeFormSubmit = async (data: any) => {\n    try {\n      if (editingEmployee) {\n        const response = await employeeApi.updateEmployee(editingEmployee.id, data)\n        if (handleApiResponse(response, \"Employee updated successfully\")) {\n          setShowEmployeeForm(false)\n          setEditingEmployee(null)\n          await loadEmployees()\n        }\n      } else {\n        const response = await employeeApi.createEmployee(data)\n        if (handleApiResponse(response, \"Employee created successfully\")) {\n          setShowEmployeeForm(false)\n          await loadEmployees()\n        }\n      }\n    } catch (error) {\n      console.error(\"Form submission error:\", error)\n      toast.error(\"Failed to save employee\")\n    }\n  }\n\n  const handleCloseForm = () => {\n    setShowEmployeeForm(false)\n    setEditingEmployee(null)\n  }\n\n  const handleCloseProfile = () => {\n    setShowEmployeeProfile(false)\n    setSelectedEmployeeId(null)\n  }\n\n  return (\n    <PullToRefresh onRefresh={handleRefresh} className=\"flex-1\">\n      <div className=\"space-y-6 p-6\">\n      <Header\n        title=\"Employee Management\"\n        subtitle={`Managing ${totalEmployees} employees across ${departments.length - 1} departments`}\n        actions={\n          <div className=\"flex items-center space-x-2\">\n            {selectedEmployees.length > 0 && (\n              <div className=\"flex items-center space-x-2 mr-4\">\n                <span className=\"text-sm text-muted-foreground\">\n                  {selectedEmployees.length} selected\n                </span>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setShowBulkActions(!showBulkActions)}\n                >\n                  <CheckSquare className=\"w-4 h-4 mr-2\" />\n                  Bulk Actions\n                </Button>\n              </div>\n            )}\n            <TouchButton\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handleRefresh}\n              disabled={isRefreshing}\n            >\n              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />\n              {isRefreshing ? 'Refreshing...' : 'Refresh'}\n            </TouchButton>\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"w-4 h-4 mr-2\" />\n              Export\n            </Button>\n            <Button size=\"sm\" onClick={handleAddEmployee}>\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Employee\n            </Button>\n          </div>\n        }\n      />\n\n      {/* Bulk Actions Panel */}\n      {showBulkActions && selectedEmployees.length > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm font-medium text-blue-900\">\n                Bulk Actions for {selectedEmployees.length} employees:\n              </span>\n              <div className=\"flex space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleBulkAction('send-email')}\n                >\n                  <Send className=\"w-4 h-4 mr-2\" />\n                  Send Email\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleBulkAction('export-data')}\n                >\n                  <FileText className=\"w-4 h-4 mr-2\" />\n                  Export Data\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleBulkAction('update-status')}\n                >\n                  <UserCheck className=\"w-4 h-4 mr-2\" />\n                  Update Status\n                </Button>\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setShowBulkActions(false)}\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Filters and Search */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Search employees by name, email, position, or department...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedDepartment}\n                onChange={(e) => setSelectedDepartment(e.target.value)}\n                className=\"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n                aria-label=\"Filter by department\"\n              >\n                {departments.map(dept => (\n                  <option key={dept} value={dept}>{dept} Department</option>\n                ))}\n              </select>\n              <select\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n                className=\"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n                aria-label=\"Filter by status\"\n              >\n                {statuses.map(status => (\n                  <option key={status} value={status}>\n                    {status === \"All\" ? \"All Status\" : status.charAt(0).toUpperCase() + status.slice(1)}\n                  </option>\n                ))}\n              </select>\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}\n                className={showAdvancedFilters ? \"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800\" : \"\"}\n              >\n                <Filter className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={() => toggleSort(sortBy)}\n              >\n                {sortOrder === \"asc\" ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />}\n              </Button>\n            </div>\n          </div>\n\n          {/* Advanced Filters */}\n          {showAdvancedFilters && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"mt-4 pt-4 border-t border-gray-200\"\n            >\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-2\">\n                    Sort By\n                  </label>\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as typeof sortBy)}\n                    className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n                    aria-label=\"Sort by field\"\n                  >\n                    <option value=\"name\">Name</option>\n                    <option value=\"department\">Department</option>\n                    <option value=\"performance\">Performance</option>\n                    <option value=\"startDate\">Start Date</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-2\">\n                    Performance Level\n                  </label>\n                  <select\n                    value={performanceFilter}\n                    onChange={(e) => setPerformanceFilter(e.target.value as typeof performanceFilter)}\n                    className=\"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary\"\n                    aria-label=\"Filter by performance level\"\n                  >\n                    <option value=\"all\">All Performance Levels</option>\n                    <option value=\"high\">High (4.5+)</option>\n                    <option value=\"medium\">Medium (3.5-4.4)</option>\n                    <option value=\"low\">Low (&lt;3.5)</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-foreground mb-2\">\n                    Salary Range\n                  </label>\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      type=\"number\"\n                      placeholder=\"Min\"\n                      value={salaryRange[0]}\n                      onChange={(e) => setSalaryRange([parseInt(e.target.value) || 0, salaryRange[1]])}\n                      className=\"w-20\"\n                    />\n                    <span className=\"text-muted-foreground\">-</span>\n                    <Input\n                      type=\"number\"\n                      placeholder=\"Max\"\n                      value={salaryRange[1]}\n                      onChange={(e) => setSalaryRange([salaryRange[0], parseInt(e.target.value) || 200000])}\n                      className=\"w-20\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Users className=\"h-8 w-8 text-blue-600 dark:text-blue-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">{employees.length}</p>\n                <p className=\"text-sm text-muted-foreground\">Total Employees</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <UserCheck className=\"h-8 w-8 text-green-600 dark:text-green-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">\n                  {employees.filter(emp => emp.status === \"active\").length}\n                </p>\n                <p className=\"text-sm text-muted-foreground\">Active</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Clock className=\"h-8 w-8 text-yellow-600 dark:text-yellow-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">\n                  {employees.filter(emp => emp.status === \"on-leave\").length}\n                </p>\n                <p className=\"text-sm text-muted-foreground\">On Leave</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-8 w-8 text-purple-600 dark:text-purple-400\" />\n              <div>\n                <p className=\"text-2xl font-bold text-foreground\">\n                  {(employees.reduce((sum, emp) => sum + emp.performance, 0) / employees.length).toFixed(1)}\n                </p>\n                <p className=\"text-sm text-muted-foreground\">Avg Performance</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Results Summary */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <p className=\"text-sm text-muted-foreground\">\n            Showing {filteredAndSortedEmployees.length} of {employees.length} employees\n          </p>\n          {selectedEmployees.length > 0 && (\n            <div className=\"flex items-center space-x-2\">\n              <CheckSquare className=\"h-4 w-4 text-blue-600\" />\n              <span className=\"text-sm text-blue-600 font-medium\">\n                {selectedEmployees.length} selected\n              </span>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setSelectedEmployees([])}\n                className=\"text-blue-600 hover:text-blue-700\"\n              >\n                Clear selection\n              </Button>\n            </div>\n          )}\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setViewMode(\"grid\")}\n          >\n            <Grid3X3 className=\"h-4 w-4 mr-2\" />\n            Grid\n          </Button>\n          <Button\n            variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setViewMode(\"list\")}\n          >\n            <List className=\"h-4 w-4 mr-2\" />\n            List\n          </Button>\n        </div>\n      </div>\n\n      {/* Employee Grid */}\n      {viewMode === \"grid\" && (\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredAndSortedEmployees.map((employee, index) => (\n            <motion.div\n              key={employee.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <SwipeableCard\n                onSwipeLeft={() => handleSwipeLeft(employee.id)}\n                onSwipeRight={() => handleSwipeRight(employee.id)}\n                className={`hover:shadow-lg transition-all cursor-pointer ${\n                  selectedEmployees.includes(employee.id) ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20' : ''\n                }`}\n              >\n                <Card className=\"border-0 shadow-none bg-transparent\">\n                <CardHeader className=\"pb-4\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"relative\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedEmployees.includes(employee.id)}\n                          onChange={() => handleSelectEmployee(employee.id)}\n                          className=\"absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                          aria-label={`Select ${employee.name}`}\n                        />\n                        <Avatar className=\"h-12 w-12 ml-6\">\n                          <AvatarImage src={employee.avatar} alt={employee.name} />\n                          <AvatarFallback>\n                            {employee.name.split(' ').map(n => n[0]).join('')}\n                          </AvatarFallback>\n                        </Avatar>\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-foreground\">{employee.name}</h3>\n                        <p className=\"text-sm text-muted-foreground\">{employee.position}</p>\n                      </div>\n                    </div>\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                      <MoreHorizontal className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <Badge variant={getStatusColor(employee.status) as any}>\n                      {employee.status.replace('-', ' ')}\n                    </Badge>\n                    <div className=\"flex items-center space-x-1\">\n                      <Star className={`h-4 w-4 ${getPerformanceColor(employee.performance)}`} />\n                      <span className={`text-sm font-medium ${getPerformanceColor(employee.performance)}`}>\n                        {employee.performance}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2 text-sm text-muted-foreground\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Mail className=\"h-4 w-4\" />\n                      <span className=\"truncate\">{employee.email}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Briefcase className=\"h-4 w-4\" />\n                      <span>{employee.department}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <MapPin className=\"h-4 w-4\" />\n                      <span>{employee.location}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <Calendar className=\"h-4 w-4\" />\n                      <span>Started {new Date(employee.startDate).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <DollarSign className=\"h-4 w-4\" />\n                      <span>${employee.salary.toLocaleString()}</span>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-2 pt-4\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                      <Eye className=\"h-4 w-4 mr-2\" />\n                      View\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                      <Edit className=\"h-4 w-4 mr-2\" />\n                      Edit\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n              </SwipeableCard>\n            </motion.div>\n          ))}\n        </div>\n      )}\n\n      {/* Employee List View */}\n      {viewMode === \"list\" && (\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-muted/50 border-b border-border\">\n                  <tr>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedEmployees.length === filteredAndSortedEmployees.length && filteredAndSortedEmployees.length > 0}\n                        onChange={handleSelectAll}\n                        className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                        aria-label=\"Select all employees\"\n                      />\n                    </th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\" onClick={() => toggleSort(\"name\")}>\n                      <div className=\"flex items-center space-x-1\">\n                        <span>Employee</span>\n                        {sortBy === \"name\" && (sortOrder === \"asc\" ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />)}\n                      </div>\n                    </th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Position</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\" onClick={() => toggleSort(\"department\")}>\n                      <div className=\"flex items-center space-x-1\">\n                        <span>Department</span>\n                        {sortBy === \"department\" && (sortOrder === \"asc\" ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />)}\n                      </div>\n                    </th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Status</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50\" onClick={() => toggleSort(\"performance\")}>\n                      <div className=\"flex items-center space-x-1\">\n                        <span>Performance</span>\n                        {sortBy === \"performance\" && (sortOrder === \"asc\" ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />)}\n                      </div>\n                    </th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Salary</th>\n                    <th className=\"text-left py-3 px-6 font-medium text-foreground\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {filteredAndSortedEmployees.map((employee, index) => (\n                    <motion.tr\n                      key={employee.id}\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: index * 0.05 }}\n                      className={`border-b border-border hover:bg-muted/50 ${\n                        selectedEmployees.includes(employee.id) ? 'bg-blue-50 dark:bg-blue-950/20' : ''\n                      }`}\n                    >\n                      <td className=\"py-4 px-6\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedEmployees.includes(employee.id)}\n                          onChange={() => handleSelectEmployee(employee.id)}\n                          className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                          aria-label={`Select ${employee.name}`}\n                        />\n                      </td>\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex items-center space-x-3\">\n                          <Avatar className=\"h-10 w-10\">\n                            <AvatarImage src={employee.avatar} alt={employee.name} />\n                            <AvatarFallback>\n                              {employee.name.split(' ').map(n => n[0]).join('')}\n                            </AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"font-medium text-foreground\">{employee.name}</p>\n                            <p className=\"text-sm text-muted-foreground\">{employee.email}</p>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-6 text-foreground\">{employee.position}</td>\n                      <td className=\"py-4 px-6 text-muted-foreground\">{employee.department}</td>\n                      <td className=\"py-4 px-6\">\n                        <Badge variant={getStatusColor(employee.status) as any}>\n                          {employee.status.replace('-', ' ')}\n                        </Badge>\n                      </td>\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className={`h-4 w-4 ${getPerformanceColor(employee.performance)}`} />\n                          <span className={`font-medium ${getPerformanceColor(employee.performance)}`}>\n                            {employee.performance}\n                          </span>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-6 text-foreground font-medium\">\n                        ${employee.salary.toLocaleString()}\n                      </td>\n                      <td className=\"py-4 px-6\">\n                        <div className=\"flex space-x-2\">\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Eye className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8 text-red-600\">\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </td>\n                    </motion.tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Floating Action Button for Mobile */}\n      <FloatingActionButton\n        icon={<Plus className=\"h-6 w-6\" />}\n        onClick={handleAddEmployee}\n        position=\"bottom-right\"\n        className=\"lg:hidden\"\n      />\n\n      {/* Employee Form Modal */}\n      <Dialog open={showEmployeeForm} onOpenChange={setShowEmployeeForm}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>\n              {editingEmployee ? \"Edit Employee\" : \"Add New Employee\"}\n            </DialogTitle>\n          </DialogHeader>\n          <EmployeeForm\n            employee={editingEmployee}\n            onSubmit={handleEmployeeFormSubmit}\n            onCancel={handleCloseForm}\n            departments={departments.filter(d => d.id !== \"All\")}\n            positions={positions}\n            managers={managers}\n          />\n        </DialogContent>\n      </Dialog>\n\n      {/* Employee Profile Modal */}\n      <Dialog open={showEmployeeProfile} onOpenChange={setShowEmployeeProfile}>\n        <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>Employee Profile</DialogTitle>\n          </DialogHeader>\n          {selectedEmployeeId && (\n            <EmployeeProfile\n              employeeId={selectedEmployeeId}\n              onEdit={() => {\n                const employee = employees.find(e => e.id === selectedEmployeeId)\n                if (employee) {\n                  setShowEmployeeProfile(false)\n                  handleEditEmployee(employee)\n                }\n              }}\n              onClose={handleCloseProfile}\n            />\n          )}\n        </DialogContent>\n      </Dialog>\n\n      {/* Loading Overlay */}\n      {loading && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n            <Loader2 className=\"h-6 w-6 animate-spin\" />\n            <span>Loading employees...</span>\n          </div>\n        </div>\n      )}\n    </div>\n    </PullToRefresh>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjDA;;;;;;;;;;;;;;;;AAmDA,qBAAqB;AACrB,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,QAAQ;IACV;CACD;AAED,MAAM,cAAc;IAAC;IAAO;IAAe;IAAW;IAAU;IAAmB;IAAS;IAAa;CAAU;AACnH,MAAM,WAAW;IAAC;IAAO;IAAU;IAAY;CAAW;AAE3C,SAAS;;IACtB,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC,EAAE;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8D,EAAE;IACzG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D,EAAE;IACtG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAC9F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAO;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,cAAc;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAExE,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAa;QAAoB;QAAgB;QAAQ;QAAW;KAAY;IAEpF,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,MAAM,CAAC,cAAc,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjE,mIAAA,CAAA,cAAW,CAAC,cAAc;gBAC1B,mIAAA,CAAA,cAAW,CAAC,YAAY;gBACxB,mIAAA,CAAA,cAAW,CAAC,WAAW;aACxB;YAED,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE;YACnC,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE;YAClC,MAAM,UAAU,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE;YAElC,IAAI,UAAU,eAAe;gBAAC;oBAAE,IAAI;oBAAO,MAAM;gBAAkB;mBAAM;aAAS;YAClF,IAAI,SAAS,aAAa;YAC1B,IAAI,SAAS,YAAY;YAEzB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,UAA2B;YAC/B,MAAM;YACN,OAAO;YACP,QAAQ,eAAe;YACvB,YAAY,uBAAuB,QAAQ,qBAAqB;YAChE,QAAQ,mBAAmB,QAAQ,eAAe,WAAW,KAAY;YACzE,QAAQ;YACR,WAAW;QACb;QAEA,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;QAChD,MAAM,OAAO,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE;QAE/B,IAAI,MAAM;YACR,aAAa,KAAK,SAAS;YAC3B,kBAAkB,KAAK,KAAK;YAC5B,cAAc,KAAK,UAAU;QAC/B;IACF;IAEA,4BAA4B;IAC5B,MAAM,6BAA6B,UAChC,MAAM,CAAC,CAAC;QACP,gBAAgB;QAChB,MAAM,gBAAgB,CAAC,eACrB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC7D,SAAS,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEpE,oBAAoB;QACpB,MAAM,oBAAoB,uBAAuB,SAAS,SAAS,UAAU,KAAK;QAElF,gBAAgB;QAChB,MAAM,gBAAgB,mBAAmB,SAAS,SAAS,MAAM,KAAK,eAAe,WAAW;QAEhG,qBAAqB;QACrB,IAAI,qBAAqB;QACzB,IAAI,sBAAsB,QAAQ,qBAAqB,SAAS,WAAW,IAAI;aAC1E,IAAI,sBAAsB,UAAU,qBAAqB,SAAS,WAAW,IAAI,OAAO,SAAS,WAAW,GAAG;aAC/G,IAAI,sBAAsB,OAAO,qBAAqB,SAAS,WAAW,GAAG;QAElF,sBAAsB;QACtB,MAAM,gBAAgB,SAAS,MAAM,IAAI,WAAW,CAAC,EAAE,IAAI,SAAS,MAAM,IAAI,WAAW,CAAC,EAAE;QAE5F,OAAO,iBAAiB,qBAAqB,iBAAiB,sBAAsB;IACtF,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,EAAE,UAAU;gBACpD;YACF,KAAK;gBACH,aAAa,EAAE,WAAW,GAAG,EAAE,WAAW;gBAC1C;YACF,KAAK;gBACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAC5E;QACJ;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,UAAU,KAAK,OAAO;QAC1B,IAAI,UAAU,KAAK,OAAO;QAC1B,IAAI,UAAU,KAAK,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,kBAAkB,MAAM,KAAK,2BAA2B,MAAM,EAAE;YAClE,qBAAqB,EAAE;QACzB,OAAO;YACL,qBAAqB,2BAA2B,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,qBAAqB,CAAA,OACnB,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,QAAS,UAAU,MAC/B;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,sBAAsB;QACtB,uBAAuB;IACzB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,QAAQ,mDAAmD;YAC7D,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAClD,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,kCAAkC;gBAChE,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,IAAI,QAAQ,CAAC,gCAAgC,EAAE,kBAAkB,MAAM,CAAC,WAAW,CAAC,GAAG;wBACrF,wBAAwB;wBACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,kBAAkB,MAAM,CAAC,kBAAkB,CAAC;oBAC/D;oBACA;gBACF,KAAK;oBACH,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,eAAe,CAAC;wBACjD,QAAQ,kBAAkB,IAAI,CAAC;oBACjC;oBACA,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,mBAAmB;oBACjD,gBAAgB;oBAClB;oBACA;gBACF;oBACE,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,cAAc,CAAC,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB,EAAE;YACvB,mBAAmB;QACrB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,OAAO;YACpB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IACnC;IAEA,MAAM,kBAAkB,CAAC;QACvB,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kCAAkC;QAClC,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,IAAI,iBAAiB;gBACnB,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE;gBACtE,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,kCAAkC;oBAChE,oBAAoB;oBACpB,mBAAmB;oBACnB,MAAM;gBACR;YACF,OAAO;gBACL,MAAM,WAAW,MAAM,mIAAA,CAAA,cAAW,CAAC,cAAc,CAAC;gBAClD,IAAI,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,kCAAkC;oBAChE,oBAAoB;oBACpB,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,oBAAoB;QACpB,mBAAmB;IACrB;IAEA,MAAM,qBAAqB;QACzB,uBAAuB;QACvB,sBAAsB;IACxB;IAEA,qBACE,6LAAC,gJAAA,CAAA,gBAAa;QAAC,WAAW;QAAe,WAAU;kBACjD,cAAA,6LAAC;YAAI,WAAU;;8BACf,6LAAC,yIAAA,CAAA,SAAM;oBACL,OAAM;oBACN,UAAU,CAAC,SAAS,EAAE,eAAe,kBAAkB,EAAE,YAAY,MAAM,GAAG,EAAE,YAAY,CAAC;oBAC7F,uBACE,6LAAC;wBAAI,WAAU;;4BACZ,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,kBAAkB,MAAM;4CAAC;;;;;;;kDAE5B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,mBAAmB,CAAC;;0DAEnC,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAK9C,6LAAC,gJAAA,CAAA,cAAW;gCACV,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,aAAa,EAAE,eAAe,iBAAiB,IAAI;;;;;;oCACzE,eAAe,kBAAkB;;;;;;;0CAEpC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS;;kDACzB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;gBAQxC,mBAAmB,kBAAkB,MAAM,GAAG,mBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAoC;4CAChC,kBAAkB,MAAM;4CAAC;;;;;;;kDAE7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB;;kEAEhC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB;;kEAEhC,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB;;kEAEhC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAK5C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,mBAAmB;0CAElC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAOrB,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDACrD,WAAU;gDACV,cAAW;0DAEV,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAAkB,OAAO;;4DAAO;4DAAK;;uDAAzB;;;;;;;;;;0DAGjB,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,cAAW;0DAEV,SAAS,GAAG,CAAC,CAAA,uBACZ,6LAAC;wDAAoB,OAAO;kEACzB,WAAW,QAAQ,eAAe,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;uDADtE;;;;;;;;;;0DAKjB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,uBAAuB,CAAC;gDACvC,WAAW,sBAAsB,wEAAwE;0DAEzG,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,WAAW;0DAEzB,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAAe,6LAAC,oOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAMlF,qCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAGlE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAU;oDACV,cAAW;;sEAEX,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAGlE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,WAAU;oDACV,cAAW;;sEAEX,6LAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,6LAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAM;;;;;;;;;;;;;;;;;;sDAGxB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAGlE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACZ,OAAO,WAAW,CAAC,EAAE;4DACrB,UAAU,CAAC,IAAM,eAAe;oEAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAG,WAAW,CAAC,EAAE;iEAAC;4DAC/E,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACZ,OAAO,WAAW,CAAC,EAAE;4DACrB,UAAU,CAAC,IAAM,eAAe;oEAAC,WAAW,CAAC,EAAE;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;iEAAO;4DACpF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAsC,UAAU,MAAM;;;;;;8DACnE,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,UAAU,MAAM;;;;;;8DAE1D,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,YAAY,MAAM;;;;;;8DAE5D,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,WAAW,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,CAAC;;;;;;8DAEzF,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAgC;wCAClC,2BAA2B,MAAM;wCAAC;wCAAK,UAAU,MAAM;wCAAC;;;;;;;gCAElE,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDACb,kBAAkB,MAAM;gDAAC;;;;;;;sDAE5B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,qBAAqB,EAAE;4CACtC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAMP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,SAAS,YAAY;oCAC3C,MAAK;oCACL,SAAS,IAAM,YAAY;;sDAE3B,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGtC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,SAAS,YAAY;oCAC3C,MAAK;oCACL,SAAS,IAAM,YAAY;;sDAE3B,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAOtC,aAAa,wBACZ,6LAAC;oBAAI,WAAU;8BACZ,2BAA2B,GAAG,CAAC,CAAC,UAAU,sBACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;sCAEjC,cAAA,6LAAC,gJAAA,CAAA,gBAAa;gCACZ,aAAa,IAAM,gBAAgB,SAAS,EAAE;gCAC9C,cAAc,IAAM,iBAAiB,SAAS,EAAE;gCAChD,WAAW,CAAC,8CAA8C,EACxD,kBAAkB,QAAQ,CAAC,SAAS,EAAE,IAAI,wDAAwD,IAClG;0CAEF,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDAChB,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,SAAS,kBAAkB,QAAQ,CAAC,SAAS,EAAE;wEAC/C,UAAU,IAAM,qBAAqB,SAAS,EAAE;wEAChD,WAAU;wEACV,cAAY,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE;;;;;;kFAEvC,6LAAC,qIAAA,CAAA,SAAM;wEAAC,WAAU;;0FAChB,6LAAC,qIAAA,CAAA,cAAW;gFAAC,KAAK,SAAS,MAAM;gFAAE,KAAK,SAAS,IAAI;;;;;;0FACrD,6LAAC,qIAAA,CAAA,iBAAc;0FACZ,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;0EAIpD,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAiC,SAAS,IAAI;;;;;;kFAC5D,6LAAC;wEAAE,WAAU;kFAAiC,SAAS,QAAQ;;;;;;;;;;;;;;;;;;kEAGnE,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;kEAC5C,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,eAAe,SAAS,MAAM;sEAC3C,SAAS,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;sEAEhC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,SAAS,WAAW,GAAG;;;;;;8EACvE,6LAAC;oEAAK,WAAW,CAAC,oBAAoB,EAAE,oBAAoB,SAAS,WAAW,GAAG;8EAChF,SAAS,WAAW;;;;;;;;;;;;;;;;;;8DAK3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAY,SAAS,KAAK;;;;;;;;;;;;sEAE5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;8EAAM,SAAS,UAAU;;;;;;;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;8EAAM,SAAS,QAAQ;;;;;;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;;wEAAK;wEAAS,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;sEAEhE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;;wEAAK;wEAAE,SAAS,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;8DAI1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;;8EAC5C,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;;8EAC5C,6LAAC,8MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnFpC,SAAS,EAAE;;;;;;;;;;gBAgGvB,aAAa,wBACZ,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,MAAK;wDACL,SAAS,kBAAkB,MAAM,KAAK,2BAA2B,MAAM,IAAI,2BAA2B,MAAM,GAAG;wDAC/G,UAAU;wDACV,WAAU;wDACV,cAAW;;;;;;;;;;;8DAGf,6LAAC;oDAAG,WAAU;oDAAmF,SAAS,IAAM,WAAW;8DACzH,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;4DACL,WAAW,UAAU,CAAC,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAAe,6LAAC,oOAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;oEAAY;;;;;;;;;;;;8DAGnH,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAChE,6LAAC;oDAAG,WAAU;oDAAmF,SAAS,IAAM,WAAW;8DACzH,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;4DACL,WAAW,gBAAgB,CAAC,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAAe,6LAAC,oOAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;oEAAY;;;;;;;;;;;;8DAGzH,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAChE,6LAAC;oDAAG,WAAU;oDAAmF,SAAS,IAAM,WAAW;8DACzH,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;4DACL,WAAW,iBAAiB,CAAC,cAAc,sBAAQ,6LAAC,iOAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAAe,6LAAC,oOAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;oEAAY;;;;;;;;;;;;8DAG1H,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAChE,6LAAC;oDAAG,WAAU;8DAAkD;;;;;;;;;;;;;;;;;kDAGpE,6LAAC;kDACE,2BAA2B,GAAG,CAAC,CAAC,UAAU,sBACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDAER,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,OAAO,QAAQ;gDAAK;gDAClC,WAAW,CAAC,yCAAyC,EACnD,kBAAkB,QAAQ,CAAC,SAAS,EAAE,IAAI,mCAAmC,IAC7E;;kEAEF,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DACC,MAAK;4DACL,SAAS,kBAAkB,QAAQ,CAAC,SAAS,EAAE;4DAC/C,UAAU,IAAM,qBAAqB,SAAS,EAAE;4DAChD,WAAU;4DACV,cAAY,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE;;;;;;;;;;;kEAGzC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,6LAAC,qIAAA,CAAA,cAAW;4EAAC,KAAK,SAAS,MAAM;4EAAE,KAAK,SAAS,IAAI;;;;;;sFACrD,6LAAC,qIAAA,CAAA,iBAAc;sFACZ,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8EAGlD,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAA+B,SAAS,IAAI;;;;;;sFACzD,6LAAC;4EAAE,WAAU;sFAAiC,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;kEAIlE,6LAAC;wDAAG,WAAU;kEAA6B,SAAS,QAAQ;;;;;;kEAC5D,6LAAC;wDAAG,WAAU;kEAAmC,SAAS,UAAU;;;;;;kEACpE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,eAAe,SAAS,MAAM;sEAC3C,SAAS,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;kEAGlC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,SAAS,WAAW,GAAG;;;;;;8EACvE,6LAAC;oEAAK,WAAW,CAAC,YAAY,EAAE,oBAAoB,SAAS,WAAW,GAAG;8EACxE,SAAS,WAAW;;;;;;;;;;;;;;;;;kEAI3B,6LAAC;wDAAG,WAAU;;4DAAwC;4DAClD,SAAS,MAAM,CAAC,cAAc;;;;;;;kEAElC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAO,WAAU;8EAC5C,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAO,WAAU;8EAC5C,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAO,WAAU;8EAC5C,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CA1DnB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAwEhC,6LAAC,gJAAA,CAAA,uBAAoB;oBACnB,oBAAM,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,SAAS;oBACT,UAAS;oBACT,WAAU;;;;;;8BAIZ,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAkB,cAAc;8BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;0CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;8CACT,kBAAkB,kBAAkB;;;;;;;;;;;0CAGzC,6LAAC,kJAAA,CAAA,eAAY;gCACX,UAAU;gCACV,UAAU;gCACV,UAAU;gCACV,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gCAC9C,WAAW;gCACX,UAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAqB,cAAc;8BAC/C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;0CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;;;;;;4BAEd,oCACC,6LAAC,qJAAA,CAAA,kBAAe;gCACd,YAAY;gCACZ,QAAQ;oCACN,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCAC9C,IAAI,UAAU;wCACZ,uBAAuB;wCACvB,mBAAmB;oCACrB;gCACF;gCACA,SAAS;;;;;;;;;;;;;;;;;gBAOhB,yBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA/1BwB;KAAA", "debugId": null}}]}