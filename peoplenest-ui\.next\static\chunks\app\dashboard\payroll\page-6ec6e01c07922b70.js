(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[827],{12486:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},24817:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(95155),l=t(12115),r=t(17859),n=t(40646),d=t(14186),i=t(85339),c=t(29869),o=t(91788);let x=(0,t(19946).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var p=t(55868),m=t(47924),h=t(92657),u=t(13717),y=t(12486),j=t(30285),g=t(62523),v=t(66695),f=t(26126),N=t(92262),k=t(91394);let A=[{id:1,employeeId:1,employeeName:"Sarah Johnson",employeeEmail:"<EMAIL>",department:"Engineering",position:"Senior Software Engineer",baseSalary:125e3,overtime:2500,bonuses:5e3,deductions:1200,netPay:131300,payPeriod:"2024-01-01 to 2024-01-31",status:"processed",processedDate:"2024-02-01",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"Mike Chen",employeeEmail:"<EMAIL>",department:"Product",position:"Product Manager",baseSalary:115e3,overtime:1800,bonuses:3e3,deductions:1100,netPay:118700,payPeriod:"2024-01-01 to 2024-01-31",status:"pending",processedDate:null,avatar:"/avatars/mike.jpg"},{id:3,employeeId:3,employeeName:"Emily Davis",employeeEmail:"<EMAIL>",department:"Design",position:"UX Designer",baseSalary:95e3,overtime:1200,bonuses:2e3,deductions:900,netPay:97300,payPeriod:"2024-01-01 to 2024-01-31",status:"processed",processedDate:"2024-02-01",avatar:"/avatars/emily.jpg"},{id:4,employeeId:4,employeeName:"David Wilson",employeeEmail:"<EMAIL>",department:"Engineering",position:"DevOps Engineer",baseSalary:11e4,overtime:2200,bonuses:1500,deductions:1e3,netPay:112700,payPeriod:"2024-01-01 to 2024-01-31",status:"review",processedDate:null,avatar:"/avatars/david.jpg"}],w={totalEmployees:A.length,totalGrossPay:A.reduce((e,s)=>e+s.baseSalary+s.overtime+s.bonuses,0),totalDeductions:A.reduce((e,s)=>e+s.deductions,0),totalNetPay:A.reduce((e,s)=>e+s.netPay,0),processed:A.filter(e=>"processed"===e.status).length,pending:A.filter(e=>"pending"===e.status).length,review:A.filter(e=>"review"===e.status).length};function b(){let[e,s]=(0,l.useState)(""),[t,b]=(0,l.useState)("All"),[M,P]=(0,l.useState)("All"),[S,E]=(0,l.useState)("2024-01-01 to 2024-01-31"),L=A.filter(s=>{let a=s.employeeName.toLowerCase().includes(e.toLowerCase())||s.employeeEmail.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),l="All"===t||s.status===t,r="All"===M||s.department===M;return a&&l&&r}),D=e=>{switch(e){case"processed":return"success";case"pending":return"warning";case"review":return"destructive";default:return"secondary"}},z=e=>{switch(e){case"processed":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"pending":default:return(0,a.jsx)(d.A,{className:"h-4 w-4"});case"review":return(0,a.jsx)(i.A,{className:"h-4 w-4"})}};return(0,a.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,a.jsx)(N.Y,{title:"Payroll Management",subtitle:"Processing payroll for ".concat(w.totalEmployees," employees"),actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(j.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Import"]}),(0,a.jsxs)(j.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,a.jsxs)(j.$,{size:"sm",children:[(0,a.jsx)(x,{className:"w-4 h-4 mr-2"}),"Process Payroll"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:["$",w.totalNetPay.toLocaleString()]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Net Pay"})]})]})})}),(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:w.processed}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Processed"})]})]})})}),(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:w.pending}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:w.review}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Needs Review"})]})]})})})]}),(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(g.p,{placeholder:"Search by employee name, email, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>b(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by status",children:[(0,a.jsx)("option",{value:"All",children:"All Status"}),(0,a.jsx)("option",{value:"processed",children:"Processed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"review",children:"Needs Review"})]}),(0,a.jsxs)("select",{value:M,onChange:e=>P(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by department",children:[(0,a.jsx)("option",{value:"All",children:"All Departments"}),(0,a.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,a.jsx)("option",{value:"Product",children:"Product"}),(0,a.jsx)("option",{value:"Design",children:"Design"}),(0,a.jsx)("option",{value:"Sales",children:"Sales"}),(0,a.jsx)("option",{value:"Marketing",children:"Marketing"})]})]})]})})}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",L.length," of ",A.length," payroll records"]})}),(0,a.jsx)(v.Zp,{children:(0,a.jsx)(v.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Department"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Base Salary"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Overtime"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Bonuses"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Deductions"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Net Pay"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:L.map((e,s)=>(0,a.jsxs)(r.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(k.eu,{className:"h-10 w-10",children:[(0,a.jsx)(k.BK,{src:e.avatar,alt:e.employeeName}),(0,a.jsx)(k.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.position})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground font-medium",children:["$",e.baseSalary.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground",children:["$",e.overtime.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-green-600 font-medium",children:["$",e.bonuses.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-red-600",children:["-$",e.deductions.toLocaleString()]}),(0,a.jsxs)("td",{className:"py-4 px-6 text-foreground font-bold",children:["$",e.netPay.toLocaleString()]}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)(f.E,{variant:D(e.status),className:"flex items-center space-x-1",children:[z(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})})]})}},29869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},34869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},55868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},66474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66932:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},68545:(e,s,t)=>{Promise.resolve().then(t.bind(t,24817))},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93509:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[706,352,289,262,441,684,358],()=>s(68545)),_N_E=e.O()}]);