"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Shield,
  FileCheck,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Users,
  FileText,
  Download,
  Upload,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from "recharts"

// Mock compliance data
const complianceItems = [
  {
    id: 1,
    title: "GDPR Data Protection Assessment",
    category: "Data Privacy",
    status: "compliant",
    priority: "high",
    dueDate: "2024-03-15",
    completedDate: "2024-01-20",
    assignedTo: "<PERSON>",
    description: "Annual GDPR compliance review and data protection impact assessment",
    progress: 100,
    documents: 8,
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    title: "SOX Financial Controls Review",
    category: "Financial",
    status: "in-progress",
    priority: "high",
    dueDate: "2024-02-28",
    completedDate: null,
    assignedTo: "Mike Chen",
    description: "Sarbanes-Oxley compliance review of financial reporting controls",
    progress: 75,
    documents: 12,
    avatar: "/avatars/mike.jpg"
  },
  {
    id: 3,
    title: "ISO 27001 Security Audit",
    category: "Information Security",
    status: "overdue",
    priority: "critical",
    dueDate: "2024-01-30",
    completedDate: null,
    assignedTo: "David Wilson",
    description: "Annual information security management system audit",
    progress: 45,
    documents: 6,
    avatar: "/avatars/david.jpg"
  },
  {
    id: 4,
    title: "OSHA Workplace Safety Training",
    category: "Health & Safety",
    status: "pending",
    priority: "medium",
    dueDate: "2024-04-15",
    completedDate: null,
    assignedTo: "Emily Davis",
    description: "Mandatory workplace safety training and certification",
    progress: 0,
    documents: 3,
    avatar: "/avatars/emily.jpg"
  },
  {
    id: 5,
    title: "HIPAA Privacy Rule Compliance",
    category: "Healthcare",
    status: "compliant",
    priority: "high",
    dueDate: "2024-06-30",
    completedDate: "2024-01-10",
    assignedTo: "Lisa Park",
    description: "Healthcare privacy and security compliance review",
    progress: 100,
    documents: 15,
    avatar: "/avatars/lisa.jpg"
  }
]

const complianceCategories = [
  { name: "Data Privacy", count: 8, compliant: 6, pending: 2 },
  { name: "Financial", count: 5, compliant: 3, pending: 2 },
  { name: "Information Security", count: 12, compliant: 8, pending: 4 },
  { name: "Health & Safety", count: 6, compliant: 4, pending: 2 },
  { name: "Healthcare", count: 4, compliant: 4, pending: 0 },
  { name: "Environmental", count: 3, compliant: 2, pending: 1 }
]

const complianceStats = {
  totalItems: complianceItems.length + 30, // Additional items
  compliantItems: complianceItems.filter(item => item.status === 'compliant').length + 22,
  overdueItems: complianceItems.filter(item => item.status === 'overdue').length + 3,
  inProgressItems: complianceItems.filter(item => item.status === 'in-progress').length + 8,
  complianceScore: 87,
  lastAuditDate: "2024-01-15"
}

const monthlyTrends = [
  { month: "Jul", compliant: 82, overdue: 8 },
  { month: "Aug", compliant: 85, overdue: 6 },
  { month: "Sep", compliant: 83, overdue: 9 },
  { month: "Oct", compliant: 88, overdue: 5 },
  { month: "Nov", compliant: 86, overdue: 7 },
  { month: "Dec", compliant: 89, overdue: 4 },
  { month: "Jan", compliant: 87, overdue: 6 }
]

const categoryDistribution = [
  { name: "Data Privacy", value: 23, color: "#3b82f6" },
  { name: "Financial", value: 14, color: "#10b981" },
  { name: "Security", value: 34, color: "#f59e0b" },
  { name: "Health & Safety", value: 17, color: "#ef4444" },
  { name: "Other", value: 12, color: "#8b5cf6" }
]

export default function CompliancePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedPriority, setSelectedPriority] = useState("All")
  const [viewMode, setViewMode] = useState<"overview" | "items" | "analytics">("overview")

  const filteredItems = complianceItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.assignedTo.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All" || item.category === selectedCategory
    const matchesStatus = selectedStatus === "All" || item.status === selectedStatus
    const matchesPriority = selectedPriority === "All" || item.priority === selectedPriority
    
    return matchesSearch && matchesCategory && matchesStatus && matchesPriority
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant": return "success"
      case "in-progress": return "default"
      case "overdue": return "destructive"
      case "pending": return "secondary"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant": return <CheckCircle className="h-4 w-4" />
      case "in-progress": return <Clock className="h-4 w-4" />
      case "overdue": return <XCircle className="h-4 w-4" />
      case "pending": return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "destructive"
      case "high": return "default"
      case "medium": return "secondary"
      case "low": return "outline"
      default: return "secondary"
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Compliance Management"
        subtitle={`${complianceStats.totalItems} compliance items • ${complianceStats.complianceScore}% compliance score`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Compliance Item
            </Button>
          </div>
        }
      />

      {/* Compliance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{complianceStats.totalItems}</p>
                <p className="text-sm text-muted-foreground">Total Items</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{complianceStats.compliantItems}</p>
                <p className="text-sm text-muted-foreground">Compliant</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{complianceStats.overdueItems}</p>
                <p className="text-sm text-muted-foreground">Overdue</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{complianceStats.inProgressItems}</p>
                <p className="text-sm text-muted-foreground">In Progress</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{complianceStats.complianceScore}%</p>
                <p className="text-sm text-muted-foreground">Score</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">Jan 15</p>
                <p className="text-sm text-muted-foreground">Last Audit</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "overview" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("overview")}
        >
          <Shield className="w-4 h-4 mr-2" />
          Overview
        </Button>
        <Button
          variant={viewMode === "items" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("items")}
        >
          <FileCheck className="w-4 h-4 mr-2" />
          Compliance Items
        </Button>
        <Button
          variant={viewMode === "analytics" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("analytics")}
        >
          <BarChart3 className="w-4 h-4 mr-2" />
          Analytics
        </Button>
      </div>

      {viewMode === "overview" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Compliance Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Compliance Categories</CardTitle>
              <CardDescription>Overview of compliance status by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {complianceCategories.map((category, index) => (
                  <motion.div
                    key={category.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card>
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">{category.name}</h3>
                            <Badge variant="outline">{category.count} items</Badge>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-green-600">Compliant: {category.compliant}</span>
                              <span className="text-orange-600">Pending: {category.pending}</span>
                            </div>
                            <Progress value={(category.compliant / category.count) * 100} className="h-2" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Compliance Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>Latest compliance updates and actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {complianceItems.slice(0, 3).map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center space-x-4 p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <Badge variant={getStatusColor(item.status) as any}>
                        {item.status}
                      </Badge>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{item.title}</h4>
                      <p className="text-sm text-muted-foreground">{item.category}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">Due: {new Date(item.dueDate).toLocaleDateString()}</p>
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={item.avatar} alt={item.assignedTo} />
                          <AvatarFallback className="text-xs">
                            {item.assignedTo.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-muted-foreground">{item.assignedTo}</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {viewMode === "items" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search compliance items..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="All">All Categories</option>
                    {complianceCategories.map(category => (
                      <option key={category.name} value={category.name}>{category.name}</option>
                    ))}
                  </select>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="All">All Status</option>
                    <option value="compliant">Compliant</option>
                    <option value="in-progress">In Progress</option>
                    <option value="overdue">Overdue</option>
                    <option value="pending">Pending</option>
                  </select>
                  <select
                    value={selectedPriority}
                    onChange={(e) => setSelectedPriority(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="All">All Priority</option>
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Compliance Items List */}
          <div className="grid grid-cols-1 gap-4">
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(item.status)}
                            <Badge variant={getStatusColor(item.status) as any}>
                              {item.status}
                            </Badge>
                          </div>
                          <Badge variant={getPriorityColor(item.priority) as any}>
                            {item.priority}
                          </Badge>
                          <Badge variant="outline">{item.category}</Badge>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold">{item.title}</h3>
                          <p className="text-muted-foreground">{item.description}</p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Due Date:</span>
                            <p className="text-muted-foreground">{new Date(item.dueDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <span className="font-medium">Assigned To:</span>
                            <div className="flex items-center space-x-2 mt-1">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={item.avatar} alt={item.assignedTo} />
                                <AvatarFallback className="text-xs">
                                  {item.assignedTo.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-muted-foreground">{item.assignedTo}</span>
                            </div>
                          </div>
                          <div>
                            <span className="font-medium">Documents:</span>
                            <p className="text-muted-foreground">{item.documents} files</p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{item.progress}%</span>
                          </div>
                          <Progress value={item.progress} className="h-2" />
                        </div>
                      </div>

                      <div className="flex space-x-2 ml-4">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {viewMode === "analytics" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Compliance Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Compliance Trends</CardTitle>
                <CardDescription>Monthly compliance and overdue items</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={monthlyTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="compliant" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="overdue" stroke="#ef4444" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Category Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Category Distribution</CardTitle>
                <CardDescription>Compliance items by category</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Compliance Score Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Compliance Score Breakdown</CardTitle>
              <CardDescription>Detailed breakdown of compliance scores by category</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={complianceCategories}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="compliant" fill="#10b981" name="Compliant" />
                  <Bar dataKey="pending" fill="#f59e0b" name="Pending" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
