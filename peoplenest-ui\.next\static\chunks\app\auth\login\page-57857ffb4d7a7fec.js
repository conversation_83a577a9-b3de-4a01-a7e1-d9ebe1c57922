(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{4177:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(95155),s=r(12115),n=r(17859),i=r(23227),o=r(75525),d=r(28883),l=r(32919),c=r(78749),u=r(92657),m=r(30285),x=r(62523),f=r(66695),h=r(26126);function p(){let[e,t]=(0,s.useState)(!1),[r,p]=(0,s.useState)(!1),[g,b]=(0,s.useState)({email:"",password:"",rememberMe:!1}),y=async e=>{e.preventDefault(),p(!0),await new Promise(e=>setTimeout(e,2e3)),p(!1),window.location.href="/dashboard"},v=e=>{let{name:t,value:r,type:a,checked:s}=e.target;b(e=>({...e,[t]:"checkbox"===a?s:r}))};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(n.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,duration:.3},className:"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"PeopleNest"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Enterprise HRMS Platform"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mt-3",children:[(0,a.jsxs)(h.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"SOC 2 Compliant"]}),(0,a.jsx)(h.E,{variant:"outline",className:"text-xs",children:"Enterprise Ready"})]})]}),(0,a.jsxs)(f.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,a.jsxs)(f.aR,{className:"space-y-1 pb-6",children:[(0,a.jsx)(f.ZB,{className:"text-2xl font-semibold text-center",children:"Welcome back"}),(0,a.jsx)(f.BT,{className:"text-center",children:"Sign in to your account to continue"})]}),(0,a.jsxs)(f.Wu,{children:[(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(x.p,{name:"email",type:"email",placeholder:"Enter your email",label:"Email Address",leftIcon:(0,a.jsx)(d.A,{className:"w-4 h-4"}),value:g.email,onChange:v,required:!0}),(0,a.jsx)(x.p,{name:"password",type:e?"text":"password",placeholder:"Enter your password",label:"Password",leftIcon:(0,a.jsx)(l.A,{className:"w-4 h-4"}),rightIcon:(0,a.jsx)("button",{type:"button",onClick:()=>t(!e),className:"text-muted-foreground hover:text-foreground transition-colors",children:e?(0,a.jsx)(c.A,{className:"w-4 h-4"}):(0,a.jsx)(u.A,{className:"w-4 h-4"})}),value:g.password,onChange:v,required:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)("input",{type:"checkbox",name:"rememberMe",checked:g.rememberMe,onChange:v,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Remember me"})]}),(0,a.jsx)("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Forgot password? (Coming Soon)"})]}),(0,a.jsx)(m.$,{type:"submit",className:"w-full h-11 text-base font-medium",loading:r,disabled:!g.email||!g.password,children:r?"Signing in...":"Sign in"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,a.jsx)("a",{href:"#",className:"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Contact your administrator"})]})})]})]}),(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.3},className:"mt-6 text-center",children:(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Protected by enterprise-grade security and encryption"})})]})]})}},23227:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,icon:o,removable:d,onRemove:l,children:c,...u}=e;return(0,a.jsxs)("div",{className:(0,n.cn)(i({variant:r,size:s}),t),...u,children:[o&&(0,a.jsx)("span",{className:"mr-1",children:o}),c,d&&(0,a.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:l,children:(0,a.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(95155),s=r(12115),n=r(99708),i=r(74466),o=r(59434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:l=!1,loading:c=!1,leftIcon:u,rightIcon:m,children:x,disabled:f,...h}=e,p=l?n.DX:"button";return(0,a.jsxs)(p,{className:(0,o.cn)(d({variant:s,size:i,className:r})),ref:t,disabled:f||c,...h,children:[c&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&u&&(0,a.jsx)("span",{className:"mr-2",children:u}),x,!c&&m&&(0,a.jsx)("span",{className:"ml-2",children:m})]})});l.displayName="Button"},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{Ee:()=>o,cn:()=>n,vv:()=>i});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function o(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(95155),s=r(12115),n=r(59434);let i=(0,r(74466).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,type:o,variant:d,size:l,leftIcon:c,rightIcon:u,error:m,label:x,helperText:f,id:h,...p}=e,g=h||s.useId(),b=!!m;return(0,a.jsxs)("div",{className:"w-full",children:[x&&(0,a.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-foreground mb-1",children:x}),(0,a.jsxs)("div",{className:"relative",children:[c&&(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:c}),(0,a.jsx)("input",{type:o,className:(0,n.cn)(i({variant:b?"error":d,size:l,className:r}),c&&"pl-10",u&&"pr-10"),ref:t,id:g,...p}),u&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:u})]}),(m||f)&&(0,a.jsx)("p",{className:(0,n.cn)("mt-1 text-xs",b?"text-destructive":"text-muted-foreground"),children:m||f})]})});o.displayName="Input"},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>l,Zp:()=>o,aR:()=>d});var a=r(95155),s=r(12115),n=r(59434);let i=(0,r(74466).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,variant:s,padding:o,hover:d=!1,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)(i({variant:s,padding:o}),d&&"transition-shadow hover:shadow-md",r),...l})});o.displayName="Card";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});d.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},75525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93717:(e,t,r)=>{Promise.resolve().then(r.bind(r,4177))}},e=>{var t=t=>e(e.s=t);e.O(0,[706,352,441,684,358],()=>t(93717)),_N_E=e.O()}]);