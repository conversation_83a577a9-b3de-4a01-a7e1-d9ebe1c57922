{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b1ee38eb.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_b1ee38eb-module__r5vH2a__className\",\n  \"variable\": \"inter_b1ee38eb-module__r5vH2a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b1ee38eb.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-sans%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_fc94ad34.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_fc94ad34-module__OmocEG__className\",\n  \"variable\": \"jetbrains_mono_fc94ad34-module__OmocEG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_fc94ad34.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22variable%22:%22--font-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  variable: \"--font-sans\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  variable: \"--font-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"PeopleNest - Enterprise HRMS Platform\",\n  description: \"Modern, AI-powered Human Resource Management System for enterprise organizations\",\n  keywords: [\"HRMS\", \"HR\", \"Human Resources\", \"Employee Management\", \"Payroll\", \"Performance\"],\n  manifest: \"/manifest.json\",\n  themeColor: \"#3b82f6\",\n  viewport: {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n  },\n  appleWebApp: {\n    capable: true,\n    statusBarStyle: \"default\",\n    title: \"PeopleNest HRMS\"\n  },\n  formatDetection: {\n    telephone: false\n  },\n  other: {\n    \"mobile-web-app-capable\": \"yes\",\n    \"apple-mobile-web-app-capable\": \"yes\",\n    \"apple-mobile-web-app-status-bar-style\": \"default\",\n    \"apple-mobile-web-app-title\": \"PeopleNest\",\n    \"application-name\": \"PeopleNest HRMS\",\n    \"msapplication-TileColor\": \"#3b82f6\",\n    \"msapplication-config\": \"/browserconfig.xml\"\n  }\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <link rel=\"apple-touch-icon\" href=\"/icons/icon-192x192.png\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"PeopleNest\" />\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"application-name\" content=\"PeopleNest HRMS\" />\n        <meta name=\"msapplication-TileColor\" content=\"#3b82f6\" />\n        <meta name=\"msapplication-tap-highlight\" content=\"no\" />\n        <meta name=\"format-detection\" content=\"telephone=no\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover\" />\n      </head>\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased touch-manipulation`}\n      >\n        {children}\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `,\n          }}\n        />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAQ;QAAM;QAAmB;QAAuB;QAAW;KAAc;IAC5F,UAAU;IACV,YAAY;IACZ,UAAU;QACR,OAAO;QACP,cAAc;QACd,cAAc;QACd,cAAc;QACd,aAAa;IACf;IACA,aAAa;QACX,SAAS;QACT,gBAAgB;QAChB,OAAO;IACT;IACA,iBAAiB;QACf,WAAW;IACb;IACA,OAAO;QACL,0BAA0B;QAC1B,gCAAgC;QAChC,yCAAyC;QACzC,8BAA8B;QAC9B,oBAAoB;QACpB,2BAA2B;QAC3B,wBAAwB;IAC1B;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAC1B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAChD,8OAAC;wBAAK,MAAK;wBAAyB,SAAQ;;;;;;kCAC5C,8OAAC;wBAAK,MAAK;wBAAmB,SAAQ;;;;;;kCACtC,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;kCAC7C,8OAAC;wBAAK,MAAK;wBAA8B,SAAQ;;;;;;kCACjD,8OAAC;wBAAK,MAAK;wBAAmB,SAAQ;;;;;;kCACtC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,yCAAyC,CAAC;;oBAEhG;kCACD,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;YAYT,CAAC;wBACH;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}