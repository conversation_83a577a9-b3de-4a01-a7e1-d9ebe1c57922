"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Clock,
  Calendar,
  Users,
  TrendingUp,

  CheckCircle,
  XCircle,
  AlertTriangle,
  Download,

  Search,
  Eye,
  Edit,
  Plus,
  MapPin,
  Timer,
  Coffee,
  LogIn,
  LogOut
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

// Mock attendance data
const attendanceRecords = [
  {
    id: 1,
    employeeId: 1,
    employeeName: "<PERSON>",
    department: "Engineering",
    date: "2024-01-25",
    clockIn: "09:00",
    clockOut: "17:30",
    breakTime: "60",
    totalHours: "7.5",
    status: "present",
    location: "Office",
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    employeeId: 2,
    employeeName: "Mike <PERSON>",
    department: "Product",
    date: "2024-01-25",
    clockIn: "08:45",
    clockOut: "17:15",
    breakTime: "45",
    totalHours: "7.75",
    status: "present",
    location: "Remote",
    avatar: "/avatars/mike.jpg"
  },
  {
    id: 3,
    employeeId: 3,
    employeeName: "Emily Davis",
    department: "Design",
    date: "2024-01-25",
    clockIn: "09:15",
    clockOut: "18:00",
    breakTime: "75",
    totalHours: "7.5",
    status: "late",
    location: "Office",
    avatar: "/avatars/emily.jpg"
  },
  {
    id: 4,
    employeeId: 4,
    employeeName: "David Wilson",
    department: "Engineering",
    date: "2024-01-25",
    clockIn: null,
    clockOut: null,
    breakTime: null,
    totalHours: "0",
    status: "absent",
    location: null,
    avatar: "/avatars/david.jpg"
  }
]

const attendanceTrends = [
  { date: 'Jan 20', present: 95, late: 3, absent: 2 },
  { date: 'Jan 21', present: 97, late: 2, absent: 1 },
  { date: 'Jan 22', present: 94, late: 4, absent: 2 },
  { date: 'Jan 23', present: 96, late: 2, absent: 2 },
  { date: 'Jan 24', present: 98, late: 1, absent: 1 },
  { date: 'Jan 25', present: 93, late: 5, absent: 2 }
]

const departmentAttendance = [
  { department: 'Engineering', present: 45, late: 2, absent: 1 },
  { department: 'Product', present: 28, late: 1, absent: 1 },
  { department: 'Design', present: 22, late: 2, absent: 0 },
  { department: 'Sales', present: 35, late: 3, absent: 2 },
  { department: 'Marketing', present: 18, late: 1, absent: 1 }
]

const attendanceStats = {
  totalEmployees: 150,
  presentToday: 140,
  lateToday: 8,
  absentToday: 2,
  avgHoursPerDay: 7.6,
  attendanceRate: 93.3
}

export default function AttendancePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [selectedDate, setSelectedDate] = useState("2024-01-25")
  const [viewMode, setViewMode] = useState<"daily" | "trends" | "reports">("daily")

  const filteredAttendance = attendanceRecords.filter(record => {
    const matchesSearch = record.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.department.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = selectedStatus === "All" || record.status === selectedStatus
    const matchesDepartment = selectedDepartment === "All" || record.department === selectedDepartment
    
    return matchesSearch && matchesStatus && matchesDepartment
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present": return "success"
      case "late": return "warning"
      case "absent": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present": return <CheckCircle className="h-4 w-4" />
      case "late": return <AlertTriangle className="h-4 w-4" />
      case "absent": return <XCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getLocationIcon = (location: string | null) => {
    if (!location) return null
    return location === "Office" ? <MapPin className="h-3 w-3" /> : <Coffee className="h-3 w-3" />
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Time & Attendance"
        subtitle={`Tracking attendance for ${attendanceStats.totalEmployees} employees`}
        actions={
          <div className="flex items-center space-x-2">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Manual Entry
            </Button>
          </div>
        }
      />

      {/* Attendance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.totalEmployees}</p>
                <p className="text-sm text-muted-foreground">Total Employees</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.presentToday}</p>
                <p className="text-sm text-muted-foreground">Present Today</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.lateToday}</p>
                <p className="text-sm text-muted-foreground">Late Today</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.absentToday}</p>
                <p className="text-sm text-muted-foreground">Absent Today</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.avgHoursPerDay}</p>
                <p className="text-sm text-muted-foreground">Avg Hours/Day</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{attendanceStats.attendanceRate}%</p>
                <p className="text-sm text-muted-foreground">Attendance Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "daily" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("daily")}
        >
          Daily View
        </Button>
        <Button
          variant={viewMode === "trends" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("trends")}
        >
          Trends
        </Button>
        <Button
          variant={viewMode === "reports" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("reports")}
        >
          Reports
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name or department..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Status</option>
                <option value="present">Present</option>
                <option value="late">Late</option>
                <option value="absent">Absent</option>
              </select>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Product">Product</option>
                <option value="Design">Design</option>
                <option value="Sales">Sales</option>
                <option value="Marketing">Marketing</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === "daily" ? (
        /* Daily Attendance Table */
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/50 border-b border-border">
                  <tr>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Employee</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Department</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Clock In</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Clock Out</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Break Time</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Total Hours</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Status</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Location</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAttendance.map((record, index) => (
                    <motion.tr
                      key={record.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="border-b border-border hover:bg-muted/50"
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={record.avatar} alt={record.employeeName} />
                            <AvatarFallback>
                              {record.employeeName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-foreground">{record.employeeName}</p>
                            <p className="text-sm text-muted-foreground">ID: {record.employeeId}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-muted-foreground">{record.department}</td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-1">
                          {record.clockIn && <LogIn className="h-4 w-4 text-green-600" />}
                          <span className="font-medium">{record.clockIn || "N/A"}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-1">
                          {record.clockOut && <LogOut className="h-4 w-4 text-red-600" />}
                          <span className="font-medium">{record.clockOut || "N/A"}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-1">
                          {record.breakTime && <Timer className="h-4 w-4 text-blue-600" />}
                          <span>{record.breakTime ? `${record.breakTime} min` : "N/A"}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6 font-medium">{record.totalHours}h</td>
                      <td className="py-4 px-6">
                        <Badge variant={getStatusColor(record.status) as "default" | "secondary" | "destructive" | "outline"} className="flex items-center space-x-1 w-fit">
                          {getStatusIcon(record.status)}
                          <span className="ml-1">{record.status.charAt(0).toUpperCase() + record.status.slice(1)}</span>
                        </Badge>
                      </td>
                      <td className="py-4 px-6">
                        {record.location && (
                          <div className="flex items-center space-x-1">
                            {getLocationIcon(record.location)}
                            <span className="text-sm">{record.location}</span>
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === "trends" ? (
        /* Attendance Trends */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Daily Attendance Trends</span>
              </CardTitle>
              <CardDescription>Attendance patterns over the last week</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={attendanceTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="present" stroke="#10b981" strokeWidth={2} name="Present" />
                  <Line type="monotone" dataKey="late" stroke="#f59e0b" strokeWidth={2} name="Late" />
                  <Line type="monotone" dataKey="absent" stroke="#ef4444" strokeWidth={2} name="Absent" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Department Attendance</span>
              </CardTitle>
              <CardDescription>Attendance breakdown by department</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={departmentAttendance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="department" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="present" stackId="a" fill="#10b981" name="Present" />
                  <Bar dataKey="late" stackId="a" fill="#f59e0b" name="Late" />
                  <Bar dataKey="absent" stackId="a" fill="#ef4444" name="Absent" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Reports */
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Reports</CardTitle>
              <CardDescription>Generate and download attendance reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <Calendar className="h-8 w-8 mx-auto text-blue-600" />
                      <h3 className="font-medium">Daily Report</h3>
                      <p className="text-sm text-muted-foreground">Today&apos;s attendance summary</p>
                      <Button size="sm" className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <Clock className="h-8 w-8 mx-auto text-green-600" />
                      <h3 className="font-medium">Weekly Report</h3>
                      <p className="text-sm text-muted-foreground">Last 7 days attendance</p>
                      <Button size="sm" className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <TrendingUp className="h-8 w-8 mx-auto text-purple-600" />
                      <h3 className="font-medium">Monthly Report</h3>
                      <p className="text-sm text-muted-foreground">Full month analysis</p>
                      <Button size="sm" className="w-full">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
