(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30187:(e,t,r)=>{Promise.resolve().then(r.bind(r,49457))},33873:e=>{"use strict";e.exports=require("path")},35763:(e,t,r)=>{Promise.resolve().then(r.bind(r,81351))},36812:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81351)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},49457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),a=r(43210),n=r(50371),i=r(17313),o=r(99891),l=r(19169),d=r(64021),c=r(12597),p=r(13861),m=r(29523),u=r(89667),x=r(44493),h=r(96834);function f(){let[e,t]=(0,a.useState)(!1),[r,f]=(0,a.useState)(!1),[y,g]=(0,a.useState)({email:"",password:"",rememberMe:!1}),b=async e=>{e.preventDefault(),f(!0),await new Promise(e=>setTimeout(e,2e3)),f(!1),window.location.href="/dashboard"},v=e=>{let{name:t,value:r,type:s,checked:a}=e.target;g(e=>({...e,[t]:"checkbox"===s?a:r}))};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md relative z-10",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(n.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,duration:.3},className:"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg",children:(0,s.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"PeopleNest"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Enterprise HRMS Platform"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mt-3",children:[(0,s.jsxs)(h.E,{variant:"secondary",className:"text-xs",children:[(0,s.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"SOC 2 Compliant"]}),(0,s.jsx)(h.E,{variant:"outline",className:"text-xs",children:"Enterprise Ready"})]})]}),(0,s.jsxs)(x.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,s.jsxs)(x.aR,{className:"space-y-1 pb-6",children:[(0,s.jsx)(x.ZB,{className:"text-2xl font-semibold text-center",children:"Welcome back"}),(0,s.jsx)(x.BT,{className:"text-center",children:"Sign in to your account to continue"})]}),(0,s.jsxs)(x.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(u.p,{name:"email",type:"email",placeholder:"Enter your email",label:"Email Address",leftIcon:(0,s.jsx)(l.A,{className:"w-4 h-4"}),value:y.email,onChange:v,required:!0}),(0,s.jsx)(u.p,{name:"password",type:e?"text":"password",placeholder:"Enter your password",label:"Password",leftIcon:(0,s.jsx)(d.A,{className:"w-4 h-4"}),rightIcon:(0,s.jsx)("button",{type:"button",onClick:()=>t(!e),className:"text-muted-foreground hover:text-foreground transition-colors",children:e?(0,s.jsx)(c.A,{className:"w-4 h-4"}):(0,s.jsx)(p.A,{className:"w-4 h-4"})}),value:y.password,onChange:v,required:!0})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)("input",{type:"checkbox",name:"rememberMe",checked:y.rememberMe,onChange:v,className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,s.jsx)("span",{className:"text-muted-foreground",children:"Remember me"})]}),(0,s.jsx)("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Forgot password? (Coming Soon)"})]}),(0,s.jsx)(m.$,{type:"submit",className:"w-full h-11 text-base font-medium",loading:r,disabled:!y.email||!y.password,children:r?"Signing in...":"Sign in"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)("a",{href:"#",className:"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Contact your administrator"})]})})]})]}),(0,s.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.3},className:"mt-6 text-center",children:(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Protected by enterprise-grade security and encryption"})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},79551:e=>{"use strict";e.exports=require("url")},81351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\auth\\login\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,912,486,722,557],()=>r(36812));module.exports=s})();