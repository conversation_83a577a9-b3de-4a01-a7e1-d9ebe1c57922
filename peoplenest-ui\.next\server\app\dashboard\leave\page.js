(()=>{var e={};e.id=569,e.ids=[569],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15202:(e,t,a)=>{"use strict";a.d(t,{s:()=>q});var r=a(43210),s=a(51215),n=a(14221),l=a(49384),i=a(10687),c=a.n(i),o=a(21080),d=a(10919),p=a(4057);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function m(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,r)}return a}function h(e,t,a){var r;return(t="symbol"==typeof(r=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?r:r+"")in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}class x extends r.PureComponent{renderIcon(e,t){var{inactiveColor:a}=this.props,s=32/6,n=32/3,l=e.inactive?a:e.color,i=null!=t?t:e.type;if("none"===i)return null;if("plainline"===i)return r.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===i)return r.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(s,",").concat(s,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(s,",").concat(s,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===i)return r.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(r.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?m(Object(a),!0).forEach(function(t){h(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}({},e);return delete c.legendIcon,r.cloneElement(e.legendIcon,c)}return r.createElement(d.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:i})}renderItems(){var{payload:e,iconSize:t,layout:a,formatter:s,inactiveColor:n,iconType:i,itemSorter:d}=this.props,m={x:0,y:0,width:32,height:32},h={display:"horizontal"===a?"inline-block":"block",marginRight:10},x={display:"inline-block",verticalAlign:"middle",marginRight:4};return(d?c()(e,d):e).map((e,a)=>{var c=e.formatter||s,d=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(a)]:!0,inactive:e.inactive});if("none"===e.type)return null;var v=e.inactive?n:e.color,f=c?c(e.value,e,a):e.value;return r.createElement("li",u({className:d,style:h,key:"legend-item-".concat(a)},(0,p.XC)(this.props,e,a)),r.createElement(o.u,{width:t,height:t,viewBox:m,style:x,"aria-label":"".concat(f," legend icon")},this.renderIcon(e,i)),r.createElement("span",{className:"recharts-legend-item-text",style:{color:v}},f))})}render(){var{payload:e,layout:t,align:a}=this.props;return e&&e.length?r.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?a:"left"}},this.renderItems()):null}}h(x,"displayName","Legend"),h(x,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var v=a(22989),f=a(45796),g=a(43209),y=a(23337),j=a(68392),b=a(51426);a(53044);var N=["contextPayload"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function A(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,r)}return a}function P(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?A(Object(a),!0).forEach(function(t){k(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):A(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function k(e,t,a){var r;return(t="symbol"==typeof(r=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?r:r+"")in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function O(e){return e.value}function E(e){var{contextPayload:t}=e,a=function(e,t){if(null==e)return{};var a,r,s=function(e,t){if(null==e)return{};var a={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;a[r]=e[r]}return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)a=n[r],-1===t.indexOf(a)&&({}).propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}(e,N),s=(0,f.s)(t,e.payloadUniqBy,O),n=P(P({},a),{},{payload:s});return r.isValidElement(e.content)?r.cloneElement(e.content,n):"function"==typeof e.content?r.createElement(e.content,n):r.createElement(x,n)}function D(e){return(0,g.j)(),null}function C(e){return(0,g.j)(),null}function S(e){var t=(0,g.G)(y.g0),a=(0,n.M)(),l=(0,b.Kp)(),{width:i,height:c,wrapperStyle:o,portal:d}=e,[p,u]=(0,j.V)([t]),m=(0,b.yi)(),h=(0,b.rY)(),x=m-(l.left||0)-(l.right||0),v=q.getWidthOrHeight(e.layout,c,i,x),f=d?o:P(P({position:"absolute",width:(null==v?void 0:v.width)||i||"auto",height:(null==v?void 0:v.height)||c||"auto"},function(e,t,a,r,s,n){var l,i,{layout:c,align:o,verticalAlign:d}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===o&&"vertical"===c?{left:((r||0)-n.width)/2}:"right"===o?{right:a&&a.right||0}:{left:a&&a.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(i="middle"===d?{top:((s||0)-n.height)/2}:"bottom"===d?{bottom:a&&a.bottom||0}:{top:a&&a.top||0}),P(P({},l),i)}(o,e,l,m,h,p)),o),N=null!=d?d:a;if(null==N)return null;var A=r.createElement("div",{className:"recharts-legend-wrapper",style:f,ref:u},r.createElement(D,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),r.createElement(C,{width:p.width,height:p.height}),r.createElement(E,w({},e,v,{margin:l,chartWidth:m,chartHeight:h,contextPayload:t})));return(0,s.createPortal)(A,N)}class q extends r.PureComponent{static getWidthOrHeight(e,t,a,r){return"vertical"===e&&(0,v.Et)(t)?{height:t}:"horizontal"===e?{width:a||r}:null}render(){return r.createElement(S,this.props)}}k(q,"displayName","Legend"),k(q,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20949:(e,t,a)=>{Promise.resolve().then(a.bind(a,97175))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},39657:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>$});var r=a(60687),s=a(43210),n=a(50371),l=a(62688);let i=(0,l.A)("plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]]);var c=a(67760),o=a(57800);let d=(0,l.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var p=a(40228),u=a(5336),m=a(48730),h=a(35071),x=a(43649),v=a(31158),f=a(96474),g=a(25541),y=a(41312),j=a(99270),b=a(13861),N=a(63143),w=a(58887),A=a(29523),P=a(89667),k=a(44493),O=a(96834),E=a(74456),D=a(32584),C=a(48482),S=a(2041),q=a(85168),R=a(27747),z=a(19598),L=a(18969),T=a(15202),M=a(515),I=a(56651),W=a(25679);let B=[{id:1,employeeId:1,employeeName:"Sarah Johnson",department:"Engineering",leaveType:"vacation",startDate:"2024-02-15",endDate:"2024-02-19",days:5,status:"approved",reason:"Family vacation",appliedDate:"2024-01-20",approver:"Mike Chen",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"David Wilson",department:"Product",leaveType:"sick",startDate:"2024-01-28",endDate:"2024-01-30",days:3,status:"approved",reason:"Medical appointment",appliedDate:"2024-01-25",approver:"Emily Davis",avatar:"/avatars/david.jpg"},{id:3,employeeId:3,employeeName:"Lisa Park",department:"Design",leaveType:"personal",startDate:"2024-02-05",endDate:"2024-02-05",days:1,status:"pending",reason:"Personal matters",appliedDate:"2024-01-30",approver:"Tom Anderson",avatar:"/avatars/lisa.jpg"},{id:4,employeeId:4,employeeName:"James Liu",department:"Engineering",leaveType:"vacation",startDate:"2024-03-01",endDate:"2024-03-15",days:11,status:"rejected",reason:"Extended vacation",appliedDate:"2024-01-15",approver:"Mike Chen",avatar:"/avatars/james.jpg"}],F=[{name:"Vacation",value:45,color:"#0088FE",icon:i},{name:"Sick Leave",value:28,color:"#00C49F",icon:c.A},{name:"Personal",value:15,color:"#FFBB28",icon:o.A},{name:"Training",value:8,color:"#FF8042",icon:d},{name:"Other",value:4,color:"#8884D8",icon:p.A}],Z=[{month:"Jan",vacation:12,sick:8,personal:4,training:2},{month:"Feb",vacation:15,sick:6,personal:5,training:3},{month:"Mar",vacation:18,sick:7,personal:3,training:1},{month:"Apr",vacation:22,sick:5,personal:6,training:4},{month:"May",vacation:25,sick:9,personal:4,training:2},{month:"Jun",vacation:28,sick:4,personal:7,training:3}],_={totalRequests:B.length,pendingRequests:B.filter(e=>"pending"===e.status).length,approvedRequests:B.filter(e=>"approved"===e.status).length,rejectedRequests:B.filter(e=>"rejected"===e.status).length,avgProcessingTime:2.5,totalDaysRequested:B.reduce((e,t)=>e+t.days,0)};function $(){let[e,t]=(0,s.useState)(""),[a,l]=(0,s.useState)("All"),[$,G]=(0,s.useState)("All"),[K,V]=(0,s.useState)("All"),[H,J]=(0,s.useState)("requests"),U=B.filter(t=>{let r=t.employeeName.toLowerCase().includes(e.toLowerCase())||t.department.toLowerCase().includes(e.toLowerCase())||t.reason.toLowerCase().includes(e.toLowerCase()),s="All"===a||t.status===a,n="All"===$||t.leaveType===$,l="All"===K||t.department===K;return r&&s&&n&&l}),X=e=>{switch(e){case"approved":return"success";case"pending":return"warning";case"rejected":return"destructive";default:return"secondary"}},Y=e=>{switch(e){case"approved":return(0,r.jsx)(u.A,{className:"h-4 w-4"});case"pending":return(0,r.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(h.A,{className:"h-4 w-4"});default:return(0,r.jsx)(x.A,{className:"h-4 w-4"})}},Q=e=>{switch(e){case"vacation":return(0,r.jsx)(i,{className:"h-4 w-4"});case"sick":return(0,r.jsx)(c.A,{className:"h-4 w-4"});case"personal":return(0,r.jsx)(o.A,{className:"h-4 w-4"});case"training":return(0,r.jsx)(d,{className:"h-4 w-4"});default:return(0,r.jsx)(p.A,{className:"h-4 w-4"})}},ee=e=>{switch(e){case"vacation":return"text-blue-600";case"sick":return"text-red-600";case"personal":return"text-green-600";case"training":return"text-purple-600";default:return"text-gray-600"}};return(0,r.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,r.jsx)(E.Y,{title:"Leave Management",subtitle:`Managing ${_.totalRequests} leave requests with ${_.pendingRequests} pending approval`,actions:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,r.jsxs)(A.$,{size:"sm",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"New Request"]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.totalRequests}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Requests"})]})]})})}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.pendingRequests}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.approvedRequests}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Approved"})]})]})})}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.rejectedRequests}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Rejected"})]})]})})}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.avgProcessingTime}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days"})]})]})})}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.totalDaysRequested}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Days"})]})]})})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(A.$,{variant:"requests"===H?"default":"outline",size:"sm",onClick:()=>J("requests"),children:"Requests"}),(0,r.jsx)(A.$,{variant:"calendar"===H?"default":"outline",size:"sm",onClick:()=>J("calendar"),children:"Calendar"}),(0,r.jsx)(A.$,{variant:"analytics"===H?"default":"outline",size:"sm",onClick:()=>J("analytics"),children:"Analytics"})]}),(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(P.p,{placeholder:"Search by employee name, department, or reason...",value:e,onChange:e=>t(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:a,onChange:e=>l(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,r.jsx)("option",{value:"All",children:"All Status"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,r.jsxs)("select",{value:$,onChange:e=>G(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,r.jsx)("option",{value:"All",children:"All Types"}),(0,r.jsx)("option",{value:"vacation",children:"Vacation"}),(0,r.jsx)("option",{value:"sick",children:"Sick Leave"}),(0,r.jsx)("option",{value:"personal",children:"Personal"}),(0,r.jsx)("option",{value:"training",children:"Training"})]}),(0,r.jsxs)("select",{value:K,onChange:e=>V(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,r.jsx)("option",{value:"All",children:"All Departments"}),(0,r.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,r.jsx)("option",{value:"Product",children:"Product"}),(0,r.jsx)("option",{value:"Design",children:"Design"})]})]})]})})}),"requests"===H?(0,r.jsx)(k.Zp,{children:(0,r.jsx)(k.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Leave Type"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Dates"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Days"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Reason"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Approver"}),(0,r.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:U.map((e,t)=>(0,r.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*t},className:"border-b border-border hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(D.eu,{className:"h-10 w-10",children:[(0,r.jsx)(D.BK,{src:e.avatar,alt:e.employeeName}),(0,r.jsx)(D.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:ee(e.leaveType),children:Q(e.leaveType)}),(0,r.jsx)("span",{className:"capitalize",children:e.leaveType})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:new Date(e.startDate).toLocaleDateString()}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["to ",new Date(e.endDate).toLocaleDateString()]})]})}),(0,r.jsx)("td",{className:"py-4 px-6 font-medium",children:e.days}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)(O.E,{variant:X(e.status),className:"flex items-center space-x-1 w-fit",children:[Y(e.status),(0,r.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("p",{className:"text-sm max-w-xs truncate",children:e.reason})}),(0,r.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.approver}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})}),(0,r.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})}),(0,r.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"calendar"===H?(0,r.jsxs)(k.Zp,{children:[(0,r.jsxs)(k.aR,{children:[(0,r.jsx)(k.ZB,{children:"Leave Calendar"}),(0,r.jsx)(k.BT,{children:"Visual overview of upcoming leave requests"})]}),(0,r.jsx)(k.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:B.filter(e=>"approved"===e.status).map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(D.eu,{className:"h-8 w-8",children:[(0,r.jsx)(D.BK,{src:e.avatar,alt:e.employeeName}),(0,r.jsx)(D.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.employeeName}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.department})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.days," days"]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:e.leaveType})]})]}),(0,r.jsx)("div",{className:"mt-2 text-sm",children:(0,r.jsxs)("p",{children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})})]},e.id))})})})]}):(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(k.Zp,{children:[(0,r.jsxs)(k.aR,{children:[(0,r.jsxs)(k.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Monthly Leave Trends"})]}),(0,r.jsx)(k.BT,{children:"Leave requests by type over time"})]}),(0,r.jsx)(k.Wu,{children:(0,r.jsx)(C.u,{width:"100%",height:300,children:(0,r.jsxs)(S.E,{data:Z,children:[(0,r.jsx)(q.d,{strokeDasharray:"3 3"}),(0,r.jsx)(R.W,{dataKey:"month"}),(0,r.jsx)(z.h,{}),(0,r.jsx)(L.m,{}),(0,r.jsx)(T.s,{}),(0,r.jsx)(M.y,{dataKey:"vacation",stackId:"a",fill:"#0088FE",name:"Vacation"}),(0,r.jsx)(M.y,{dataKey:"sick",stackId:"a",fill:"#00C49F",name:"Sick Leave"}),(0,r.jsx)(M.y,{dataKey:"personal",stackId:"a",fill:"#FFBB28",name:"Personal"}),(0,r.jsx)(M.y,{dataKey:"training",stackId:"a",fill:"#FF8042",name:"Training"})]})})})]}),(0,r.jsxs)(k.Zp,{children:[(0,r.jsxs)(k.aR,{children:[(0,r.jsxs)(k.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Leave Type Distribution"})]}),(0,r.jsx)(k.BT,{children:"Breakdown of leave types"})]}),(0,r.jsx)(k.Wu,{children:(0,r.jsx)(C.u,{width:"100%",height:300,children:(0,r.jsxs)(I.r,{children:[(0,r.jsx)(Pie,{data:F,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,value:t})=>`${e} ${t}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:F.map((e,t)=>(0,r.jsx)(W.f,{fill:e.color},`cell-${t}`))}),(0,r.jsx)(L.m,{})]})})})]})]})]})}},43649:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},56651:(e,t,a)=>{"use strict";a.d(t,{r:()=>j});var r=a(43210),s=a(49605),n=a(64231),l=a(13420),i=a(71680),c=a(25893),o=a(43209);function d(e){return(0,o.j)(),null}a(61645);var p=a(2264),u=a(73865),m=a(12128),h=["width","height","layout"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},f=(0,r.forwardRef)(function(e,t){var a,s=(0,u.e)(e.categoricalChartProps,v),{width:o,height:f,layout:g}=s,y=function(e,t){if(null==e)return{};var a,r,s=function(e,t){if(null==e)return{};var a={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;a[r]=e[r]}return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)a=n[r],-1===t.indexOf(a)&&({}).propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}(s,h);if(!(0,m.F)(o)||!(0,m.F)(f))return null;var{chartName:j,defaultTooltipEventType:b,validateTooltipEventTypes:N,tooltipPayloadSearcher:w}=e;return r.createElement(n.J,{preloadedState:{options:{chartName:j,defaultTooltipEventType:b,validateTooltipEventTypes:N,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(a=s.id)?a:j},r.createElement(l.TK,{chartData:s.data}),r.createElement(i.s,{width:o,height:f,layout:g,margin:s.margin}),r.createElement(c.p,{accessibilityLayer:s.accessibilityLayer,barCategoryGap:s.barCategoryGap,maxBarSize:s.maxBarSize,stackOffset:s.stackOffset,barGap:s.barGap,barSize:s.barSize,syncId:s.syncId,syncMethod:s.syncMethod,className:s.className}),r.createElement(d,{cx:s.cx,cy:s.cy,startAngle:s.startAngle,endAngle:s.endAngle,innerRadius:s.innerRadius,outerRadius:s.outerRadius}),r.createElement(p.L,x({width:o,height:f},y,{ref:t})))}),g=["item"],y={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},j=(0,r.forwardRef)((e,t)=>{var a=(0,u.e)(e,y);return r.createElement(f,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:g,tooltipPayloadSearcher:s.uN,categoricalChartProps:a,ref:t})})},57800:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},62805:(e,t,a)=>{Promise.resolve().then(a.bind(a,39657))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},67760:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},72710:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=a(65239),s=a(48088),n=a(88170),l=a.n(n),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let o={children:["",{children:["dashboard",{children:["leave",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,97175)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/leave/page",pathname:"/dashboard/leave",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},97175:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\leave\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\leave\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,912,486,722,658,632,203,557,188],()=>a(72710));module.exports=r})();