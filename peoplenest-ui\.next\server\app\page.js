(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12872:()=>{},16189:(e,t,r)=>{"use strict";var n=r(65773);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19731:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx","default")},22396:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},26024:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>p});var n=r(65239),a=r(48088),i=r(88170),s=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64252:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(60687);r(43210);var a=r(16189),i=r(50371),s=r(17313),o=r(41862);function l(){return(0,a.useRouter)(),(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center",children:(0,n.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center",children:[(0,n.jsx)(i.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,duration:.3},className:"inline-flex items-center justify-center w-20 h-20 bg-primary rounded-3xl mb-6 shadow-lg",children:(0,n.jsx)(s.A,{className:"w-10 h-10 text-primary-foreground"})}),(0,n.jsx)(i.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4,duration:.3},className:"text-4xl font-bold text-foreground mb-4",children:"PeopleNest"}),(0,n.jsx)(i.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.3},className:"text-xl text-muted-foreground mb-8",children:"Enterprise HRMS Platform"}),(0,n.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.3},className:"flex items-center justify-center space-x-2 text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"w-5 h-5 animate-spin"}),(0,n.jsx)("span",{children:"Loading your workspace..."})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},82939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>l});var n=r(37413),a=r(58745),i=r.n(a),s=r(64066),o=r.n(s);r(61135);let l={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",themeColor:"#3b82f6",viewport:{width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover"},appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}};function p({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/icons/icon-192x192.png"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"PeopleNest"}),(0,n.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"application-name",content:"PeopleNest HRMS"}),(0,n.jsx)("meta",{name:"msapplication-TileColor",content:"#3b82f6"}),(0,n.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"}),(0,n.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"})]}),(0,n.jsxs)("body",{className:`${i().variable} ${o().variable} font-sans antialiased touch-manipulation`,children:[e,(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,912,486],()=>r(33218));module.exports=n})();