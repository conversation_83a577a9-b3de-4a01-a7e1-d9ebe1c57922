(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{14554:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>I});var a=t(95155),s=t(12115),n=t(35695),i=t(6874),l=t.n(i),o=t(17859),d=t(60760),c=t(73783),m=t(72713),x=t(17580),h=t(71007),u=t(69037),f=t(55868),g=t(33109),b=t(14186),p=t(69074),v=t(81497),y=t(57434),j=t(381),N=t(75525),w=t(59947),k=t(23227),A=t(13052),P=t(42355),z=t(34835),C=t(59434),E=t(30285),S=t(91394),D=t(26126);let F=[{title:"Overview",items:[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Analytics",icon:m.A,href:"/dashboard/analytics",badge:null}]},{title:"People Management",items:[{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"},{name:"Recruitment",icon:h.A,href:"/dashboard/recruitment",badge:"12"},{name:"Onboarding",icon:u.A,href:"/dashboard/onboarding",badge:"3"}]},{title:"Operations",items:[{name:"Payroll",icon:f.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:g.A,href:"/dashboard/performance",badge:"5"},{name:"Time & Attendance",icon:b.A,href:"/dashboard/attendance",badge:"2"},{name:"Leave Management",icon:p.A,href:"/dashboard/leave",badge:"8"}]},{title:"Communication",items:[{name:"Announcements",icon:v.A,href:"/dashboard/announcements",badge:"2"},{name:"Documents",icon:y.A,href:"/dashboard/documents",badge:null}]},{title:"System",items:[{name:"Settings",icon:j.A,href:"/dashboard/settings",badge:null},{name:"Compliance",icon:N.A,href:"/dashboard/compliance",badge:null},{name:"Help & Support",icon:w.A,href:"/dashboard/help",badge:null}]}];function R(e){let{className:r}=e,[t,i]=(0,s.useState)(!1),c=(0,n.usePathname)();return(0,a.jsxs)(o.P.div,{initial:!1,animate:{width:t?80:280},transition:{duration:.3,ease:"easeInOut"},className:(0,C.cn)("relative flex flex-col bg-white border-r border-gray-200 shadow-sm",r),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,a.jsx)(d.N,{mode:"wait",children:!t&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-primary rounded-lg",children:(0,a.jsx)(k.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"HRMS Platform"})]})]})}),(0,a.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>i(!t),className:"h-8 w-8 text-muted-foreground hover:text-foreground",children:t?(0,a.jsx)(A.A,{className:"h-4 w-4"}):(0,a.jsx)(P.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto py-4",children:(0,a.jsx)("nav",{className:"space-y-6 px-3",children:F.map((e,r)=>(0,a.jsxs)("div",{children:[(0,a.jsx)(d.N,{children:!t&&(0,a.jsx)(o.P.h3,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:e.title})}),(0,a.jsx)("ul",{className:"space-y-1",children:e.items.map(e=>{let r=c===e.href;return(0,a.jsx)("li",{children:(0,a.jsxs)(l(),{href:e.href,className:(0,C.cn)("w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",r?"bg-primary text-primary-foreground shadow-sm":"text-foreground hover:bg-muted hover:text-foreground"),children:[(0,a.jsx)(e.icon,{className:(0,C.cn)("flex-shrink-0 w-5 h-5",t?"mx-auto":"mr-3")}),(0,a.jsx)(d.N,{children:!t&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex items-center justify-between flex-1 min-w-0",children:[(0,a.jsx)("span",{className:"truncate",children:e.name}),e.badge&&(0,a.jsx)(D.E,{variant:r?"secondary":"outline",className:"ml-2 text-xs",children:e.badge})]})})]})},e.name)})})]},e.title))})}),(0,a.jsx)("div",{className:"border-t border-gray-200 p-4",children:(0,a.jsxs)("div",{className:(0,C.cn)("flex items-center",t?"justify-center":"space-x-3"),children:[(0,a.jsxs)(S.eu,{className:"h-8 w-8",children:[(0,a.jsx)(S.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,a.jsx)(S.q5,{children:"JD"})]}),(0,a.jsx)(d.N,{children:!t&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:"John Doe"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:"HR Manager"})]})}),(0,a.jsx)(d.N,{children:!t&&(0,a.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsx)(E.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(z.A,{className:"h-4 w-4"})})})})]})})]})}var H=t(47924),M=t(23861),_=t(74783),L=t(57340),$=t(54416);let B=[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"},{name:"Payroll",icon:f.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:g.A,href:"/dashboard/performance",badge:"5"},{name:"Leave",icon:p.A,href:"/dashboard/leave",badge:"8"},{name:"Settings",icon:j.A,href:"/dashboard/settings",badge:null}],O=[{name:"Search",icon:H.A,action:"search"},{name:"Notifications",icon:M.A,action:"notifications",badge:"3"},{name:"Profile",icon:h.A,action:"profile"}];function V(e){let{className:r}=e,[t,i]=(0,s.useState)(!1),[c,m]=(0,s.useState)("/dashboard"),x=(0,n.usePathname)();(0,s.useEffect)(()=>{m(x)},[x]),(0,s.useEffect)(()=>{i(!1)},[x]),(0,s.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]);let u=e=>{switch(e){case"search":console.log("Search triggered");break;case"notifications":console.log("Notifications triggered");break;case"profile":console.log("Profile triggered")}i(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>i(!0),className:"h-10 w-10",children:(0,a.jsx)(_.A,{className:"h-6 w-6"})}),(0,a.jsx)("div",{children:(0,a.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"})})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:O.map(e=>(0,a.jsxs)(E.$,{variant:"ghost",size:"icon",onClick:()=>u(e.action),className:"h-10 w-10 relative",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),e.badge&&(0,a.jsx)(D.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge})]},e.name))})]})}),(0,a.jsx)(d.N,{children:t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>i(!1),className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"}),(0,a.jsx)(o.P.div,{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"},transition:{type:"spring",damping:30,stiffness:300},className:"lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-white shadow-xl",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-primary rounded-lg flex items-center justify-center",children:(0,a.jsx)(L.A,{className:"h-6 w-6 text-primary-foreground"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"HRMS Dashboard"})]})]}),(0,a.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>i(!1),className:"h-10 w-10",children:(0,a.jsx)($.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto py-6",children:(0,a.jsx)("nav",{className:"px-6 space-y-2",children:B.map((e,r)=>{let t=c===e.href;return(0,a.jsx)(o.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*r},children:(0,a.jsxs)(l(),{href:e.href,onClick:()=>{m(e.href)},className:"\n                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200\n                              ".concat(t?"bg-primary/10 text-primary border border-primary/20":"text-muted-foreground hover:bg-muted hover:text-foreground","\n                            "),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat(t?"text-primary":"text-muted-foreground")}),(0,a.jsx)("span",{className:"font-medium",children:e.name}),e.badge&&(0,a.jsx)(D.E,{variant:t?"default":"secondary",className:"ml-auto",children:e.badge})]})},e.name)})})}),(0,a.jsx)("div",{className:"p-6 border-t border-border",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-foreground",children:"John Doe"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"HR Manager"})]})]})})]})})]})}),(0,a.jsx)("div",{className:"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2",children:(0,a.jsx)("div",{className:"flex items-center justify-around",children:B.slice(0,4).map(e=>{let r=c===e.href;return(0,a.jsxs)(l(),{href:e.href,onClick:()=>{console.log("Bottom nav link clicked:",e.href),m(e.href)},className:"\n                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative\n                  ".concat(r?"text-primary":"text-muted-foreground hover:text-foreground","\n                "),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:e.name}),e.badge&&(0,a.jsx)(D.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge}),r&&(0,a.jsx)(o.P.div,{layoutId:"activeTab",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"})]},e.name)})})})]})}function I(e){let{children:r}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,a.jsx)(R,{className:"hidden lg:flex"}),(0,a.jsx)(V,{}),(0,a.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,a.jsx)("main",{className:"flex-1 overflow-y-auto pt-16 lg:pt-0 pb-16 lg:pb-0",children:r})})]})}},26126:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var a=t(95155);t(12115);var s=t(74466),n=t(59434);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:s,icon:l,removable:o,onRemove:d,children:c,...m}=e;return(0,a.jsxs)("div",{className:(0,n.cn)(i({variant:t,size:s}),r),...m,children:[l&&(0,a.jsx)("span",{className:"mr-1",children:l}),c,o&&(0,a.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:d,children:(0,a.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(95155),s=t(12115),n=t(99708),i=t(74466),l=t(59434);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:d=!1,loading:c=!1,leftIcon:m,rightIcon:x,children:h,disabled:u,...f}=e,g=d?n.DX:"button";return(0,a.jsxs)(g,{className:(0,l.cn)(o({variant:s,size:i,className:t})),ref:r,disabled:u||c,...f,children:[c&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&m&&(0,a.jsx)("span",{className:"mr-2",children:m}),h,!c&&x&&(0,a.jsx)("span",{className:"ml-2",children:x})]})});d.displayName="Button"},59434:(e,r,t)=>{"use strict";t.d(r,{Ee:()=>l,cn:()=>n,vv:()=>i});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function i(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:r}).format(e)}function l(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},90105:(e,r,t)=>{Promise.resolve().then(t.bind(t,14554))},91394:(e,r,t)=>{"use strict";t.d(r,{BK:()=>d,eu:()=>o,q5:()=>c});var a=t(95155),s=t(12115),n=t(54011),i=t(59434);let l=(0,t(74466).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),o=s.forwardRef((e,r)=>{let{className:t,size:s,src:o,alt:m,fallback:x,status:h,...u}=e;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(n.bL,{ref:r,className:(0,i.cn)(l({size:s}),t),...u,children:[(0,a.jsx)(d,{src:o,alt:m}),(0,a.jsx)(c,{children:x})]}),h&&(0,a.jsx)("div",{className:(0,i.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===h,"bg-muted-foreground":"offline"===h,"bg-yellow-500":"away"===h,"bg-red-500":"busy"===h})})]})});o.displayName=n.bL.displayName;let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n._V,{ref:r,className:(0,i.cn)("aspect-square h-full w-full",t),...s})});d.displayName=n._V.displayName;let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.H4,{ref:r,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",t),...s})});c.displayName=n.H4.displayName}},e=>{var r=r=>e(e.s=r);e.O(0,[706,352,289,620,441,684,358],()=>r(90105)),_N_E=e.O()}]);