"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[262],{26126:(e,r,t)=>{t.d(r,{E:()=>i});var s=t(95155);t(12115);var a=t(74466),n=t(59434);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:r,variant:t,size:a,icon:i,removable:d,onRemove:o,children:c,...u}=e;return(0,s.jsxs)("div",{className:(0,n.cn)(l({variant:t,size:a}),r),...u,children:[i&&(0,s.jsx)("span",{className:"mr-1",children:i}),c,d&&(0,s.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:o,children:(0,s.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},30285:(e,r,t)=>{t.d(r,{$:()=>o});var s=t(95155),a=t(12115),n=t(99708),l=t(74466),i=t(59434);let d=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:o=!1,loading:c=!1,leftIcon:u,rightIcon:m,children:x,disabled:f,...h}=e,g=o?n.DX:"button";return(0,s.jsxs)(g,{className:(0,i.cn)(d({variant:a,size:l,className:t})),ref:r,disabled:f||c,...h,children:[c&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!c&&u&&(0,s.jsx)("span",{className:"mr-2",children:u}),x,!c&&m&&(0,s.jsx)("span",{className:"ml-2",children:m})]})});o.displayName="Button"},59434:(e,r,t)=>{t.d(r,{Ee:()=>i,cn:()=>n,vv:()=>l});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function l(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:r}).format(e)}function i(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},62523:(e,r,t)=>{t.d(r,{p:()=>i});var s=t(95155),a=t(12115),n=t(59434);let l=(0,t(74466).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,r)=>{let{className:t,type:i,variant:d,size:o,leftIcon:c,rightIcon:u,error:m,label:x,helperText:f,id:h,...g}=e,p=h||a.useId(),b=!!m;return(0,s.jsxs)("div",{className:"w-full",children:[x&&(0,s.jsx)("label",{htmlFor:p,className:"block text-sm font-medium text-foreground mb-1",children:x}),(0,s.jsxs)("div",{className:"relative",children:[c&&(0,s.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:c}),(0,s.jsx)("input",{type:i,className:(0,n.cn)(l({variant:b?"error":d,size:o,className:t}),c&&"pl-10",u&&"pr-10"),ref:r,id:p,...g}),u&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:u})]}),(m||f)&&(0,s.jsx)("p",{className:(0,n.cn)("mt-1 text-xs",b?"text-destructive":"text-muted-foreground"),children:m||f})]})});i.displayName="Input"},66695:(e,r,t)=>{t.d(r,{BT:()=>c,Wu:()=>u,ZB:()=>o,Zp:()=>i,aR:()=>d});var s=t(95155),a=t(12115),n=t(59434);let l=(0,t(74466).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),i=a.forwardRef((e,r)=>{let{className:t,variant:a,padding:i,hover:d=!1,...o}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)(l({variant:a,padding:i}),d&&"transition-shadow hover:shadow-md",t),...o})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});u.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},91394:(e,r,t)=>{t.d(r,{BK:()=>o,eu:()=>d,q5:()=>c});var s=t(95155),a=t(12115),n=t(54011),l=t(59434);let i=(0,t(74466).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,size:a,src:d,alt:u,fallback:m,status:x,...f}=e;return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(n.bL,{ref:r,className:(0,l.cn)(i({size:a}),t),...f,children:[(0,s.jsx)(o,{src:d,alt:u}),(0,s.jsx)(c,{children:m})]}),x&&(0,s.jsx)("div",{className:(0,l.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===x,"bg-muted-foreground":"offline"===x,"bg-yellow-500":"away"===x,"bg-red-500":"busy"===x})})]})});d.displayName=n.bL.displayName;let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n._V,{ref:r,className:(0,l.cn)("aspect-square h-full w-full",t),...a})});o.displayName=n._V.displayName;let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.H4,{ref:r,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",t),...a})});c.displayName=n.H4.displayName},92262:(e,r,t)=>{t.d(r,{Y:()=>z});var s=t(95155),a=t(12115),n=t(60760),l=t(17859),i=t(84616),d=t(91788),o=t(53904),c=t(66932),u=t(47924),m=t(23861),x=t(81497),f=t(66474),h=t(71007),g=t(381),p=t(93509),b=t(34869),v=t(34835),j=t(30285),N=t(62523),y=t(91394),w=t(26126),k=t(66695),C=t(59434);function z(e){let{title:r,subtitle:t,actions:z,className:A}=e,[R,F]=(0,a.useState)(!1),[D,S]=(0,a.useState)(!1),[V,B]=(0,a.useState)(""),E=[{id:1,title:"New Employee Onboarding",message:"Sarah Johnson has completed her onboarding checklist",time:"2 minutes ago",unread:!0,type:"success"},{id:2,title:"Leave Request Pending",message:"Mike Chen has requested 3 days of annual leave",time:"1 hour ago",unread:!0,type:"warning"},{id:3,title:"Performance Review Due",message:"5 performance reviews are due this week",time:"3 hours ago",unread:!1,type:"info"}],L=[{name:"Add Employee",icon:i.A,action:()=>{}},{name:"Export Data",icon:d.A,action:()=>{}},{name:"Refresh",icon:o.A,action:()=>{}},{name:"Filter",icon:c.A,action:()=>{}}];return(0,s.jsx)("header",{className:(0,C.cn)("sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm",A),children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)("div",{children:[r&&(0,s.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:r}),t&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]})}),(0,s.jsx)("div",{className:"flex-1 max-w-md mx-8",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(N.p,{type:"text",placeholder:"Search employees, documents, or anything...",value:V,onChange:e=>B(e.target.value),className:"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors"})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[z&&(0,s.jsx)("div",{className:"flex items-center space-x-2 mr-4",children:z}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:L.map(e=>(0,s.jsx)(j.$,{variant:"ghost",size:"icon",onClick:e.action,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:e.name,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},e.name))}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(j.$,{variant:"ghost",size:"icon",onClick:()=>F(!R),className:"h-9 w-9 text-muted-foreground hover:text-foreground relative",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),E.some(e=>e.unread)&&(0,s.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"})]}),(0,s.jsx)(n.N,{children:R&&(0,s.jsx)(l.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-80 z-50",children:(0,s.jsx)(k.Zp,{className:"shadow-lg border-border",children:(0,s.jsxs)(k.Wu,{className:"p-0",children:[(0,s.jsx)("div",{className:"p-4 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground",children:"Notifications"}),(0,s.jsxs)(w.E,{variant:"secondary",className:"text-xs",children:[E.filter(e=>e.unread).length," new"]})]})}),(0,s.jsx)("div",{className:"max-h-80 overflow-y-auto",children:E.map(e=>(0,s.jsx)("div",{className:(0,C.cn)("p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors",e.unread&&"bg-primary/5"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:(0,C.cn)("w-2 h-2 rounded-full mt-2 flex-shrink-0","success"===e.type&&"bg-green-500","warning"===e.type&&"bg-yellow-500","info"===e.type&&"bg-primary")}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-2",children:e.time})]})]})},e.id))}),(0,s.jsx)("div",{className:"p-3 border-t border-border",children:(0,s.jsx)(j.$,{variant:"ghost",className:"w-full text-sm",children:"View all notifications"})})]})})})})]}),(0,s.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-9 w-9 text-muted-foreground hover:text-foreground",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(j.$,{variant:"ghost",onClick:()=>S(!D),className:"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80",children:[(0,s.jsxs)(y.eu,{className:"h-7 w-7",children:[(0,s.jsx)(y.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,s.jsx)(y.q5,{children:"JD"})]}),(0,s.jsx)(f.A,{className:"h-3 w-3"})]}),(0,s.jsx)(n.N,{children:D&&(0,s.jsx)(l.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-56 z-50",children:(0,s.jsx)(k.Zp,{className:"shadow-lg border-gray-200",children:(0,s.jsxs)(k.Wu,{className:"p-0",children:[(0,s.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(y.eu,{className:"h-10 w-10",children:[(0,s.jsx)(y.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,s.jsx)(y.q5,{children:"JD"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:"John Doe"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"HR Manager"})]})]})}),(0,s.jsxs)("div",{className:"py-2",children:[[{icon:h.A,label:"Profile",href:"/dashboard"},{icon:g.A,label:"Settings",href:"/dashboard"},{icon:p.A,label:"Dark Mode",href:"#"},{icon:b.A,label:"Language",href:"#"}].map(e=>(0,s.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:[(0,s.jsx)(e.icon,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.label})]},e.label)),(0,s.jsx)("hr",{className:"my-2"}),(0,s.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Sign out"})]})]})]})})})})]})]})]})})}}}]);