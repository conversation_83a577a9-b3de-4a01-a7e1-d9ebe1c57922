(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[437],{65041:(e,s,a)=>{Promise.resolve().then(a.bind(a,82203))},82203:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>J});var t=a(95155),l=a(12115),n=a(17859),r=a(40646),c=a(1243),d=a(54861),i=a(14186),x=a(4516),o=a(67312),m=a(91788),h=a(84616),p=a(17580),j=a(33109),u=a(47924),N=a(70306),f=a(34835),y=a(46561),g=a(92657),v=a(13717),b=a(69074),w=a(30285),k=a(62523),A=a(66695),T=a(26126),D=a(92262),W=a(91394),E=a(83540),C=a(93504),Z=a(94754),I=a(96025),R=a(52071),O=a(85755),P=a(64683),S=a(13279),z=a(3401),L=a(56690);let $=[{id:1,employeeId:1,employeeName:"Sarah Johnson",department:"Engineering",date:"2024-01-25",clockIn:"09:00",clockOut:"17:30",breakTime:"60",totalHours:"7.5",status:"present",location:"Office",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"Mike Chen",department:"Product",date:"2024-01-25",clockIn:"08:45",clockOut:"17:15",breakTime:"45",totalHours:"7.75",status:"present",location:"Remote",avatar:"/avatars/mike.jpg"},{id:3,employeeId:3,employeeName:"Emily Davis",department:"Design",date:"2024-01-25",clockIn:"09:15",clockOut:"18:00",breakTime:"75",totalHours:"7.5",status:"late",location:"Office",avatar:"/avatars/emily.jpg"},{id:4,employeeId:4,employeeName:"David Wilson",department:"Engineering",date:"2024-01-25",clockIn:null,clockOut:null,breakTime:null,totalHours:"0",status:"absent",location:null,avatar:"/avatars/david.jpg"}],H=[{date:"Jan 20",present:95,late:3,absent:2},{date:"Jan 21",present:97,late:2,absent:1},{date:"Jan 22",present:94,late:4,absent:2},{date:"Jan 23",present:96,late:2,absent:2},{date:"Jan 24",present:98,late:1,absent:1},{date:"Jan 25",present:93,late:5,absent:2}],K=[{department:"Engineering",present:45,late:2,absent:1},{department:"Product",present:28,late:1,absent:1},{department:"Design",present:22,late:2,absent:0},{department:"Sales",present:35,late:3,absent:2},{department:"Marketing",present:18,late:1,absent:1}],B={totalEmployees:150,presentToday:140,lateToday:8,absentToday:2,avgHoursPerDay:7.6,attendanceRate:93.3};function J(){let[e,s]=(0,l.useState)(""),[a,J]=(0,l.useState)("All"),[M,_]=(0,l.useState)("All"),[q,F]=(0,l.useState)("2024-01-25"),[G,U]=(0,l.useState)("daily"),V=$.filter(s=>{let t=s.employeeName.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),l="All"===a||s.status===a,n="All"===M||s.department===M;return t&&l&&n}),Y=e=>{switch(e){case"present":return"success";case"late":return"warning";case"absent":return"destructive";default:return"secondary"}},Q=e=>{switch(e){case"present":return(0,t.jsx)(r.A,{className:"h-4 w-4"});case"late":return(0,t.jsx)(c.A,{className:"h-4 w-4"});case"absent":return(0,t.jsx)(d.A,{className:"h-4 w-4"});default:return(0,t.jsx)(i.A,{className:"h-4 w-4"})}},X=e=>e?"Office"===e?(0,t.jsx)(x.A,{className:"h-3 w-3"}):(0,t.jsx)(o.A,{className:"h-3 w-3"}):null;return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(D.Y,{title:"Time & Attendance",subtitle:"Tracking attendance for ".concat(B.totalEmployees," employees"),actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"date",value:q,onChange:e=>F(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"}),(0,t.jsxs)(w.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,t.jsxs)(w.$,{size:"sm",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Manual Entry"]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:B.totalEmployees}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Employees"})]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:B.presentToday}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Present Today"})]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:B.lateToday}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Late Today"})]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:B.absentToday}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Absent Today"})]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:B.avgHoursPerDay}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Hours/Day"})]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[B.attendanceRate,"%"]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendance Rate"})]})]})})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(w.$,{variant:"daily"===G?"default":"outline",size:"sm",onClick:()=>U("daily"),children:"Daily View"}),(0,t.jsx)(w.$,{variant:"trends"===G?"default":"outline",size:"sm",onClick:()=>U("trends"),children:"Trends"}),(0,t.jsx)(w.$,{variant:"reports"===G?"default":"outline",size:"sm",onClick:()=>U("reports"),children:"Reports"})]}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(k.p,{placeholder:"Search by employee name or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:a,onChange:e=>J(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Status"}),(0,t.jsx)("option",{value:"present",children:"Present"}),(0,t.jsx)("option",{value:"late",children:"Late"}),(0,t.jsx)("option",{value:"absent",children:"Absent"})]}),(0,t.jsxs)("select",{value:M,onChange:e=>_(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Departments"}),(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"}),(0,t.jsx)("option",{value:"Sales",children:"Sales"}),(0,t.jsx)("option",{value:"Marketing",children:"Marketing"})]})]})]})})}),"daily"===G?(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Department"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Clock In"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Clock Out"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Break Time"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Total Hours"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Location"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:V.map((e,s)=>(0,t.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(W.eu,{className:"h-10 w-10",children:[(0,t.jsx)(W.BK,{src:e.avatar,alt:e.employeeName}),(0,t.jsx)(W.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",e.employeeId]})]})]})}),(0,t.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[e.clockIn&&(0,t.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"font-medium",children:e.clockIn||"N/A"})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[e.clockOut&&(0,t.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"font-medium",children:e.clockOut||"N/A"})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[e.breakTime&&(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{children:e.breakTime?"".concat(e.breakTime," min"):"N/A"})]})}),(0,t.jsxs)("td",{className:"py-4 px-6 font-medium",children:[e.totalHours,"h"]}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)(T.E,{variant:Y(e.status),className:"flex items-center space-x-1 w-fit",children:[Q(e.status),(0,t.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:e.location&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[X(e.location),(0,t.jsx)("span",{className:"text-sm",children:e.location})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(w.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})}),(0,t.jsx)(w.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"trends"===G?(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(A.Zp,{children:[(0,t.jsxs)(A.aR,{children:[(0,t.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Daily Attendance Trends"})]}),(0,t.jsx)(A.BT,{children:"Attendance patterns over the last week"})]}),(0,t.jsx)(A.Wu,{children:(0,t.jsx)(E.u,{width:"100%",height:300,children:(0,t.jsxs)(C.b,{data:H,children:[(0,t.jsx)(Z.d,{strokeDasharray:"3 3"}),(0,t.jsx)(I.W,{dataKey:"date"}),(0,t.jsx)(R.h,{}),(0,t.jsx)(O.m,{}),(0,t.jsx)(P.s,{}),(0,t.jsx)(S.N,{type:"monotone",dataKey:"present",stroke:"#10b981",strokeWidth:2,name:"Present"}),(0,t.jsx)(S.N,{type:"monotone",dataKey:"late",stroke:"#f59e0b",strokeWidth:2,name:"Late"}),(0,t.jsx)(S.N,{type:"monotone",dataKey:"absent",stroke:"#ef4444",strokeWidth:2,name:"Absent"})]})})})]}),(0,t.jsxs)(A.Zp,{children:[(0,t.jsxs)(A.aR,{children:[(0,t.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Department Attendance"})]}),(0,t.jsx)(A.BT,{children:"Attendance breakdown by department"})]}),(0,t.jsx)(A.Wu,{children:(0,t.jsx)(E.u,{width:"100%",height:300,children:(0,t.jsxs)(z.E,{data:K,children:[(0,t.jsx)(Z.d,{strokeDasharray:"3 3"}),(0,t.jsx)(I.W,{dataKey:"department"}),(0,t.jsx)(R.h,{}),(0,t.jsx)(O.m,{}),(0,t.jsx)(P.s,{}),(0,t.jsx)(L.y,{dataKey:"present",stackId:"a",fill:"#10b981",name:"Present"}),(0,t.jsx)(L.y,{dataKey:"late",stackId:"a",fill:"#f59e0b",name:"Late"}),(0,t.jsx)(L.y,{dataKey:"absent",stackId:"a",fill:"#ef4444",name:"Absent"})]})})})]})]}):(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)(A.Zp,{children:[(0,t.jsxs)(A.aR,{children:[(0,t.jsx)(A.ZB,{children:"Attendance Reports"}),(0,t.jsx)(A.BT,{children:"Generate and download attendance reports"})]}),(0,t.jsx)(A.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)(b.A,{className:"h-8 w-8 mx-auto text-blue-600"}),(0,t.jsx)("h3",{className:"font-medium",children:"Daily Report"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Today's attendance summary"}),(0,t.jsxs)(w.$,{size:"sm",className:"w-full",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)(i.A,{className:"h-8 w-8 mx-auto text-green-600"}),(0,t.jsx)("h3",{className:"font-medium",children:"Weekly Report"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last 7 days attendance"}),(0,t.jsxs)(w.$,{size:"sm",className:"w-full",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}),(0,t.jsx)(A.Zp,{children:(0,t.jsx)(A.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 mx-auto text-purple-600"}),(0,t.jsx)("h3",{className:"font-medium",children:"Monthly Report"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Full month analysis"}),(0,t.jsxs)(w.$,{size:"sm",className:"w-full",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})})]})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[706,352,289,384,932,162,715,262,441,684,358],()=>s(65041)),_N_E=e.O()}]);