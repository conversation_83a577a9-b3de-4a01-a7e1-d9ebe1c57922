exports.id=486,exports.ids=[486],exports.modules={2015:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return c},parseParameter:function(){return l}});let r=i(46143),n=i(71437),s=i(53293),a=i(72887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(t){let e=t.match(o);return e?u(e[2]):u(t)}function u(t){let e=t.startsWith("[")&&t.endsWith("]");e&&(t=t.slice(1,-1));let i=t.startsWith("...");return i&&(t=t.slice(3)),{key:t,repeat:i,optional:e}}function h(t,e,i){let r={},l=1,h=[];for(let c of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>c.startsWith(t)),a=c.match(o);if(t&&a&&a[2]){let{key:e,optional:i,repeat:n}=u(a[2]);r[e]={pos:l++,repeat:n,optional:i},h.push("/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)")}else if(a&&a[2]){let{key:t,repeat:e,optional:n}=u(a[2]);r[t]={pos:l++,repeat:e,optional:n},i&&a[1]&&h.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=e?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),h.push(o)}else h.push("/"+(0,s.escapeStringRegexp)(c));e&&a&&a[3]&&h.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:h.join(""),groups:r}}function c(t,e){let{includeSuffix:i=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:n=!1}=void 0===e?{}:e,{parameterizedRoute:s,groups:a}=h(t,i,r),o=s;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function p(t){let e,{interceptionMarker:i,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=t,{key:h,optional:c,repeat:p}=u(n),d=h.replace(/\W/g,"");o&&(d=""+o+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r());let m=d in a;o?a[d]=""+o+h:a[d]=h;let g=i?(0,s.escapeStringRegexp)(i):"";return e=m&&l?"\\k<"+d+">":p?"(?<"+d+">.+?)":"(?<"+d+">[^/]+?)",c?"(?:/"+g+e+")?":"/"+g+e}function d(t,e,i,l,u){let h,c=(h=0,()=>{let t="",e=++h;for(;e>0;)t+=String.fromCharCode(97+(e-1)%26),e=Math.floor((e-1)/26);return t}),d={},f=[];for(let h of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=n.INTERCEPTION_ROUTE_MARKERS.some(t=>h.startsWith(t)),a=h.match(o);if(t&&a&&a[2])f.push(p({getSafeRouteKey:c,interceptionMarker:a[1],segment:a[2],routeKeys:d,keyPrefix:e?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&f.push("/"+(0,s.escapeStringRegexp)(a[1]));let t=p({getSafeRouteKey:c,segment:a[2],routeKeys:d,keyPrefix:e?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(t=t.substring(1)),f.push(t)}else f.push("/"+(0,s.escapeStringRegexp)(h));i&&a&&a[3]&&f.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:d}}function f(t,e){var i,r,n;let s=d(t,e.prefixRouteKeys,null!=(i=e.includeSuffix)&&i,null!=(r=e.includePrefix)&&r,null!=(n=e.backreferenceDuplicateKeys)&&n),a=s.namedParameterizedRoute;return e.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...c(t,e),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function m(t,e){let{parameterizedRoute:i}=h(t,!1,!1),{catchAll:r=!0}=e;if("/"===i)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:n}=d(t,!1,!1,!1,!1);return{namedRegex:"^"+n+(r?"(?:(/.*)?)":"")+"$"}}},6341:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return d}});let r=i(79551),n=i(11959),s=i(12437),a=i(2015),o=i(78034),l=i(15526),u=i(72887),h=i(74722),c=i(46143),p=i(47912);function d(t,e,i){let n=(0,r.parse)(t.url,!0);for(let t of(delete n.search,Object.keys(n.query))){let r=t!==c.NEXT_QUERY_PARAM_PREFIX&&t.startsWith(c.NEXT_QUERY_PARAM_PREFIX),s=t!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&t.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(r||s||e.includes(t)||i&&Object.keys(i.groups).includes(t))&&delete n.query[t]}t.url=(0,r.format)(n)}function f(t,e,i){if(!i)return t;for(let r of Object.keys(i.groups)){let n,{optional:s,repeat:a}=i.groups[r],o=`[${a?"...":""}${r}]`;s&&(o=`[${o}]`);let l=e[r];n=Array.isArray(l)?l.map(t=>t&&encodeURIComponent(t)).join("/"):l?encodeURIComponent(l):"",t=t.replaceAll(o,n)}return t}function m(t,e,i,r){let n={};for(let s of Object.keys(e.groups)){let a=t[s];"string"==typeof a?a=(0,h.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(h.normalizeRscURL));let o=i[s],l=e.groups[s].optional;if((Array.isArray(o)?o.some(t=>Array.isArray(a)?a.some(e=>e.includes(t)):null==a?void 0:a.includes(t)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete t[s]),a&&"string"==typeof a&&e.groups[s].repeat&&(a=a.split("/")),a&&(n[s]=a)}return{params:n,hasValidParams:!0}}function g({page:t,i18n:e,basePath:i,rewrites:r,pageIsDynamic:h,trailingSlash:c,caseSensitive:g}){let y,v,x;return h&&(y=(0,a.getNamedRouteRegex)(t,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(t)),{handleRewrites:function(a,o){let p={},d=o.pathname,f=r=>{let u=(0,s.getPathMatch)(r.source+(c?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let f=u(o.pathname);if((r.has||r.missing)&&f){let t=(0,l.matchHas)(a,o.query,r.has,r.missing);t?Object.assign(f,t):f=!1}if(f){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:f,query:o.query});if(s.protocol)return!0;if(Object.assign(p,a,f),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(d=o.pathname))return!1;if(i&&(d=d.replace(RegExp(`^${i}`),"")||"/"),e){let t=(0,n.normalizeLocalePath)(d,e.locales);d=t.pathname,o.query.nextInternalLocale=t.detectedLocale||f.nextInternalLocale}if(d===t)return!0;if(h&&v){let t=v(d);if(t)return o.query={...o.query,...t},!0}}return!1};for(let t of r.beforeFiles||[])f(t);if(d!==t){let e=!1;for(let t of r.afterFiles||[])if(e=f(t))break;if(!e&&!(()=>{let e=(0,u.removeTrailingSlash)(d||"");return e===(0,u.removeTrailingSlash)(t)||(null==v?void 0:v(e))})()){for(let t of r.fallback||[])if(e=f(t))break}}return p},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(t){if(!y)return null;let{groups:e,routeKeys:i}=y,r=(0,o.getRouteMatcher)({re:{exec:t=>{let r=Object.fromEntries(new URLSearchParams(t));for(let[t,e]of Object.entries(r)){let i=(0,p.normalizeNextQueryParam)(t);i&&(r[i]=e,delete r[t])}let n={};for(let t of Object.keys(i)){let s=i[t];if(!s)continue;let a=e[s],o=r[t];if(!a.optional&&!o)return null;n[a.pos]=o}return n}},groups:e})(t);return r||null},normalizeDynamicRouteParams:(t,e)=>y&&x?m(t,y,x,e):{params:{},hasValidParams:!1},normalizeVercelUrl:(t,e)=>d(t,e,y),interpolateDynamicPath:(t,e)=>f(t,e,y)}}function y(t,e){return"string"==typeof t[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&t[c.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===e?t[c.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},7044:(t,e,i)=>{"use strict";i.d(e,{B:()=>r});let r="undefined"!=typeof window},7236:(t,e,i)=>{"use strict";i.d(e,{$:()=>s,q:()=>a});var r=i(68762);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,a,o,l]=n.match(r.S);return{[t]:parseFloat(s),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},7504:(t,e,i)=>{"use strict";i.d(e,{y:()=>a});var r=i(73063),n=i(12742),s=i(21874);let a={test:t=>s.B.test(t)||r.u.test(t)||n.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):n.V.test(t)?n.V.parse(t):r.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):n.V.transform(t),getAnimatableNone:t=>{let e=a.parse(t);return e.alpha=0,a.transform(e)}}},8304:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return c},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return h}});let r=i(12958),n=i(74722),s=i(70554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(t,e)=>e&&0!==e.length?`(?:\\.(${t.join("|")})|(\\.(${e.join("|")})))`:`(\\.(?:${t.join("|")}))`;function u(t,e,i){let n=(i?"":"?")+"$",s=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(e.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${l(e.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],e)}${n}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,e)}${n}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,e)}${n}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,e)}${n}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,e)}${n}`)],u=(0,r.normalizePathSep)(t);return o.some(t=>t.test(u))}function h(t){let e=t.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(t)&&u(e,[],!0)&&"/robots.txt"!==e&&"/manifest.webmanifest"!==e&&!e.endsWith("/sitemap.xml")}function c(t){return!(0,s.isAppRouteRoute)(t)&&u(t,[],!1)}function p(t){let e=(0,n.normalizeAppPath)(t).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==e[0]&&(e="/"+e),(0,s.isAppRouteRoute)(t)&&u(e,[],!1)}},12157:(t,e,i)=>{"use strict";i.d(e,{L:()=>r});let r=(0,i(43210).createContext)({})},12437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getPathMatch",{enumerable:!0,get:function(){return n}});let r=i(35362);function n(t,e){let i=[],n=(0,r.pathToRegexp)(t,i,{delimiter:"/",sensitive:"boolean"==typeof(null==e?void 0:e.sensitive)&&e.sensitive,strict:null==e?void 0:e.strict}),s=(0,r.regexpToFunction)((null==e?void 0:e.regexModifier)?new RegExp(e.regexModifier(n.source),n.flags):n,i);return(t,r)=>{if("string"!=typeof t)return!1;let n=s(t);if(!n)return!1;if(null==e?void 0:e.removeUnnamedParams)for(let t of i)"number"==typeof t.name&&delete n.params[t.name];return{...r,...n.params}}}},12742:(t,e,i)=>{"use strict";i.d(e,{V:()=>o});var r=i(95444),n=i(32874),s=i(97095),a=i(7236);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,s.a)(e))+", "+n.KN.transform((0,s.a)(i))+", "+(0,s.a)(r.X4.transform(a))+")"}},12958:(t,e)=>{"use strict";function i(t){return t.replace(/\\/g,"/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathSep",{enumerable:!0,get:function(){return i}})},14296:(t,e,i)=>{"use strict";i.d(e,{v:()=>n});var r=i(87556);class n{constructor(){this.subscriptions=[]}add(t){return(0,r.Kq)(this.subscriptions,t),()=>(0,r.Ai)(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},15124:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var r=i(43210);let n=i(7044).B?r.useLayoutEffect:r.useEffect},15526:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{compileNonPath:function(){return h},matchHas:function(){return u},parseDestination:function(){return c},prepareDestination:function(){return p}});let r=i(35362),n=i(53293),s=i(76759),a=i(71437),o=i(88212);function l(t){return t.replace(/__ESC_COLON_/gi,":")}function u(t,e,i,r){void 0===i&&(i=[]),void 0===r&&(r=[]);let n={},s=i=>{let r,s=i.key;switch(i.type){case"header":s=s.toLowerCase(),r=t.headers[s];break;case"cookie":r="cookies"in t?t.cookies[i.key]:(0,o.getCookieParser)(t.headers)()[i.key];break;case"query":r=e[s];break;case"host":{let{host:e}=(null==t?void 0:t.headers)||{};r=null==e?void 0:e.split(":",1)[0].toLowerCase()}}if(!i.value&&r)return n[function(t){let e="";for(let i=0;i<t.length;i++){let r=t.charCodeAt(i);(r>64&&r<91||r>96&&r<123)&&(e+=t[i])}return e}(s)]=r,!0;if(r){let t=RegExp("^"+i.value+"$"),e=Array.isArray(r)?r.slice(-1)[0].match(t):r.match(t);if(e)return Array.isArray(e)&&(e.groups?Object.keys(e.groups).forEach(t=>{n[t]=e.groups[t]}):"host"===i.type&&e[0]&&(n.host=e[0])),!0}return!1};return!(!i.every(t=>s(t))||r.some(t=>s(t)))&&n}function h(t,e){if(!t.includes(":"))return t;for(let i of Object.keys(e))t.includes(":"+i)&&(t=t.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return t=t.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+t,{validate:!1})(e).slice(1)}function c(t){let e=t.destination;for(let i of Object.keys({...t.params,...t.query}))i&&(e=e.replace(RegExp(":"+(0,n.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,s.parseUrl)(e),r=i.pathname;r&&(r=l(r));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let u=i.hash;return u&&(u=l(u)),{...i,pathname:r,hostname:o,href:a,hash:u}}function p(t){let e,i,n=Object.assign({},t.query),s=c(t),{hostname:o,query:u}=s,p=s.pathname;s.hash&&(p=""+p+s.hash);let d=[],f=[];for(let t of((0,r.pathToRegexp)(p,f),f))d.push(t.name);if(o){let t=[];for(let e of((0,r.pathToRegexp)(o,t),t))d.push(e.name)}let m=(0,r.compile)(p,{validate:!1});for(let[i,n]of(o&&(e=(0,r.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(n)?u[i]=n.map(e=>h(l(e),t.params)):"string"==typeof n&&(u[i]=h(l(n),t.params));let g=Object.keys(t.params).filter(t=>"nextInternalLocale"!==t);if(t.appendParamsToQuery&&!g.some(t=>d.includes(t)))for(let e of g)e in u||(u[e]=t.params[e]);if((0,a.isInterceptionRouteAppPath)(p))for(let e of p.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));if(i){"(..)(..)"===i?(t.params["0"]="(..)",t.params["1"]="(..)"):t.params["0"]=i;break}}try{let[r,n]=(i=m(t.params)).split("#",2);e&&(s.hostname=e(t.params)),s.pathname=r,s.hash=(n?"#":"")+(n||""),delete s.search}catch(t){if(t.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw t}return s.query={...n,...s.query},{newUrl:i,destQuery:u,parsedDestination:s}}},15547:(t,e,i)=>{"use strict";function r(t,e){return e?1e3/e*t:0}i.d(e,{f:()=>r})},17313:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18171:(t,e,i)=>{"use strict";i.d(e,{s:()=>n});var r=i(74479);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},19331:(t,e,i)=>{"use strict";i.d(e,{G:()=>h});var r=i(97819),n=i(83361),s=i(78205),a=i(66244),o=i(64068),l=i(97758),u=i(91955);function h(t,e,{clamp:i=!0,ease:c,mixer:p}={}){let d=t.length;if((0,a.V)(d===e.length,"Both input and output ranges must be the same length"),1===d)return()=>e[0];if(2===d&&e[0]===e[1])return()=>e[1];let f=t[0]===t[1];t[0]>t[d-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let a=[],o=i||r.W.mix||u.j,l=t.length-1;for(let i=0;i<l;i++){let r=o(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||n.l:e;r=(0,s.F)(t,r)}a.push(r)}return a}(e,c,p),g=m.length,y=i=>{if(f&&i<t[0])return e[0];let r=0;if(g>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=(0,o.q)(t[r],t[r+1],i);return m[r](n)};return i?e=>y((0,l.q)(t[0],t[d-1],e)):y}},21279:(t,e,i)=>{"use strict";i.d(e,{t:()=>r});let r=(0,i(43210).createContext)(null)},21874:(t,e,i)=>{"use strict";i.d(e,{B:()=>u});var r=i(97758),n=i(95444),s=i(97095),a=i(7236);let o=t=>(0,r.q)(0,255,t),l={...n.ai,transform:t=>Math.round(o(t))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.a)(n.X4.transform(r))+")"}},22238:(t,e,i)=>{"use strict";i.d(e,{j:()=>n,p:()=>a});let r=t=>e=>"string"==typeof e&&e.startsWith(t),n=r("--"),s=r("var(--"),a=t=>!!s(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},23671:(t,e,i)=>{"use strict";i.d(e,{Gt:()=>n,PP:()=>o,WG:()=>s,uv:()=>a});var r=i(83361);let{schedule:n,cancel:s,state:a,steps:o}=(0,i(69848).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},23736:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),i(44827);let r=i(42785);function n(t,e,i){void 0===i&&(i=!0);let n=new URL("http://n"),s=e?new URL(e,n):t.startsWith(".")?new URL("http://n"):n,{pathname:a,searchParams:o,search:l,hash:u,href:h,origin:c}=new URL(t,s);if(c!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+t),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:h.slice(c.length)}}},24325:(t,e,i)=>{"use strict";let r;i.d(e,{k:()=>o});var n=i(97819),s=i(23671);function a(){r=void 0}let o={now:()=>(void 0===r&&o.set(s.uv.isProcessing||n.W.useManualTiming?s.uv.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(a)}}},24342:(t,e,i)=>{"use strict";i.d(e,{OQ:()=>h,bt:()=>l});var r=i(14296),n=i(15547),s=i(24325),a=i(23671);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new r.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},26415:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{e.parse=function(e,i){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var n={},s=e.split(r),a=(i||{}).decode||t,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var h=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==n[h]&&(n[h]=function(t,e){try{return e(t)}catch(e){return t}}(c,a))}}return n},e.serialize=function(t,e,r){var s=r||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(t))throw TypeError("argument name is invalid");var o=a(e);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=t+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=decodeURIComponent,i=encodeURIComponent,r=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},30660:(t,e)=>{"use strict";function i(t){let e=5381;for(let i=0;i<t.length;i++)e=(e<<5)+e+t.charCodeAt(i)|0;return e>>>0}function r(t){return i(t).toString(36).slice(0,5)}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{djb2Hash:function(){return i},hexHash:function(){return r}})},31658:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return d}});let r=i(8304),n=function(t){return t&&t.__esModule?t:{default:t}}(i(78671)),s=i(6341),a=i(2015),o=i(30660),l=i(74722),u=i(12958),h=i(35499);function c(t){let e=n.default.dirname(t);if(t.endsWith("/sitemap"))return"";let i="";return e.split("/").some(t=>(0,h.isGroupSegment)(t)||(0,h.isParallelRouteSegment)(t))&&(i=(0,o.djb2Hash)(e).toString(36).slice(0,6)),i}function p(t,e,i){let r=(0,l.normalizeAppPath)(t),o=(0,a.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),h=(0,s.interpolateDynamicPath)(r,e,o),{name:p,ext:d}=n.default.parse(i),f=c(n.default.posix.join(t,p)),m=f?`-${f}`:"";return(0,u.normalizePathSep)(n.default.join(h,`${p}${m}${d}`))}function d(t){if(!(0,r.isMetadataPage)(t))return t;let e=t,i="";if("/robots"===t?e+=".txt":"/manifest"===t?e+=".webmanifest":i=c(t),!e.endsWith("/route")){let{dir:t,name:r,ext:s}=n.default.parse(e);e=n.default.posix.join(t,`${r}${i?`-${i}`:""}${s}`,"route")}return e}function f(t,e){let i=t.endsWith("/route"),r=i?t.slice(0,-6):t,n=r.endsWith("/sitemap")?".xml":"";return(e?`${r}/[__metadata_id__]`:`${r}${n}`)+(i?"/route":"")}},32582:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r=(0,i(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},32874:(t,e,i)=>{"use strict";i.d(e,{KN:()=>s,gQ:()=>u,px:()=>a,uj:()=>n,vh:()=>o,vw:()=>l});let r=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=r("deg"),s=r("%"),a=r("px"),o=r("vh"),l=r("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},35362:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{function t(t,e){void 0===e&&(e={});for(var i=function(t){for(var e=[],i=0;i<t.length;){var r=t[i];if("*"===r||"+"===r||"?"===r){e.push({type:"MODIFIER",index:i,value:t[i++]});continue}if("\\"===r){e.push({type:"ESCAPED_CHAR",index:i++,value:t[i++]});continue}if("{"===r){e.push({type:"OPEN",index:i,value:t[i++]});continue}if("}"===r){e.push({type:"CLOSE",index:i,value:t[i++]});continue}if(":"===r){for(var n="",s=i+1;s<t.length;){var a=t.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=t[s++];continue}break}if(!n)throw TypeError("Missing parameter name at "+i);e.push({type:"NAME",index:i,value:n}),i=s;continue}if("("===r){var o=1,l="",s=i+1;if("?"===t[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<t.length;){if("\\"===t[s]){l+=t[s++]+t[s++];continue}if(")"===t[s]){if(0==--o){s++;break}}else if("("===t[s]&&(o++,"?"!==t[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=t[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);e.push({type:"PATTERN",index:i,value:l}),i=s;continue}e.push({type:"CHAR",index:i,value:t[i++]})}return e.push({type:"END",index:i,value:""}),e}(t),r=e.prefixes,s=void 0===r?"./":r,a="[^"+n(e.delimiter||"/#?")+"]+?",o=[],l=0,u=0,h="",c=function(t){if(u<i.length&&i[u].type===t)return i[u++].value},p=function(t){var e=c(t);if(void 0!==e)return e;var r=i[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+t)},d=function(){for(var t,e="";t=c("CHAR")||c("ESCAPED_CHAR");)e+=t;return e};u<i.length;){var f=c("CHAR"),m=c("NAME"),g=c("PATTERN");if(m||g){var y=f||"";-1===s.indexOf(y)&&(h+=y,y=""),h&&(o.push(h),h=""),o.push({name:m||l++,prefix:y,suffix:"",pattern:g||a,modifier:c("MODIFIER")||""});continue}var v=f||c("ESCAPED_CHAR");if(v){h+=v;continue}if(h&&(o.push(h),h=""),c("OPEN")){var y=d(),x=c("NAME")||"",b=c("PATTERN")||"",P=d();p("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:y,suffix:P,modifier:c("MODIFIER")||""});continue}p("END")}return o}function i(t,e){void 0===e&&(e={});var i=s(e),r=e.encode,n=void 0===r?function(t){return t}:r,a=e.validate,o=void 0===a||a,l=t.map(function(t){if("object"==typeof t)return RegExp("^(?:"+t.pattern+")$",i)});return function(e){for(var i="",r=0;r<t.length;r++){var s=t[r];if("string"==typeof s){i+=s;continue}var a=e?e[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,h="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!h)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var c=0;c<a.length;c++){var p=n(a[c],s);if(o&&!l[r].test(p))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+p+'"');i+=s.prefix+p+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var p=n(String(a),s);if(o&&!l[r].test(p))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+p+'"');i+=s.prefix+p+s.suffix;continue}if(!u){var d=h?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+d)}}return i}}function r(t,e,i){void 0===i&&(i={});var r=i.decode,n=void 0===r?function(t){return t}:r;return function(i){var r=t.exec(i);if(!r)return!1;for(var s=r[0],a=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(t){if(void 0!==r[t]){var i=e[t-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=r[t].split(i.prefix+i.suffix).map(function(t){return n(t,i)}):o[i.name]=n(r[t],i)}}(l);return{path:s,index:a,params:o}}}function n(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(t){return t&&t.sensitive?"":"i"}function a(t,e,i){void 0===i&&(i={});for(var r=i.strict,a=void 0!==r&&r,o=i.start,l=i.end,u=i.encode,h=void 0===u?function(t){return t}:u,c="["+n(i.endsWith||"")+"]|$",p="["+n(i.delimiter||"/#?")+"]",d=void 0===o||o?"^":"",f=0;f<t.length;f++){var m=t[f];if("string"==typeof m)d+=n(h(m));else{var g=n(h(m.prefix)),y=n(h(m.suffix));if(m.pattern)if(e&&e.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";d+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+v}else d+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else d+="("+m.pattern+")"+m.modifier;else d+="(?:"+g+y+")"+m.modifier}}if(void 0===l||l)a||(d+=p+"?"),d+=i.endsWith?"(?="+c+")":"$";else{var x=t[t.length-1],b="string"==typeof x?p.indexOf(x[x.length-1])>-1:void 0===x;a||(d+="(?:"+p+"(?="+c+"))?"),b||(d+="(?="+p+"|"+c+")")}return new RegExp(d,s(i))}function o(e,i,r){if(e instanceof RegExp){if(!i)return e;var n=e.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return e}return Array.isArray(e)?RegExp("(?:"+e.map(function(t){return o(t,i,r).source}).join("|")+")",s(r)):a(t(e,r),i,r)}Object.defineProperty(e,"__esModule",{value:!0}),e.parse=t,e.compile=function(e,r){return i(t(e,r),r)},e.tokensToFunction=i,e.match=function(t,e){var i=[];return r(o(t,i,e),i,e)},e.regexpToFunction=r,e.tokensToRegexp=a,e.pathToRegexp=o})(),t.exports=e})()},39664:(t,e,i)=>{"use strict";i.d(e,{V:()=>h,f:()=>f});var r=i(7504);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(68762),a=i(97095);let o="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],a=0,h=e.replace(u,t=>(r.y.test(t)?(n.color.push(a),s.push(l),i.push(r.y.parse(t))):t.startsWith("var(")?(n.var.push(a),s.push("var"),i.push(t)):(n.number.push(a),s.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:h,indexes:n,types:s}}function c(t){return h(t).values}function p(t){let{split:e,types:i}=h(t),n=e.length;return t=>{let s="";for(let u=0;u<n;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===o?s+=(0,a.a)(t[u]):e===l?s+=r.y.transform(t[u]):s+=t[u]}return s}}let d=t=>"number"==typeof t?0:r.y.test(t)?r.y.getAnimatableNone(t):t,f={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.S)?.length||0)+(t.match(n)?.length||0)>0},parse:c,createTransformer:p,getAnimatableNone:function(t){let e=c(t);return p(t)(e.map(d))}}},42785:(t,e)=>{"use strict";function i(t){let e={};for(let[i,r]of t.entries()){let t=e[i];void 0===t?e[i]=r:Array.isArray(t)?t.push(r):e[i]=[t,r]}return e}function r(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;for(let[i,n]of Object.entries(t))if(Array.isArray(n))for(let t of n)e.append(i,r(t));else e.set(i,r(n));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,r]of e.entries())t.append(i,r)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},44827:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return d},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(t){let e,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>n.test(t);function a(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function o(){let{href:t}=window.location,e=a();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let r=await t.getInitialProps(e);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class f extends Error{}class m extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},50371:(t,e,i)=>{"use strict";function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function s(t,e,i,r){if("function"==typeof e){let[s,a]=n(r);e=e(void 0!==i?i:t.custom,s,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,a]=n(r);e=e(void 0!==i?i:t.custom,s,a)}return e}function a(t,e,i){let r=t.getProps();return s(r,e,void 0!==i?i:r.custom,t)}function o(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>nI});var l,u,h=i(23671);let c=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],p=new Set(c),d=new Set(["width","height","top","left","right","bottom",...c]);var f=i(24342);let m=t=>Array.isArray(t);var g=i(97819);let y=t=>!!(t&&t.getVelocity);function v(t,e){let i=t.getValue("willChange");if(y(i)&&i.add)return i.add(e);if(!i&&g.W.WillChange){let i=new g.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let x=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),b="data-"+x("framerAppearId");var P=i(78205),T=i(97758);let E=t=>1e3*t,w=t=>t/1e3;var A=i(24325);let S={layout:0,mainThread:0,waapi:0};var R=i(91955);let M=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>h.Gt.update(e,t),stop:()=>(0,h.WG)(e),now:()=>h.uv.isProcessing?h.uv.timestamp:A.k.now()}},k=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function C(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var j=i(15547);function V(t,e,i){let r=Math.max(e-5,0);return(0,j.f)(i-t(r),e-r)}let D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var O=i(66244);function _(t,e){return t*Math.sqrt(1-e*e)}let L=["duration","bounce"],F=["stiffness","damping","mass"];function N(t,e){return e.some(e=>void 0!==t[e])}function I(t=D.visualDuration,e=D.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:c,duration:p,velocity:d,isResolvedFromDuration:f}=function(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!N(t,F)&&N(t,L))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*(0,T.q)(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:D.mass,stiffness:r,damping:n}}else{let i=function({duration:t=D.duration,bounce:e=D.bounce,velocity:i=D.velocity,mass:r=D.mass}){let n,s;(0,O.$)(t<=E(D.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=(0,T.q)(D.minDamping,D.maxDamping,a),t=(0,T.q)(D.minDuration,D.maxDuration,w(t)),a<1?(n=e=>{let r=e*a,n=r*t;return .001-(r-i)/_(e,a)*Math.exp(-n)},s=e=>{let r=e*a*t,s=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-r),l=_(Math.pow(e,2),a);return(r*i+i-s)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=E(t),isNaN(o))return{stiffness:D.stiffness,damping:D.damping,duration:t};{let e=Math.pow(o,2)*r;return{stiffness:e,damping:2*a*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:D.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-w(r.velocity||0)}),m=d||0,g=h/(2*Math.sqrt(u*c)),y=o-a,v=w(Math.sqrt(u/c)),x=5>Math.abs(y);if(n||(n=x?D.restSpeed.granular:D.restSpeed.default),s||(s=x?D.restDelta.granular:D.restDelta.default),g<1){let t=_(v,g);i=e=>o-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return o-i*((m+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let b={calculatedDuration:f&&p||null,next:t=>{let e=i(t);if(f)l.done=t>=p;else{let r=0===t?m:0;g<1&&(r=0===t?E(m):V(i,t,e));let a=Math.abs(o-e)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(C(b),2e4),e=k(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function $({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let c,p,d=t[0],f={done:!1,value:d},m=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=d+y,x=void 0===a?v:a(v);x!==v&&(y=x-d);let b=t=>-y*Math.exp(-t/r),P=t=>x+b(t),T=t=>{let e=b(t),i=P(t);f.done=Math.abs(e)<=u,f.value=f.done?x:i},E=t=>{m(f.value)&&(c=t,p=I({keyframes:[f.value,g(f.value)],velocity:V(P,t,f.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return E(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==c||(e=!0,T(t),E(t)),void 0!==c&&t>=c)?p.next(t-c):(e||T(t),f)}}}I.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(C(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:w(n)}}(t,100,I);return t.ease=e.ease,t.duration=E(e.duration),t.type="keyframes",t};var B=i(83361);let U=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function W(t,e,i,r){if(t===e&&i===r)return B.l;let n=e=>(function(t,e,i,r,n){let s,a,o=0;do(s=U(a=e+(i-e)/2,r,n)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:U(n(t),e,r)}let q=W(.42,0,1,1),G=W(0,0,.58,1),X=W(.42,0,.58,1),z=t=>Array.isArray(t)&&"number"!=typeof t[0],K=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,H=t=>e=>1-t(1-e),Y=W(.33,1.53,.69,.99),Q=H(Y),Z=K(Q),J=t=>(t*=2)<1?.5*Q(t):.5*(2-Math.pow(2,-10*(t-1))),tt=t=>1-Math.sin(Math.acos(t)),te=H(tt),ti=K(tt),tr=t=>Array.isArray(t)&&"number"==typeof t[0],tn={linear:B.l,easeIn:q,easeInOut:X,easeOut:G,circIn:tt,circInOut:ti,circOut:te,backIn:Q,backInOut:Z,backOut:Y,anticipate:J},ts=t=>"string"==typeof t,ta=t=>{if(tr(t)){(0,O.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return W(e,i,r,n)}return ts(t)?((0,O.V)(void 0!==tn[t],`Invalid easing type '${t}'`),tn[t]):t};var to=i(19331),tl=i(64068),tu=i(68028);function th({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=z(r)?r.map(ta):ta(r),a={done:!1,value:e[0]},o=(n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=(0,tl.q)(0,e,r);t.push((0,tu.k)(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),l=(0,to.G)(o,e,{ease:Array.isArray(s)?s:e.map(()=>s||X).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=l(e),a.done=e>=t,a)}}let tc=t=>null!==t;function tp(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(tc),a=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return a&&void 0!==r?r:s[a]}let td={decay:$,inertia:$,tween:th,keyframes:th,spring:I};function tf(t){"string"==typeof t.type&&(t.type=td[t.type])}class tm{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let tg=t=>t/100;class ty extends tm{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.k.now()&&this.tick(A.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},S.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tf(t);let{type:e=th,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:a}=t,o=e||th;o!==th&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,P.F)(tg,(0,R.j)(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=C(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:p,type:d,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,r)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,p&&(i-=p/a)):"mirror"===c&&(x=s)),v=(0,T.q)(0,1,i)*a}let b=y?{done:!1,value:u[0]}:x.next(v);n&&(b.value=n(b.value));let{done:P}=b;y||null===o||(P=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return E&&d!==$&&(b.value=tp(u,this.options,m,this.speed)),f&&f(b.value),E&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return w(this.calculatedDuration)}get time(){return w(this.currentTime)}set time(t){t=E(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=w(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=M,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,S.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let tv=t=>180*t/Math.PI,tx=t=>tP(tv(Math.atan2(t[1],t[0]))),tb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tx,rotateZ:tx,skewX:t=>tv(Math.atan(t[1])),skewY:t=>tv(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tP=t=>((t%=360)<0&&(t+=360),t),tT=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),tE=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tT,scaleY:tE,scale:t=>(tT(t)+tE(t))/2,rotateX:t=>tP(tv(Math.atan2(t[6],t[5]))),rotateY:t=>tP(tv(Math.atan2(-t[2],t[0]))),rotateZ:tx,rotate:tx,skewX:t=>tv(Math.atan(t[4])),skewY:t=>tv(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tA(t){return+!!t.includes("scale")}function tS(t,e){let i,r;if(!t||"none"===t)return tA(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=tw,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tb,r=e}if(!r)return tA(e);let s=i[e],a=r[1].split(",").map(tM);return"function"==typeof s?s(a):a[s]}let tR=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tS(i,e)};function tM(t){return parseFloat(t.trim())}var tk=i(95444),tC=i(32874);let tj=t=>t===tk.ai||t===tC.px,tV=new Set(["x","y","z"]),tD=c.filter(t=>!tV.has(t)),tO={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tS(e,"x"),y:(t,{transform:e})=>tS(e,"y")};tO.translateX=tO.x,tO.translateY=tO.y;let t_=new Set,tL=!1,tF=!1,tN=!1;function tI(){if(tF){let t=Array.from(t_).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tD.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tF=!1,tL=!1,t_.forEach(t=>t.complete(tN)),t_.clear()}function t$(){t_.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tF=!0)})}class tB{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(t_.add(this),tL||(tL=!0,h.Gt.read(t$),h.Gt.resolveKeyframes(tI))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),t_.delete(this)}cancel(){"scheduled"===this.state&&(t_.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tU=t=>t.startsWith("--");function tW(t){let e;return()=>(void 0===e&&(e=t()),e)}let tq=tW(()=>void 0!==window.ScrollTimeline);var tG=i(82082);let tX={},tz=function(t,e){let i=tW(t);return()=>tX[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),tK=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tK([0,.65,.55,1]),circOut:tK([.55,0,1,.45]),backIn:tK([.31,.01,.66,-.59]),backOut:tK([.33,1.53,.69,.99])};function tY(t){return"function"==typeof t&&"applyToOptions"in t}class tQ extends tm{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,(0,O.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tY(t)&&tz()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?tz()?k(e,i):"ease-out":tr(e)?tK(e):Array.isArray(e)?e.map(e=>t(e,i)||tH.easeOut):tH[e]}(o,n);Array.isArray(c)&&(h.easing=c),tG.Q.value&&S.waapi++;let p={delay:r,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(p.pseudoElement=u);let d=t.animate(h,p);return tG.Q.value&&d.finished.finally(()=>{S.waapi--}),d}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=tp(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tU(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return w(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return w(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=E(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tq())?(this.animation.timeline=t,B.l):e(this)}}let tZ={anticipate:J,backInOut:Z,circInOut:ti};class tJ extends tQ{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tZ&&(t.ease=tZ[t.ease])}(t),tf(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ty({...s,autoplay:!1}),o=E(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}var t0=i(39664);let t1=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t0.f.test(t)||"0"===t)&&!t.startsWith("url("));var t2=i(18171);let t5=new Set(["opacity","clipPath","filter","transform"]),t4=tW(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t3 extends tm{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.k.now();let c={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:u,...h},p=u?.KeyframeResolver||tB;this.keyframeResolver=new p(a,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=A.k.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],a=t1(n,e),o=t1(s,e);return(0,O.$)(a===o,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tY(i))&&r)}(t,n,s,a)&&((g.W.instantAnimations||!o)&&u?.(tp(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:a}=t;if(!(0,t2.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return t4()&&i&&t5.has(i)&&("transform"!==i||!l)&&!o&&!r&&"mirror"!==n&&0!==s&&"inertia"!==a}(h)?new tJ({...h,element:h.motionValue.owner.current}):new ty(h);c.finished.then(()=>this.notifyFinished()).catch(B.l),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tN=!0,t$(),tI(),tN=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t7=t=>null!==t,t6={type:"spring",stiffness:500,damping:25,restSpeed:10},t9=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),t8={type:"keyframes",duration:.8},et={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ee=(t,{keyframes:e})=>e.length>2?t8:p.has(t)?t.startsWith("scale")?t9(e[1]):t6:et,ei=(t,e,i,r={},n,s)=>a=>{let l=o(r,t)||{},u=l.delay||r.delay||0,{elapsed:c=0}=r;c-=E(u);let p={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(p,ee(t,p)),p.duration&&(p.duration=E(p.duration)),p.repeatDelay&&(p.repeatDelay=E(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let d=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(d=!0)),(g.W.instantAnimations||g.W.skipAnimations)&&(d=!0,p.duration=0,p.delay=0),p.allowFlatten=!l.type&&!l.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(t7),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(p.keyframes,l);if(void 0!==t)return void h.Gt.update(()=>{p.onUpdate(t),p.onComplete()})}return l.isSync?new ty(p):new t3(p)};function er(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:l,...u}=e;r&&(s=r);let c=[],p=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let r=t.getValue(e,t.latestValues[e]??null),n=u[e];if(void 0===n||p&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(p,e))continue;let a={delay:i,...o(s||{},e)},l=r.get();if(void 0!==l&&!r.isAnimating&&!Array.isArray(n)&&n===l&&!a.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){let i=t.props[b];if(i){let t=window.MotionHandoffAnimation(i,e,h.Gt);null!==t&&(a.startTime=t,f=!0)}}v(t,e),r.start(ei(e,r,n,t.shouldReduceMotion&&d.has(e)?{type:!1}:a,t,f));let m=r.animation;m&&c.push(m)}return l&&Promise.all(c).then(()=>{h.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var s;let i=m(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,f.OQ)(i))}}(t,l)})}),c}function en(t,e,i={}){let r=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(er(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,r=0,n=1,s){let a=[],o=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>o-t*r;return Array.from(t.variantChildren).sort(es).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(en(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,s+r,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function es(t,e){return t.sortNodePosition(e)}function ea(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function eo(t){return"string"==typeof t||Array.isArray(t)}let el=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],eu=["initial",...el],eh=eu.length,ec=[...el].reverse(),ep=el.length;function ed(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ef(){return{animate:ed(!0),whileInView:ed(),whileHover:ed(),whileTap:ed(),whileDrag:ed(),whileFocus:ed(),exit:ed()}}class em{constructor(t){this.isMounted=!1,this.node=t}update(){}}class eg extends em{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>en(t,e,i)));else if("string"==typeof e)r=en(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;r=Promise.all(er(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ef(),n=!0,s=e=>(i,r)=>{let n=a(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<eh;t++){let r=eu[t],n=e.props[r];(eo(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},h=[],c=new Set,p={},d=1/0;for(let e=0;e<ep;e++){var f,g;let a=ec[e],y=i[a],v=void 0!==l[a]?l[a]:u[a],x=eo(v),b=a===o?y.isActive:null;!1===b&&(d=e);let P=v===u[a]&&v!==l[a]&&x;if(P&&n&&t.manuallyAnimateOnMount&&(P=!1),y.protectedKeys={...p},!y.isActive&&null===b||!v&&!y.prevProp||r(v)||"boolean"==typeof v)continue;let T=(f=y.prevProp,"string"==typeof(g=v)?g!==f:!!Array.isArray(g)&&!ea(g,f)),E=T||a===o&&y.isActive&&!P&&x||e>d&&x,w=!1,A=Array.isArray(v)?v:[v],S=A.reduce(s(a),{});!1===b&&(S={});let{prevResolvedValues:R={}}=y,M={...R,...S},k=e=>{E=!0,c.has(e)&&(w=!0,c.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=R[t];if(p.hasOwnProperty(t))continue;let r=!1;(m(e)&&m(i)?ea(e,i):e===i)?void 0!==e&&c.has(t)?k(t):y.protectedKeys[t]=!0:null!=e?k(t):c.add(t)}y.prevProp=v,y.prevResolvedValues=S,y.isActive&&(p={...p,...S}),n&&t.blockInitialAnimation&&(E=!1);let C=!(P&&T)||w;E&&C&&h.push(...A.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),h.push({animation:e})}let y=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ef(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ey=0;class ev extends em{constructor(){super(...arguments),this.id=ey++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ex={x:!1,y:!1};function eb(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let eP=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function eT(t){return{point:{x:t.pageX,y:t.pageY}}}let eE=t=>e=>eP(e)&&t(e,eT(e));function ew(t,e,i,r){return eb(t,e,eE(i),r)}function eA({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function eS(t){return t.max-t.min}function eR(t,e,i,r=.5){t.origin=r,t.originPoint=(0,tu.k)(e.min,e.max,t.origin),t.scale=eS(i)/eS(e),t.translate=(0,tu.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function eM(t,e,i,r){eR(t.x,e.x,i.x,r?r.originX:void 0),eR(t.y,e.y,i.y,r?r.originY:void 0)}function ek(t,e,i){t.min=i.min+e.min,t.max=t.min+eS(e)}function eC(t,e,i){t.min=e.min-i.min,t.max=t.min+eS(e)}function ej(t,e,i){eC(t.x,e.x,i.x),eC(t.y,e.y,i.y)}let eV=()=>({translate:0,scale:1,origin:0,originPoint:0}),eD=()=>({x:eV(),y:eV()}),eO=()=>({min:0,max:0}),e_=()=>({x:eO(),y:eO()});function eL(t){return[t("x"),t("y")]}function eF(t){return void 0===t||1===t}function eN({scale:t,scaleX:e,scaleY:i}){return!eF(t)||!eF(e)||!eF(i)}function eI(t){return eN(t)||e$(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function e$(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function eB(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function eU(t,e=0,i=1,r,n){t.min=eB(t.min,e,i,r,n),t.max=eB(t.max,e,i,r,n)}function eW(t,{x:e,y:i}){eU(t.x,e.translate,e.scale,e.originPoint),eU(t.y,i.translate,i.scale,i.originPoint)}function eq(t,e){t.min=t.min+e,t.max=t.max+e}function eG(t,e,i,r,n=.5){let s=(0,tu.k)(t.min,t.max,n);eU(t,e,i,s,r)}function eX(t,e){eG(t.x,e.x,e.scaleX,e.scale,e.originX),eG(t.y,e.y,e.scaleY,e.scale,e.originY)}function ez(t,e){return eA(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let eK=({current:t})=>t?t.ownerDocument.defaultView:null;function eH(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let eY=(t,e)=>Math.abs(t-e);class eQ{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=e0(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(eY(t.x,e.x)**2+eY(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=h.uv;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=eZ(e,this.transformPagePoint),h.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=e0("pointercancel"===t.type?this.lastMoveEventInfo:eZ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!eP(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=eZ(eT(t),this.transformPagePoint),{point:a}=s,{timestamp:o}=h.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,e0(s,this.history)),this.removeListeners=(0,P.F)(ew(this.contextWindow,"pointermove",this.handlePointerMove),ew(this.contextWindow,"pointerup",this.handlePointerUp),ew(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,h.WG)(this.updatePoint)}}function eZ(t,e){return e?{point:e(t.point)}:t}function eJ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function e0({point:t},e){return{point:t,delta:eJ(t,e1(e)),offset:eJ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=e1(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>E(.1)));)i--;if(!r)return{x:0,y:0};let s=w(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function e1(t){return t[t.length-1]}function e2(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function e5(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function e4(t,e,i){return{min:e3(t,e),max:e3(t,i)}}function e3(t,e){return"number"==typeof t?t:t[e]||0}let e7=new WeakMap;class e6{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=e_(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new eQ(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(eT(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ex[t])return null;else return ex[t]=!0,()=>{ex[t]=!1};return ex.x||ex.y?null:(ex.x=ex.y=!0,()=>{ex.x=ex.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),eL(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tC.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=eS(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&h.Gt.postRender(()=>n(t,e)),v(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>eL(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:eK(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&h.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!e9(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?(0,tu.k)(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?(0,tu.k)(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&eH(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:e2(t.x,i,n),y:e2(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:e4(t,"left","right"),y:e4(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&eL(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!eH(e))return!1;let r=e.current;(0,O.V)(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=ez(t,i),{scroll:n}=e;return n&&(eq(r.x,n.offset.x),eq(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:e5(t.x,s.x),y:e5(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=eA(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(eL(a=>{if(!e9(a,e,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return v(this.visualElement,t),i.start(ei(t,i,0,e,this.visualElement,!1))}stopAnimation(){eL(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){eL(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){eL(e=>{let{drag:i}=this.getProps();if(!e9(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-(0,tu.k)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!eH(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};eL(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=eS(t),n=eS(e);return n>r?i=(0,tl.q)(e.min,e.max-r,t.min):r>n&&(i=(0,tl.q)(t.min,t.max-n,e.min)),(0,T.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),eL(e=>{if(!e9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set((0,tu.k)(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;e7.set(this.visualElement,this);let t=ew(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();eH(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),h.Gt.read(e);let n=eb(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(eL(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function e9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class e8 extends em{constructor(t){super(t),this.removeGroupControls=B.l,this.removeListeners=B.l,this.controls=new e6(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let it=t=>(e,i)=>{t&&h.Gt.postRender(()=>t(e,i))};class ie extends em{constructor(){super(...arguments),this.removePointerDownListener=B.l}onPointerDown(t){this.session=new eQ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:eK(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:it(t),onStart:it(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&h.Gt.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=ew(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ii=i(60687);let{schedule:ir}=(0,i(69848).I)(queueMicrotask,!1);var is=i(43210),ia=i(86044),io=i(12157);let il=(0,is.createContext)({}),iu={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ih(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ic={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tC.px.test(t))return t;else t=parseFloat(t);let i=ih(t,e.target.x),r=ih(t,e.target.y);return`${i}% ${r}%`}};var ip=i(22238);let id={};class im extends is.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in iy)id[t]=iy[t],(0,ip.j)(t)&&(id[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iu.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||h.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ir.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ig(t){let[e,i]=(0,ia.xQ)(),r=(0,is.useContext)(io.L);return(0,ii.jsx)(im,{...t,layoutGroup:r,switchLayoutGroup:(0,is.useContext)(il),isPresent:e,safeToRemove:i})}let iy={borderRadius:{...ic,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ic,borderTopRightRadius:ic,borderBottomLeftRadius:ic,borderBottomRightRadius:ic,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=t0.f.parse(t);if(r.length>5)return t;let n=t0.f.createTransformer(t),s=+("number"!=typeof r[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;r[0+s]/=a,r[1+s]/=o;let l=(0,tu.k)(a,o,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var iv=i(74479);function ix(t){return(0,iv.G)(t)&&"ownerSVGElement"in t}var ib=i(14296),iP=i(87556);let iT=(t,e)=>t.depth-e.depth;class iE{constructor(){this.children=[],this.isDirty=!1}add(t){(0,iP.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,iP.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iT),this.isDirty=!1,this.children.forEach(t)}}function iw(t){return y(t)?t.get():t}let iA=["TopLeft","TopRight","BottomLeft","BottomRight"],iS=iA.length,iR=t=>"string"==typeof t?parseFloat(t):t,iM=t=>"number"==typeof t||tC.px.test(t);function ik(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iC=iV(0,.5,te),ij=iV(.5,.95,B.l);function iV(t,e,i){return r=>r<t?0:r>e?1:i((0,tl.q)(t,e,r))}function iD(t,e){t.min=e.min,t.max=e.max}function iO(t,e){iD(t.x,e.x),iD(t.y,e.y)}function i_(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function iL(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function iF(t,e,[i,r,n],s,a){!function(t,e=0,i=1,r=.5,n,s=t,a=t){if(tC.KN.test(e)&&(e=parseFloat(e),e=(0,tu.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,tu.k)(s.min,s.max,r);t===s&&(o-=e),t.min=iL(t.min,e,i,o,n),t.max=iL(t.max,e,i,o,n)}(t,e[i],e[r],e[n],e.scale,s,a)}let iN=["x","scaleX","originX"],iI=["y","scaleY","originY"];function i$(t,e,i,r){iF(t.x,e,iN,i?i.x:void 0,r?r.x:void 0),iF(t.y,e,iI,i?i.y:void 0,r?r.y:void 0)}function iB(t){return 0===t.translate&&1===t.scale}function iU(t){return iB(t.x)&&iB(t.y)}function iW(t,e){return t.min===e.min&&t.max===e.max}function iq(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iG(t,e){return iq(t.x,e.x)&&iq(t.y,e.y)}function iX(t){return eS(t.x)/eS(t.y)}function iz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iK{constructor(){this.members=[]}add(t){(0,iP.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,iP.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iH={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iY=["","X","Y","Z"],iQ={visibility:"hidden"},iZ=0;function iJ(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function i0({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=iZ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tG.Q.value&&(iH.nodes=iH.calculatedTargetDeltas=iH.calculatedProjections=0),this.nodes.forEach(i5),this.nodes.forEach(rt),this.nodes.forEach(re),this.nodes.forEach(i4),tG.Q.addProjectionMetrics&&tG.Q.addProjectionMetrics(iH)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iE)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ib.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ix(e)&&!(ix(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.k.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&((0,h.WG)(r),t(s-e))};return h.Gt.setup(r,!0),()=>(0,h.WG)(r)}(r,250),iu.hasAnimatedSinceResize&&(iu.hasAnimatedSinceResize=!1,this.nodes.forEach(i8))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||ro,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=n.getProps(),u=!this.targetLayout||!iG(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...o(s,"layout"),onPlay:a,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||i8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,h.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ri),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[b];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",h.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i7);return}this.isUpdating||this.nodes.forEach(i6),this.isUpdating=!1,this.nodes.forEach(i9),this.nodes.forEach(i1),this.nodes.forEach(i2),this.clearAllSnapshots();let t=A.k.now();h.uv.delta=(0,T.q)(0,1e3/60,t-h.uv.timestamp),h.uv.timestamp=t,h.uv.isProcessing=!0,h.PP.update.process(h.uv),h.PP.preRender.process(h.uv),h.PP.render.process(h.uv),h.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ir.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(i3),this.sharedNodes.forEach(rr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,h.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){h.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||eS(this.snapshot.measuredBox.x)||eS(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=e_(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!iU(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||eI(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),rh((e=r).x),rh(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return e_();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rp))){let{scroll:t}=this.root;t&&(eq(e.x,t.offset.x),eq(e.y,t.offset.y))}return e}removeElementScroll(t){let e=e_();if(iO(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&iO(e,t),eq(e.x,n.offset.x),eq(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=e_();iO(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&eX(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),eI(r.latestValues)&&eX(i,r.latestValues)}return eI(this.latestValues)&&eX(i,this.latestValues),i}removeTransform(t){let e=e_();iO(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!eI(i.latestValues))continue;eN(i.latestValues)&&i.updateSnapshot();let r=e_();iO(r,i.measurePageBox()),i$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return eI(this.latestValues)&&i$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=h.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e_(),this.relativeTargetOrigin=e_(),ej(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=e_(),this.targetWithTransforms=e_()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,ek(s.x,a.x,o.x),ek(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iO(this.target,this.layout.layoutBox),eW(this.target,this.targetDelta)):iO(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e_(),this.relativeTargetOrigin=e_(),ej(this.relativeTargetOrigin,this.target,t.target),iO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tG.Q.value&&iH.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||eN(this.parent.latestValues)||e$(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===h.uv.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;iO(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,r=!1){let n,s,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){s=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eX(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,eW(t,s)),r&&eI(n.latestValues)&&eX(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=e_());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(i_(this.prevProjectionDelta.x,this.projectionDelta.x),i_(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),eM(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&iz(this.projectionDelta.x,this.prevProjectionDelta.x)&&iz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),tG.Q.value&&iH.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=eD(),this.projectionDelta=eD(),this.projectionDeltaWithTransform=eD()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=eD();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=e_(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(ra));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(rn(a.x,t.x,r),rn(a.y,t.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,p,d,f,m,g;ej(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,f=this.relativeTargetOrigin,m=o,g=r,rs(d.x,f.x,m.x,g),rs(d.y,f.y,m.y,g),i&&(u=this.relativeTarget,p=i,iW(u.x,p.x)&&iW(u.y,p.y))&&(this.isProjectionDirty=!1),i||(i=e_()),iO(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=(0,tu.k)(0,i.opacity??1,iC(r)),t.opacityExit=(0,tu.k)(e.opacity??1,0,ij(r))):s&&(t.opacity=(0,tu.k)(e.opacity??1,i.opacity??1,r));for(let n=0;n<iS;n++){let s=`border${iA[n]}Radius`,a=ik(e,s),o=ik(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||iM(a)===iM(o)?(t[s]=Math.max((0,tu.k)(iR(a),iR(o),r),0),(tC.KN.test(o)||tC.KN.test(a))&&(t[s]+="%")):t[s]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,tu.k)(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,h.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=h.Gt.update(()=>{iu.hasAnimatedSinceResize=!0,S.layout++,this.motionValue||(this.motionValue=(0,f.OQ)(0)),this.currentAnimation=function(t,e,i){let r=y(t)?t:(0,f.OQ)(t);return r.start(ei("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{S.layout--},onComplete:()=>{S.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&rc(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||e_();let e=eS(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=eS(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}iO(e,i),eX(e,n),eM(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iK),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&iJ("z",t,r,this.animationValues);for(let e=0;e<iY.length;e++)iJ(`rotate${iY[e]}`,t,r,this.animationValues),iJ(`skew${iY[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return iQ;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=iw(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=iw(t?.pointerEvents)||""),this.hasProjected&&!eI(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,a=i?.z||0;if((n||s||a)&&(r=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,id){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:a}=id[t],o="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=r===this?iw(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(i7),this.root.sharedNodes.clear()}}}function i1(t){t.updateLayout()}function i2(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?eL(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=eS(r);r.min=i[t].min,r.max=r.min+n}):rc(n,e.layoutBox,i)&&eL(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],a=eS(i[r]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+a)});let a=eD();eM(a,i,e.layoutBox);let o=eD();s?eM(o,t.applyTransform(r,!0),e.measuredBox):eM(o,i,e.layoutBox);let l=!iU(a),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=e_();ej(a,e.layoutBox,n.layoutBox);let o=e_();ej(o,i,s.layoutBox),iG(a,o)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i5(t){tG.Q.value&&iH.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function i4(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function i3(t){t.clearSnapshot()}function i7(t){t.clearMeasurements()}function i6(t){t.isLayoutDirty=!1}function i9(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i8(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rt(t){t.resolveTargetDelta()}function re(t){t.calcProjection()}function ri(t){t.resetSkewAndRotation()}function rr(t){t.removeLeadSnapshot()}function rn(t,e,i){t.translate=(0,tu.k)(e.translate,0,i),t.scale=(0,tu.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rs(t,e,i,r){t.min=(0,tu.k)(e.min,i.min,r),t.max=(0,tu.k)(e.max,i.max,r)}function ra(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ro={duration:.45,ease:[.4,0,.1,1]},rl=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ru=rl("applewebkit/")&&!rl("chrome/")?Math.round:B.l;function rh(t){t.min=ru(t.min),t.max=ru(t.max)}function rc(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iX(e)-iX(i)))}function rp(t){return t!==t.root&&t.scroll?.wasRoot}let rd=i0({attachResizeListener:(t,e)=>eb(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rf={current:void 0},rm=i0({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rf.current){let t=new rd({});t.mount(window),t.setOptions({layoutScroll:!0}),rf.current=t}return rf.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function rg(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function ry(t){return!("touch"===t.pointerType||ex.x||ex.y)}function rv(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&h.Gt.postRender(()=>n(e,eT(e)))}class rx extends em{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=rg(t,i),a=t=>{if(!ry(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{ry(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",a,n)}),s}(t,(t,e)=>(rv(this.node,e,"Start"),t=>rv(this.node,t,"End"))))}unmount(){}}class rb extends em{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,P.F)(eb(this.node.current,"focus",()=>this.onFocus()),eb(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rP=(t,e)=>!!e&&(t===e||rP(t,e.parentElement)),rT=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rE=new WeakSet;function rw(t){return e=>{"Enter"===e.key&&t(e)}}function rA(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rS=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=rw(()=>{if(rE.has(i))return;rA(i,"down");let t=rw(()=>{rA(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rA(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function rR(t){return eP(t)&&!(ex.x||ex.y)}function rM(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&h.Gt.postRender(()=>n(e,eT(e)))}class rk extends em{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=rg(t,i),a=t=>{let r=t.currentTarget;if(!rR(t))return;rE.add(r);let s=e(r,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rE.has(r)&&rE.delete(r),rR(t)&&"function"==typeof s&&s(t,{success:e})},o=t=>{a(t,r===window||r===document||i.useGlobalTarget||rP(r,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),(0,t2.s)(t))&&(t.addEventListener("focus",t=>rS(t,n)),rT.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rM(this.node,e,"Start"),(t,{success:e})=>rM(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rC=new WeakMap,rj=new WeakMap,rV=t=>{let e=rC.get(t.target);e&&e(t)},rD=t=>{t.forEach(rV)},rO={some:0,all:1};class r_ extends em{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:rO[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;rj.has(i)||rj.set(i,{});let r=rj.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(rD,{root:t,...e})),r[n]}(e);return rC.set(t,i),r.observe(t),()=>{rC.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rL=(0,is.createContext)({strict:!1});var rF=i(32582);let rN=(0,is.createContext)({});function rI(t){return r(t.animate)||eu.some(e=>eo(t[e]))}function r$(t){return!!(rI(t)||t.variants)}function rB(t){return Array.isArray(t)?t.join(" "):t}var rU=i(7044);let rW={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rq={};for(let t in rW)rq[t]={isEnabled:e=>rW[t].some(t=>!!e[t])};let rG=Symbol.for("motionComponentSymbol");var rX=i(21279),rz=i(15124);function rK(t,{layout:e,layoutId:i}){return p.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!id[t]||"opacity"===t)}let rH=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rY={...tk.ai,transform:Math.round},rQ={rotate:tC.uj,rotateX:tC.uj,rotateY:tC.uj,rotateZ:tC.uj,scale:tk.hs,scaleX:tk.hs,scaleY:tk.hs,scaleZ:tk.hs,skew:tC.uj,skewX:tC.uj,skewY:tC.uj,distance:tC.px,translateX:tC.px,translateY:tC.px,translateZ:tC.px,x:tC.px,y:tC.px,z:tC.px,perspective:tC.px,transformPerspective:tC.px,opacity:tk.X4,originX:tC.gQ,originY:tC.gQ,originZ:tC.px},rZ={borderWidth:tC.px,borderTopWidth:tC.px,borderRightWidth:tC.px,borderBottomWidth:tC.px,borderLeftWidth:tC.px,borderRadius:tC.px,radius:tC.px,borderTopLeftRadius:tC.px,borderTopRightRadius:tC.px,borderBottomRightRadius:tC.px,borderBottomLeftRadius:tC.px,width:tC.px,maxWidth:tC.px,height:tC.px,maxHeight:tC.px,top:tC.px,right:tC.px,bottom:tC.px,left:tC.px,padding:tC.px,paddingTop:tC.px,paddingRight:tC.px,paddingBottom:tC.px,paddingLeft:tC.px,margin:tC.px,marginTop:tC.px,marginRight:tC.px,marginBottom:tC.px,marginLeft:tC.px,backgroundPositionX:tC.px,backgroundPositionY:tC.px,...rQ,zIndex:rY,fillOpacity:tk.X4,strokeOpacity:tk.X4,numOctaves:rY},rJ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},r0=c.length;function r1(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(p.has(t)){a=!0;continue}if((0,ip.j)(t)){n[t]=i;continue}{let e=rH(i,rZ[t]);t.startsWith("origin")?(o=!0,s[t]=e):r[t]=e}}if(!e.transform&&(a||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<r0;s++){let a=c[s],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=rH(o,rZ[a]);if(!l){n=!1;let e=rJ[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let r2=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function r5(t,e,i){for(let r in e)y(e[r])||rK(r,i)||(t[r]=e[r])}let r4={offset:"stroke-dashoffset",array:"stroke-dasharray"},r3={offset:"strokeDashoffset",array:"strokeDasharray"};function r7(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:a=0,...o},l,u,h){if(r1(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p}=t;c.transform&&(p.transform=c.transform,delete c.transform),(p.transform||c.transformOrigin)&&(p.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),p.transform&&(p.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==r&&(c.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?r4:r3;t[s.offset]=tC.px.transform(-r);let a=tC.px.transform(e),o=tC.px.transform(i);t[s.array]=`${a} ${o}`}(c,n,s,a,!1)}let r6=()=>({...r2(),attrs:{}}),r9=t=>"string"==typeof t&&"svg"===t.toLowerCase(),r8=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||r8.has(t)}let ne=t=>!nt(t);try{!function(t){"function"==typeof t&&(ne=e=>e.startsWith("on")?!nt(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let ni=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nr(t){if("string"!=typeof t||t.includes("-"));else if(ni.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nn=i(72789);let ns=t=>(e,i)=>{let n=(0,is.useContext)(rN),a=(0,is.useContext)(rX.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,a){return{latestValues:function(t,e,i,n){let a={},o=n(t,{});for(let t in o)a[t]=iw(o[t]);let{initial:l,animate:u}=t,h=rI(t),c=r$(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let p=!!i&&!1===i.initial,d=(p=p||!1===l)?u:l;if(d&&"boolean"!=typeof d&&!r(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let r=s(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=p?e.length-1:0;e=e[t]}null!==e&&(a[t]=e)}for(let e in t)a[e]=t[e]}}}return a}(i,n,a,t),renderState:e()}})(t,e,n,a);return i?o():(0,nn.M)(o)};function na(t,e,i){let{style:r}=t,n={};for(let s in r)(y(r[s])||e.style&&y(e.style[s])||rK(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let no={useVisualState:ns({scrapeMotionValuesFromProps:na,createRenderState:r2})};function nl(t,e,i){let r=na(t,e,i);for(let i in t)(y(t[i])||y(e[i]))&&(r[-1!==c.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let nu={useVisualState:ns({scrapeMotionValuesFromProps:nl,createRenderState:r6})},nh=t=>e=>e.test(t),nc=[tk.ai,tC.px,tC.KN,tC.uj,tC.vw,tC.vh,{test:t=>"auto"===t,parse:t=>t}],np=t=>nc.find(nh(t)),nd=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),nf=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nm=t=>/^0[^.\s]+$/u.test(t);var ng=i(68762);let ny=new Set(["brightness","contrast","saturate","opacity"]);function nv(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(ng.S)||[];if(!r)return t;let n=i.replace(r,""),s=+!!ny.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let nx=/\b([a-z-]*)\(.*?\)/gu,nb={...t0.f,getAnimatableNone:t=>{let e=t.match(nx);return e?e.map(nv).join(" "):t}};var nP=i(7504);let nT={...rZ,color:nP.y,backgroundColor:nP.y,outlineColor:nP.y,fill:nP.y,stroke:nP.y,borderColor:nP.y,borderTopColor:nP.y,borderRightColor:nP.y,borderBottomColor:nP.y,borderLeftColor:nP.y,filter:nb,WebkitFilter:nb},nE=t=>nT[t];function nw(t,e){let i=nE(t);return i!==nb&&(i=t0.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let nA=new Set(["auto","none","0"]);class nS extends tB{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&(r=r.trim(),(0,ip.p)(r))){let n=function t(e,i,r=1){(0,O.V)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=nf.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return nd(t)?parseFloat(t):t}return(0,ip.p)(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!d.has(i)||2!==t.length)return;let[r,n]=t,s=np(r),a=np(n);if(s!==a)if(tj(s)&&tj(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tO[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||nm(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!nA.has(e)&&(0,t0.V)(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=nw(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tO[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=tO[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let nR=[...nc,nP.y,t0.f],nM=t=>nR.find(nh(t)),nk={current:null},nC={current:!1},nj=new WeakMap,nV=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nD{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tB,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,h.Gt.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=rI(e),this.isVariantNode=r$(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==o[t]&&y(e)&&e.set(o[t],!1)}}mount(t){this.current=t,nj.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nC.current||function(){if(nC.current=!0,rU.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nk.current=t.matches;t.addListener(e),e()}else nk.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nk.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,h.WG)(this.notifyUpdate),(0,h.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=p.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&h.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rq){let e=rq[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):e_()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nV.length;e++){let i=nV[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(y(n))t.addValue(r,n);else if(y(s))t.addValue(r,(0,f.OQ)(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,(0,f.OQ)(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,f.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nd(i)||nm(i))?i=parseFloat(i):!nM(i)&&t0.f.test(e)&&(i=nw(t,e)),this.setBaseTarget(t,y(i)?i.get():i)),y(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=s(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||y(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new ib.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nO extends nD{constructor(){super(...arguments),this.KeyframeResolver=nS}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;y(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function n_(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class nL extends nO{constructor(){super(...arguments),this.type="html",this.renderInstance=n_}readValueFromInstance(t,e){if(p.has(e))return this.projection?.isProjecting?tA(e):tR(t,e);{let i=window.getComputedStyle(t),r=((0,ip.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ez(t,e)}build(t,e,i){r1(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return na(t,e,i)}}let nF=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nN extends nO{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=e_}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(p.has(e)){let t=nE(e);return t&&t.default||0}return e=nF.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nl(t,e,i)}build(t,e,i){r7(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in n_(t,e,void 0,r),e.attrs)t.setAttribute(nF.has(i)?i:x(i),e.attrs[i])}mount(t){this.isSVGTag=r9(t.tagName),super.mount(t)}}let nI=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((l={animation:{Feature:eg},exit:{Feature:ev},inView:{Feature:r_},tap:{Feature:rk},focus:{Feature:rb},hover:{Feature:rx},pan:{Feature:ie},drag:{Feature:e8,ProjectionNode:rm,MeasureLayout:ig},layout:{ProjectionNode:rm,MeasureLayout:ig}},u=(t,e)=>nr(t)?new nN(e):new nL(e,{allowProjection:t!==is.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:n}){function s(t,s){var a,o,l;let u,h={...(0,is.useContext)(rF.Q),...t,layoutId:function({layoutId:t}){let e=(0,is.useContext)(io.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:c}=h,p=function(t){let{initial:e,animate:i}=function(t,e){if(rI(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eo(e)?e:void 0,animate:eo(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,is.useContext)(rN));return(0,is.useMemo)(()=>({initial:e,animate:i}),[rB(e),rB(i)])}(t),d=r(t,c);if(!c&&rU.B){o=0,l=0,(0,is.useContext)(rL).strict;let t=function(t){let{drag:e,layout:i}=rq;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,p.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,is.useContext)(rN),a=(0,is.useContext)(rL),o=(0,is.useContext)(rX.t),l=(0,is.useContext)(rF.Q).reducedMotion,u=(0,is.useRef)(null);r=r||a.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let h=u.current,c=(0,is.useContext)(il);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&eH(o),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,n,c);let p=(0,is.useRef)(!1);(0,is.useInsertionEffect)(()=>{h&&p.current&&h.update(i,o)});let d=i[b],f=(0,is.useRef)(!!d&&!window.MotionHandoffIsComplete?.(d)&&window.MotionHasOptimisedAnimation?.(d));return(0,rz.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),ir.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,is.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(d)}),f.current=!1))}),h}(n,d,h,e,t.ProjectionNode)}return(0,ii.jsxs)(rN.Provider,{value:p,children:[u&&p.visualElement?(0,ii.jsx)(u,{visualElement:p.visualElement,...h}):null,i(n,t,(a=p.visualElement,(0,is.useCallback)(t=>{t&&d.onMount&&d.onMount(t),a&&(t?a.mount(t):a.unmount()),s&&("function"==typeof s?s(t):eH(s)&&(s.current=t))},[a])),d,c,p.visualElement)]})}t&&function(t){for(let e in t)rq[e]={...rq[e],...t[e]}}(t),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let a=(0,is.forwardRef)(s);return a[rG]=n,a}({...nr(t)?nu:no,preloadedFeatures:l,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let a=(nr(e)?function(t,e,i,r){let n=(0,is.useMemo)(()=>{let i=r6();return r7(i,e,r9(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};r5(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return r5(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,is.useMemo)(()=>{let i=r2();return r1(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),o=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(ne(n)||!0===i&&nt(n)||!e&&!nt(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==is.Fragment?{...o,...a,ref:r}:{},{children:u}=i,h=(0,is.useMemo)(()=>y(u)?u.get():u,[u]);return(0,is.createElement)(e,{...l,children:h})}}(e),createVisualElement:u,Component:t})}))},53293:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let i=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function n(t){return i.test(t)?t.replace(r,"\\$&"):t}},62688:(t,e,i)=>{"use strict";i.d(e,{A:()=>c});var r=i(43210);let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),a=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,r.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:s="",children:a,iconNode:h,...c},p)=>(0,r.createElement)("svg",{ref:p,...u,width:e,height:e,stroke:t,strokeWidth:n?24*Number(i)/Number(e):i,className:o("lucide",s),...!a&&!l(c)&&{"aria-hidden":"true"},...c},[...h.map(([t,e])=>(0,r.createElement)(t,e)),...Array.isArray(a)?a:[a]])),c=(t,e)=>{let i=(0,r.forwardRef)(({className:i,...s},l)=>(0,r.createElement)(h,{ref:l,iconNode:e,className:o(`lucide-${n(a(t))}`,`lucide-${t}`,i),...s}));return i.displayName=a(t),i}},64068:(t,e,i)=>{"use strict";i.d(e,{q:()=>r});let r=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r}},66244:(t,e,i)=>{"use strict";i.d(e,{$:()=>r,V:()=>n});let r=()=>{},n=()=>{}},68028:(t,e,i)=>{"use strict";i.d(e,{k:()=>r});let r=(t,e,i)=>t+(e-t)*i},68762:(t,e,i)=>{"use strict";i.d(e,{S:()=>r});let r=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},69848:(t,e,i)=>{"use strict";i.d(e,{I:()=>a});var r=i(97819);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(82082);function a(t,e){let i=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=n.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){o.has(e)&&(c.schedule(e),t()),u++,e(l)}let c={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(l=t,n){a=!0;return}n=!0,[i,r]=[r,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),n=!1,a&&(a=!1,c.process(t))}};return c}(l,e?i:void 0),t),{}),{setup:h,read:c,resolveKeyframes:p,preUpdate:d,update:f,preRender:m,render:g,postRender:y}=u,v=()=>{let n=r.W.useManualTiming?o.timestamp:performance.now();i=!1,r.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,h.process(o),c.process(o),p.process(o),d.process(o),f.process(o),m.process(o),g.process(o),y.process(o),o.isProcessing=!1,i&&e&&(a=!1,t(v))},x=()=>{i=!0,a=!0,o.isProcessing||t(v)};return{schedule:n.reduce((t,e)=>{let r=u[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:o,steps:u}}},70554:(t,e)=>{"use strict";function i(t){return t.endsWith("/route")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},71437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let r=i(74722),n=["(..)(..)","(.)","(..)","(...)"];function s(t){return void 0!==t.split("/").find(t=>n.find(e=>t.startsWith(e)))}function a(t){let e,i,s;for(let r of t.split("/"))if(i=n.find(t=>r.startsWith(t))){[e,s]=t.split(i,2);break}if(!e||!i||!s)throw Object.defineProperty(Error("Invalid interception route: "+t+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(e=(0,r.normalizeAppPath)(e),i){case"(.)":s="/"===e?"/"+s:e+"/"+s;break;case"(..)":if("/"===e)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=e.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=e.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:e,interceptedRoute:s}}},72789:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var r=i(43210);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},73063:(t,e,i)=>{"use strict";i.d(e,{u:()=>n});var r=i(21874);let n={test:(0,i(7236).$)("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:r.B.transform}},74479:(t,e,i)=>{"use strict";function r(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>r})},74722:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let r=i(85531),n=i(35499);function s(t){return(0,r.ensureLeadingSlash)(t.split("/").reduce((t,e,i,r)=>!e||(0,n.isGroupSegment)(e)||"@"===e[0]||("page"===e||"route"===e)&&i===r.length-1?t:t+"/"+e,""))}function a(t){return t.replace(/\.rsc($|\?)/,"$1")}},76759:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseUrl",{enumerable:!0,get:function(){return s}});let r=i(42785),n=i(23736);function s(t){if(t.startsWith("/"))return(0,n.parseRelativeUrl)(t);let e=new URL(t);return{hash:e.hash,hostname:e.hostname,href:e.href,pathname:e.pathname,port:e.port,protocol:e.protocol,query:(0,r.searchParamsToUrlQuery)(e.searchParams),search:e.search}}},78034:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let r=i(44827);function n(t){let{re:e,groups:i}=t;return t=>{let n=e.exec(t);if(!n)return!1;let s=t=>{try{return decodeURIComponent(t)}catch(t){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[t,e]of Object.entries(i)){let i=n[e.pos];void 0!==i&&(e.repeat?a[t]=i.split("/").map(t=>s(t)):a[t]=s(i))}return a}}},78205:(t,e,i)=>{"use strict";i.d(e,{F:()=>n});let r=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(r)},82082:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r={value:null,addProjectionMetrics:null}},83361:(t,e,i)=>{"use strict";i.d(e,{l:()=>r});let r=t=>t},85531:(t,e)=>{"use strict";function i(t){return t.startsWith("/")?t:"/"+t}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},86044:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var r=i(43210),n=i(21279);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return o(l)},[t]);let u=(0,r.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,u]:[!0]}},87556:(t,e,i)=>{"use strict";function r(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>r})},88212:(t,e,i)=>{"use strict";function r(t){return function(){let{cookie:e}=t;if(!e)return{};let{parse:r}=i(26415);return r(Array.isArray(e)?e.join("; "):e)}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getCookieParser",{enumerable:!0,get:function(){return r}})},91955:(t,e,i)=>{"use strict";i.d(e,{j:()=>A});var r=i(78205),n=i(66244),s=i(22238),a=i(7504),o=i(39664),l=i(73063),u=i(12742);function h(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var c=i(21874);function p(t,e){return i=>i>0?e:t}var d=i(68028);let f=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},m=[l.u,c.B,u.V],g=t=>m.find(e=>e.test(t));function y(t){let e=g(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===u.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,a=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,o=2*i-r;n=h(o,r,t+1/3),s=h(o,r,t),a=h(o,r,t-1/3)}else n=s=a=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(i)),i}let v=(t,e)=>{let i=y(t),r=y(e);if(!i||!r)return p(t,e);let n={...i};return t=>(n.red=f(i.red,r.red,t),n.green=f(i.green,r.green,t),n.blue=f(i.blue,r.blue,t),n.alpha=(0,d.k)(i.alpha,r.alpha,t),c.B.transform(n))},x=new Set(["none","hidden"]);function b(t,e){return i=>(0,d.k)(t,e,i)}function P(t){return"number"==typeof t?b:"string"==typeof t?(0,s.p)(t)?p:a.y.test(t)?v:w:Array.isArray(t)?T:"object"==typeof t?a.y.test(t)?v:E:p}function T(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>P(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function E(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=P(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let w=(t,e)=>{let i=o.f.createTransformer(e),s=(0,o.V)(t),a=(0,o.V)(e);return s.indexes.var.length===a.indexes.var.length&&s.indexes.color.length===a.indexes.color.length&&s.indexes.number.length>=a.indexes.number.length?x.has(t)&&!a.values.length||x.has(e)&&!s.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,r.F)(T(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],a=t.indexes[s][r[s]],o=t.values[a]??0;i[n]=o,r[s]++}return i}(s,a),a.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),p(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,d.k)(t,e,i):P(t)(t,e)}},95444:(t,e,i)=>{"use strict";i.d(e,{X4:()=>s,ai:()=>n,hs:()=>a});var r=i(97758);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...n,transform:t=>(0,r.q)(0,1,t)},a={...n,default:1}},97095:(t,e,i)=>{"use strict";i.d(e,{a:()=>r});let r=t=>Math.round(1e5*t)/1e5},97758:(t,e,i)=>{"use strict";i.d(e,{q:()=>r});let r=(t,e,i)=>i>e?e:i<t?t:i},97819:(t,e,i)=>{"use strict";i.d(e,{W:()=>r});let r={}}};