(()=>{var e={};e.id=631,e.ids=[631],e.modules={1325:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>K});var t=a(60687),i=a(43210),r=a(50371),n=a(58869),l=a(97051),c=a(99891),o=a(62688);let d=(0,o.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),m=(0,o.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),p=(0,o.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var h=a(78122),u=a(8819),x=a(16023),f=a(88233),y=a(19169);let j=(0,o.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var v=a(10022),N=a(40228),g=a(19959),b=a(12597),w=a(13861),k=a(29523),A=a(89667),C=a(44493),P=a(96834),M=a(74456),S=a(32584),E=a(70569),R=a(98599),z=a(11273),_=a(65551),q=a(83721),T=a(18853),D=a(14163),B="Switch",[L,H]=(0,z.A)(B),[Z,G]=L(B),$=i.forwardRef((e,s)=>{let{__scopeSwitch:a,name:r,checked:n,defaultChecked:l,required:c,disabled:o,value:d="on",onCheckedChange:m,form:p,...h}=e,[u,x]=i.useState(null),f=(0,R.s)(s,e=>x(e)),y=i.useRef(!1),j=!u||p||!!u.closest("form"),[v,N]=(0,_.i)({prop:n,defaultProp:l??!1,onChange:m,caller:B});return(0,t.jsxs)(Z,{scope:a,checked:v,disabled:o,children:[(0,t.jsx)(D.sG.button,{type:"button",role:"switch","aria-checked":v,"aria-required":c,"data-state":U(v),"data-disabled":o?"":void 0,disabled:o,value:d,...h,ref:f,onClick:(0,E.m)(e.onClick,e=>{N(e=>!e),j&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),j&&(0,t.jsx)(F,{control:u,bubbles:!y.current,name:r,value:d,checked:v,required:c,disabled:o,form:p,style:{transform:"translateX(-100%)"}})]})});$.displayName=B;var I="SwitchThumb",W=i.forwardRef((e,s)=>{let{__scopeSwitch:a,...i}=e,r=G(I,a);return(0,t.jsx)(D.sG.span,{"data-state":U(r.checked),"data-disabled":r.disabled?"":void 0,...i,ref:s})});W.displayName=I;var F=i.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:r=!0,...n},l)=>{let c=i.useRef(null),o=(0,R.s)(c,l),d=(0,q.Z)(a),m=(0,T.X)(s);return i.useEffect(()=>{let e=c.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&s){let t=new Event("click",{bubbles:r});s.call(e,a),e.dispatchEvent(t)}},[d,a,r]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:o,style:{...n.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function U(e){return e?"checked":"unchecked"}F.displayName="SwitchBubbleInput";var V=a(4780);let Y=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)($,{className:(0,V.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:(0,t.jsx)(W,{className:(0,V.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));Y.displayName=$.displayName;let X={profile:{firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",department:"Engineering",position:"Senior Software Engineer",avatar:"/avatars/john.jpg",timezone:"America/New_York",language:"English"},notifications:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,weeklyReports:!0,systemAlerts:!0,leaveApprovals:!0,payrollUpdates:!1,announcementUpdates:!0},security:{twoFactorEnabled:!0,sessionTimeout:30,passwordLastChanged:"2024-01-15",loginHistory:!0,deviceManagement:!0},appearance:{theme:"system",compactMode:!1,showAvatars:!0,animationsEnabled:!0,sidebarCollapsed:!1}},O={general:{companyName:"PeopleNest Inc.",companyEmail:"<EMAIL>",companyPhone:"+****************",address:"123 Business Ave, Suite 100, City, State 12345",website:"https://www.peoplenest.com",timezone:"America/New_York",fiscalYearStart:"January"},hrPolicies:{workingHours:"9:00 AM - 5:00 PM",workingDays:"Monday - Friday",leavePolicyEnabled:!0,overtimeEnabled:!0,remoteWorkEnabled:!0,flexibleHoursEnabled:!0},payroll:{payFrequency:"Bi-weekly",payrollCurrency:"USD",taxCalculationEnabled:!0,benefitsEnabled:!0,bonusEnabled:!0}};function K(){let[e,s]=(0,i.useState)("profile"),[a,o]=(0,i.useState)(!1),[E,R]=(0,i.useState)(X),[z,_]=(0,i.useState)(O),q=[{id:"profile",label:"Profile",icon:n.A},{id:"notifications",label:"Notifications",icon:l.A},{id:"security",label:"Security",icon:c.A},{id:"appearance",label:"Appearance",icon:d},{id:"company",label:"Company",icon:m},{id:"system",label:"System",icon:p}],T=(e,s)=>{R(a=>({...a,notifications:{...a.notifications,[e]:s}}))},D=(e,s)=>{R(a=>({...a,appearance:{...a.appearance,[e]:s}}))};return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(M.Y,{title:"Settings",subtitle:"Manage your account, preferences, and system configuration",actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Reset to Default"]}),(0,t.jsxs)(k.$,{size:"sm",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,t.jsx)("div",{className:"lg:w-64",children:(0,t.jsx)(C.Zp,{children:(0,t.jsx)(C.Wu,{className:"p-4",children:(0,t.jsx)("nav",{className:"space-y-2",children:q.map(a=>{let i=a.icon;return(0,t.jsxs)("button",{onClick:()=>s(a.id),className:`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${e===a.id?"bg-primary text-primary-foreground":"hover:bg-muted text-muted-foreground"}`,children:[(0,t.jsx)(i,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:a.label})]},a.id)})})})})}),(0,t.jsxs)("div",{className:"flex-1",children:["profile"===e&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Profile Information"})]}),(0,t.jsx)(C.BT,{children:"Update your personal information and contact details"})]}),(0,t.jsxs)(C.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)(S.eu,{className:"h-20 w-20",children:[(0,t.jsx)(S.BK,{src:E.profile.avatar,alt:"Profile"}),(0,t.jsxs)(S.q5,{className:"text-lg",children:[E.profile.firstName[0],E.profile.lastName[0]]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Change Photo"]}),(0,t.jsxs)(k.$,{variant:"ghost",size:"sm",className:"text-destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Remove Photo"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"First Name"}),(0,t.jsx)(A.p,{value:E.profile.firstName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Last Name"}),(0,t.jsx)(A.p,{value:E.profile.lastName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,t.jsx)(A.p,{value:E.profile.email,type:"email"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,t.jsx)(A.p,{value:E.profile.phone})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Department"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"}),(0,t.jsx)("option",{value:"Sales",children:"Sales"}),(0,t.jsx)("option",{value:"Marketing",children:"Marketing"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Position"}),(0,t.jsx)(A.p,{value:E.profile.position})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Timezone"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,t.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,t.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,t.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Language"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"English",children:"English"}),(0,t.jsx)("option",{value:"Spanish",children:"Spanish"}),(0,t.jsx)("option",{value:"French",children:"French"}),(0,t.jsx)("option",{value:"German",children:"German"})]})]})]})]})]})}),"notifications"===e&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Notification Preferences"})]}),(0,t.jsx)(C.BT,{children:"Choose how you want to be notified about important updates"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Email Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),(0,t.jsx)(Y,{checked:E.notifications.emailNotifications,onCheckedChange:e=>T("emailNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Push Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive browser push notifications"})]}),(0,t.jsx)(Y,{checked:E.notifications.pushNotifications,onCheckedChange:e=>T("pushNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"SMS Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via text message"})]}),(0,t.jsx)(Y,{checked:E.notifications.smsNotifications,onCheckedChange:e=>T("smsNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Weekly Reports"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive weekly summary reports"})]}),(0,t.jsx)(Y,{checked:E.notifications.weeklyReports,onCheckedChange:e=>T("weeklyReports",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"System Alerts"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Important system and security alerts"})]}),(0,t.jsx)(Y,{checked:E.notifications.systemAlerts,onCheckedChange:e=>T("systemAlerts",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Leave Approvals"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Notifications for leave requests and approvals"})]}),(0,t.jsx)(Y,{checked:E.notifications.leaveApprovals,onCheckedChange:e=>T("leaveApprovals",e)})]})]})})]})}),"security"===e&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Security Settings"})]}),(0,t.jsx)(C.BT,{children:"Manage your account security and privacy settings"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Two-Factor Authentication"}),(0,t.jsx)(P.E,{variant:"success",className:"text-xs",children:"Enabled"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,t.jsx)(Y,{checked:E.security.twoFactorEnabled})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Change Password"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.p,{type:a?"text":"password",placeholder:"Current password"}),(0,t.jsx)(k.$,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6",onClick:()=>o(!a),children:a?(0,t.jsx)(b.A,{className:"h-4 w-4"}):(0,t.jsx)(w.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(A.p,{type:"password",placeholder:"New password"}),(0,t.jsx)(A.p,{type:"password",placeholder:"Confirm new password"}),(0,t.jsx)(k.$,{size:"sm",children:"Update Password"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Session Timeout"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"15",children:"15 minutes"}),(0,t.jsx)("option",{value:"30",children:"30 minutes"}),(0,t.jsx)("option",{value:"60",children:"1 hour"}),(0,t.jsx)("option",{value:"240",children:"4 hours"}),(0,t.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Password Last Changed"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(E.security.passwordLastChanged).toLocaleDateString()})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Login History"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Keep track of account access"})]}),(0,t.jsx)(Y,{checked:E.security.loginHistory})]})]})})]})}),"appearance"===e&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(d,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Appearance Settings"})]}),(0,t.jsx)(C.BT,{children:"Customize the look and feel of your interface"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Theme"}),(0,t.jsxs)("select",{value:E.appearance.theme,onChange:e=>D("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"light",children:"Light"}),(0,t.jsx)("option",{value:"dark",children:"Dark"}),(0,t.jsx)("option",{value:"system",children:"System"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Compact Mode"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use smaller spacing and elements"})]}),(0,t.jsx)(Y,{checked:E.appearance.compactMode,onCheckedChange:e=>D("compactMode",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Show Avatars"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display user profile pictures"})]}),(0,t.jsx)(Y,{checked:E.appearance.showAvatars,onCheckedChange:e=>D("showAvatars",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Animations"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable smooth transitions and animations"})]}),(0,t.jsx)(Y,{checked:E.appearance.animationsEnabled,onCheckedChange:e=>D("animationsEnabled",e)})]})]})})]})})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12492:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o});var t=a(65239),i=a(48088),r=a(88170),n=a.n(r),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let o={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,62623)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},12597:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},18853:(e,s,a)=>{"use strict";a.d(s,{X:()=>r});var t=a(43210),i=a(66156);function r(e){let[s,a]=t.useState(void 0);return(0,i.N)(()=>{if(e){a({width:e.offsetWidth,height:e.offsetHeight});let s=new ResizeObserver(s=>{let t,i;if(!Array.isArray(s)||!s.length)return;let r=s[0];if("borderBoxSize"in r){let e=r.borderBoxSize,s=Array.isArray(e)?e[0]:e;t=s.inlineSize,i=s.blockSize}else t=e.offsetWidth,i=e.offsetHeight;a({width:t,height:i})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}a(void 0)},[e]),s}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},19581:(e,s,a)=>{Promise.resolve().then(a.bind(a,1325))},19959:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},24317:(e,s,a)=>{Promise.resolve().then(a.bind(a,62623))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},62623:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65551:(e,s,a)=>{"use strict";a.d(s,{i:()=>l});var t,i=a(43210),r=a(66156),n=(t||(t=a.t(i,2)))[" useInsertionEffect ".trim().toString()]||r.N;function l({prop:e,defaultProp:s,onChange:a=()=>{},caller:t}){let[r,l,c]=function({defaultProp:e,onChange:s}){let[a,t]=i.useState(e),r=i.useRef(a),l=i.useRef(s);return n(()=>{l.current=s},[s]),i.useEffect(()=>{r.current!==a&&(l.current?.(a),r.current=a)},[a,r]),[a,t,l]}({defaultProp:s,onChange:a}),o=void 0!==e,d=o?e:r;{let s=i.useRef(void 0!==e);i.useEffect(()=>{let e=s.current;if(e!==o){let s=o?"controlled":"uncontrolled";console.warn(`${t} is changing from ${e?"controlled":"uncontrolled"} to ${s}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}s.current=o},[o,t])}return[d,i.useCallback(s=>{if(o){let a="function"==typeof s?s(e):s;a!==e&&c.current?.(a)}else l(s)},[o,e,l,c])]}Symbol("RADIX:SYNC_STATE")},70569:(e,s,a)=>{"use strict";function t(e,s,{checkForDefaultPrevented:a=!0}={}){return function(t){if(e?.(t),!1===a||!t.defaultPrevented)return s?.(t)}}a.d(s,{m:()=>t})},79551:e=>{"use strict";e.exports=require("url")},83721:(e,s,a)=>{"use strict";a.d(s,{Z:()=>i});var t=a(43210);function i(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,912,486,722,658,557,188],()=>a(12492));module.exports=t})();