(()=>{var e={};e.id=437,e.ids=[437],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9415:(e,t,a)=>{Promise.resolve().then(a.bind(a,76557))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15202:(e,t,a)=>{"use strict";a.d(t,{s:()=>z});var s=a(43210),r=a(51215),n=a(14221),l=a(49384),i=a(10687),c=a.n(i),o=a(21080),d=a(10919),p=a(4057);function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}function x(e,t,a){var s;return(t="symbol"==typeof(s=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?s:s+"")in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}class h extends s.PureComponent{renderIcon(e,t){var{inactiveColor:a}=this.props,r=32/6,n=32/3,l=e.inactive?a:e.color,i=null!=t?t:e.type;if("none"===i)return null;if("plainline"===i)return s.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===i)return s.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===i)return s.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(s.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach(function(t){x(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}({},e);return delete c.legendIcon,s.cloneElement(e.legendIcon,c)}return s.createElement(d.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:i})}renderItems(){var{payload:e,iconSize:t,layout:a,formatter:r,inactiveColor:n,iconType:i,itemSorter:d}=this.props,u={x:0,y:0,width:32,height:32},x={display:"horizontal"===a?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return(d?c()(e,d):e).map((e,a)=>{var c=e.formatter||r,d=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(a)]:!0,inactive:e.inactive});if("none"===e.type)return null;var f=e.inactive?n:e.color,j=c?c(e.value,e,a):e.value;return s.createElement("li",m({className:d,style:x,key:"legend-item-".concat(a)},(0,p.XC)(this.props,e,a)),s.createElement(o.u,{width:t,height:t,viewBox:u,style:h,"aria-label":"".concat(j," legend icon")},this.renderIcon(e,i)),s.createElement("span",{className:"recharts-legend-item-text",style:{color:f}},j))})}render(){var{payload:e,layout:t,align:a}=this.props;return e&&e.length?s.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?a:"left"}},this.renderItems()):null}}x(h,"displayName","Legend"),x(h,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var f=a(22989),j=a(45796),y=a(43209),g=a(23337),v=a(68392),b=a(51426);a(53044);var N=["contextPayload"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)({}).hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e}).apply(null,arguments)}function k(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,s)}return a}function A(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?k(Object(a),!0).forEach(function(t){P(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):k(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function P(e,t,a){var s;return(t="symbol"==typeof(s=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var s=a.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?s:s+"")in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function O(e){return e.value}function E(e){var{contextPayload:t}=e,a=function(e,t){if(null==e)return{};var a,s,r=function(e,t){if(null==e)return{};var a={};for(var s in e)if(({}).hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)a=n[s],-1===t.indexOf(a)&&({}).propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}(e,N),r=(0,j.s)(t,e.payloadUniqBy,O),n=A(A({},a),{},{payload:r});return s.isValidElement(e.content)?s.cloneElement(e.content,n):"function"==typeof e.content?s.createElement(e.content,n):s.createElement(h,n)}function C(e){return(0,y.j)(),null}function D(e){return(0,y.j)(),null}function T(e){var t=(0,y.G)(g.g0),a=(0,n.M)(),l=(0,b.Kp)(),{width:i,height:c,wrapperStyle:o,portal:d}=e,[p,m]=(0,v.V)([t]),u=(0,b.yi)(),x=(0,b.rY)(),h=u-(l.left||0)-(l.right||0),f=z.getWidthOrHeight(e.layout,c,i,h),j=d?o:A(A({position:"absolute",width:(null==f?void 0:f.width)||i||"auto",height:(null==f?void 0:f.height)||c||"auto"},function(e,t,a,s,r,n){var l,i,{layout:c,align:o,verticalAlign:d}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===o&&"vertical"===c?{left:((s||0)-n.width)/2}:"right"===o?{right:a&&a.right||0}:{left:a&&a.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(i="middle"===d?{top:((r||0)-n.height)/2}:"bottom"===d?{bottom:a&&a.bottom||0}:{top:a&&a.top||0}),A(A({},l),i)}(o,e,l,u,x,p)),o),N=null!=d?d:a;if(null==N)return null;var k=s.createElement("div",{className:"recharts-legend-wrapper",style:j,ref:m},s.createElement(C,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),s.createElement(D,{width:p.width,height:p.height}),s.createElement(E,w({},e,f,{margin:l,chartWidth:u,chartHeight:x,contextPayload:t})));return(0,r.createPortal)(k,N)}class z extends s.PureComponent{static getWidthOrHeight(e,t,a,s){return"vertical"===e&&(0,f.Et)(t)?{height:t}:"horizontal"===e?{width:a||s}:null}render(){return s.createElement(T,this.props)}}P(z,"displayName","Legend"),P(z,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26287:(e,t,a)=>{Promise.resolve().then(a.bind(a,47817))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},43649:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47817:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\attendance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63840:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>o});var s=a(65239),r=a(48088),n=a(88170),l=a.n(n),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let o={children:["",{children:["dashboard",{children:["attendance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,47817)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\attendance\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/attendance/page",pathname:"/dashboard/attendance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},76557:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>K});var s=a(60687),r=a(43210),n=a(50371),l=a(5336),i=a(43649),c=a(35071),o=a(48730),d=a(97992),p=a(62688);let m=(0,p.A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);var u=a(31158),x=a(96474),h=a(41312),f=a(25541),j=a(99270);let y=(0,p.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);var g=a(40083);let v=(0,p.A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var b=a(13861),N=a(63143),w=a(40228),k=a(29523),A=a(89667),P=a(44493),O=a(96834),E=a(74456),C=a(32584),D=a(48482),T=a(61678),z=a(85168),M=a(27747),W=a(19598),I=a(18969),S=a(15202),R=a(66424),q=a(2041),Z=a(515);let H=[{id:1,employeeId:1,employeeName:"Sarah Johnson",department:"Engineering",date:"2024-01-25",clockIn:"09:00",clockOut:"17:30",breakTime:"60",totalHours:"7.5",status:"present",location:"Office",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"Mike Chen",department:"Product",date:"2024-01-25",clockIn:"08:45",clockOut:"17:15",breakTime:"45",totalHours:"7.75",status:"present",location:"Remote",avatar:"/avatars/mike.jpg"},{id:3,employeeId:3,employeeName:"Emily Davis",department:"Design",date:"2024-01-25",clockIn:"09:15",clockOut:"18:00",breakTime:"75",totalHours:"7.5",status:"late",location:"Office",avatar:"/avatars/emily.jpg"},{id:4,employeeId:4,employeeName:"David Wilson",department:"Engineering",date:"2024-01-25",clockIn:null,clockOut:null,breakTime:null,totalHours:"0",status:"absent",location:null,avatar:"/avatars/david.jpg"}],_=[{date:"Jan 20",present:95,late:3,absent:2},{date:"Jan 21",present:97,late:2,absent:1},{date:"Jan 22",present:94,late:4,absent:2},{date:"Jan 23",present:96,late:2,absent:2},{date:"Jan 24",present:98,late:1,absent:1},{date:"Jan 25",present:93,late:5,absent:2}],L=[{department:"Engineering",present:45,late:2,absent:1},{department:"Product",present:28,late:1,absent:1},{department:"Design",present:22,late:2,absent:0},{department:"Sales",present:35,late:3,absent:2},{department:"Marketing",present:18,late:1,absent:1}],$={totalEmployees:150,presentToday:140,lateToday:8,absentToday:2,avgHoursPerDay:7.6,attendanceRate:93.3};function K(){let[e,t]=(0,r.useState)(""),[a,p]=(0,r.useState)("All"),[K,B]=(0,r.useState)("All"),[G,J]=(0,r.useState)("2024-01-25"),[V,U]=(0,r.useState)("daily"),X=H.filter(t=>{let s=t.employeeName.toLowerCase().includes(e.toLowerCase())||t.department.toLowerCase().includes(e.toLowerCase()),r="All"===a||t.status===a,n="All"===K||t.department===K;return s&&r&&n}),Y=e=>{switch(e){case"present":return"success";case"late":return"warning";case"absent":return"destructive";default:return"secondary"}},F=e=>{switch(e){case"present":return(0,s.jsx)(l.A,{className:"h-4 w-4"});case"late":return(0,s.jsx)(i.A,{className:"h-4 w-4"});case"absent":return(0,s.jsx)(c.A,{className:"h-4 w-4"});default:return(0,s.jsx)(o.A,{className:"h-4 w-4"})}},Q=e=>e?"Office"===e?(0,s.jsx)(d.A,{className:"h-3 w-3"}):(0,s.jsx)(m,{className:"h-3 w-3"}):null;return(0,s.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,s.jsx)(E.Y,{title:"Time & Attendance",subtitle:`Tracking attendance for ${$.totalEmployees} employees`,actions:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"date",value:G,onChange:e=>J(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"}),(0,s.jsxs)(k.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,s.jsxs)(k.$,{size:"sm",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Manual Entry"]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:$.totalEmployees}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Employees"})]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:$.presentToday}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Present Today"})]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:$.lateToday}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Late Today"})]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:$.absentToday}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Absent Today"})]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-foreground",children:$.avgHoursPerDay}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Hours/Day"})]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[$.attendanceRate,"%"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendance Rate"})]})]})})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(k.$,{variant:"daily"===V?"default":"outline",size:"sm",onClick:()=>U("daily"),children:"Daily View"}),(0,s.jsx)(k.$,{variant:"trends"===V?"default":"outline",size:"sm",onClick:()=>U("trends"),children:"Trends"}),(0,s.jsx)(k.$,{variant:"reports"===V?"default":"outline",size:"sm",onClick:()=>U("reports"),children:"Reports"})]}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(A.p,{placeholder:"Search by employee name or department...",value:e,onChange:e=>t(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("select",{value:a,onChange:e=>p(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,s.jsx)("option",{value:"All",children:"All Status"}),(0,s.jsx)("option",{value:"present",children:"Present"}),(0,s.jsx)("option",{value:"late",children:"Late"}),(0,s.jsx)("option",{value:"absent",children:"Absent"})]}),(0,s.jsxs)("select",{value:K,onChange:e=>B(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,s.jsx)("option",{value:"All",children:"All Departments"}),(0,s.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,s.jsx)("option",{value:"Product",children:"Product"}),(0,s.jsx)("option",{value:"Design",children:"Design"}),(0,s.jsx)("option",{value:"Sales",children:"Sales"}),(0,s.jsx)("option",{value:"Marketing",children:"Marketing"})]})]})]})})}),"daily"===V?(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-0",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Department"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Clock In"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Clock Out"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Break Time"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Total Hours"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Location"}),(0,s.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:X.map((e,t)=>(0,s.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*t},className:"border-b border-border hover:bg-muted/50",children:[(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(C.eu,{className:"h-10 w-10",children:[(0,s.jsx)(C.BK,{src:e.avatar,alt:e.employeeName}),(0,s.jsx)(C.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",e.employeeId]})]})]})}),(0,s.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[e.clockIn&&(0,s.jsx)(y,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:e.clockIn||"N/A"})]})}),(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[e.clockOut&&(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"font-medium",children:e.clockOut||"N/A"})]})}),(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[e.breakTime&&(0,s.jsx)(v,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("span",{children:e.breakTime?`${e.breakTime} min`:"N/A"})]})}),(0,s.jsxs)("td",{className:"py-4 px-6 font-medium",children:[e.totalHours,"h"]}),(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)(O.E,{variant:Y(e.status),className:"flex items-center space-x-1 w-fit",children:[F(e.status),(0,s.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,s.jsx)("td",{className:"py-4 px-6",children:e.location&&(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[Q(e.location),(0,s.jsx)("span",{className:"text-sm",children:e.location})]})}),(0,s.jsx)("td",{className:"py-4 px-6",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(k.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)(k.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(N.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"trends"===V?(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(P.Zp,{children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Daily Attendance Trends"})]}),(0,s.jsx)(P.BT,{children:"Attendance patterns over the last week"})]}),(0,s.jsx)(P.Wu,{children:(0,s.jsx)(D.u,{width:"100%",height:300,children:(0,s.jsxs)(T.b,{data:_,children:[(0,s.jsx)(z.d,{strokeDasharray:"3 3"}),(0,s.jsx)(M.W,{dataKey:"date"}),(0,s.jsx)(W.h,{}),(0,s.jsx)(I.m,{}),(0,s.jsx)(S.s,{}),(0,s.jsx)(R.N,{type:"monotone",dataKey:"present",stroke:"#10b981",strokeWidth:2,name:"Present"}),(0,s.jsx)(R.N,{type:"monotone",dataKey:"late",stroke:"#f59e0b",strokeWidth:2,name:"Late"}),(0,s.jsx)(R.N,{type:"monotone",dataKey:"absent",stroke:"#ef4444",strokeWidth:2,name:"Absent"})]})})})]}),(0,s.jsxs)(P.Zp,{children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Department Attendance"})]}),(0,s.jsx)(P.BT,{children:"Attendance breakdown by department"})]}),(0,s.jsx)(P.Wu,{children:(0,s.jsx)(D.u,{width:"100%",height:300,children:(0,s.jsxs)(q.E,{data:L,children:[(0,s.jsx)(z.d,{strokeDasharray:"3 3"}),(0,s.jsx)(M.W,{dataKey:"department"}),(0,s.jsx)(W.h,{}),(0,s.jsx)(I.m,{}),(0,s.jsx)(S.s,{}),(0,s.jsx)(Z.y,{dataKey:"present",stackId:"a",fill:"#10b981",name:"Present"}),(0,s.jsx)(Z.y,{dataKey:"late",stackId:"a",fill:"#f59e0b",name:"Late"}),(0,s.jsx)(Z.y,{dataKey:"absent",stackId:"a",fill:"#ef4444",name:"Absent"})]})})})]})]}):(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)(P.Zp,{children:[(0,s.jsxs)(P.aR,{children:[(0,s.jsx)(P.ZB,{children:"Attendance Reports"}),(0,s.jsx)(P.BT,{children:"Generate and download attendance reports"})]}),(0,s.jsx)(P.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)(w.A,{className:"h-8 w-8 mx-auto text-blue-600"}),(0,s.jsx)("h3",{className:"font-medium",children:"Daily Report"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Today's attendance summary"}),(0,s.jsxs)(k.$,{size:"sm",className:"w-full",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 mx-auto text-green-600"}),(0,s.jsx)("h3",{className:"font-medium",children:"Weekly Report"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last 7 days attendance"}),(0,s.jsxs)(k.$,{size:"sm",className:"w-full",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}),(0,s.jsx)(P.Zp,{children:(0,s.jsx)(P.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)(f.A,{className:"h-8 w-8 mx-auto text-purple-600"}),(0,s.jsx)("h3",{className:"font-medium",children:"Monthly Report"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Full month analysis"}),(0,s.jsxs)(k.$,{size:"sm",className:"w-full",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})})]})})]})})]})}},79551:e=>{"use strict";e.exports=require("url")},97992:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,912,486,722,658,632,203,835,557,188],()=>a(63840));module.exports=s})();