"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  HelpCircle,
  Search,
  Book,
  MessageCircle,
  Phone,
  Mail,
  FileText,
  Video,
  Download,
  ExternalLink,
  ChevronRight,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Lightbulb,
  Headphones,
  Globe,
  Zap,
  Shield,
  Settings,
  User,
  Calendar,
  BarChart3
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock help data
const faqItems = [
  {
    id: 1,
    question: "How do I reset my password?",
    answer: "You can reset your password by clicking the 'Forgot Password' link on the login page, or by going to Settings > Security > Change Password.",
    category: "Account",
    helpful: 45,
    views: 234
  },
  {
    id: 2,
    question: "How do I submit a leave request?",
    answer: "Navigate to Dashboard > Leave Management, click 'Request Leave', fill in the details including dates and reason, then submit for approval.",
    category: "Leave Management",
    helpful: 38,
    views: 189
  },
  {
    id: 3,
    question: "Where can I view my payroll information?",
    answer: "Go to Dashboard > Payroll to view your current and historical payroll information, including pay stubs and tax documents.",
    category: "Payroll",
    helpful: 52,
    views: 312
  },
  {
    id: 4,
    question: "How do I update my personal information?",
    answer: "Visit Settings > Profile to update your personal information, contact details, and emergency contacts.",
    category: "Profile",
    helpful: 29,
    views: 156
  },
  {
    id: 5,
    question: "How do I clock in and out?",
    answer: "Use the Dashboard > Time & Attendance page to clock in/out, or use the quick clock buttons on the main dashboard.",
    category: "Time Tracking",
    helpful: 41,
    views: 278
  }
]

const supportTickets = [
  {
    id: 1,
    title: "Unable to access payroll documents",
    status: "open",
    priority: "high",
    category: "Technical",
    createdDate: "2024-01-22",
    lastUpdate: "2024-01-23",
    assignedTo: "Support Team",
    description: "Getting error when trying to download pay stubs"
  },
  {
    id: 2,
    title: "Leave balance calculation incorrect",
    status: "in-progress",
    priority: "medium",
    category: "Leave",
    createdDate: "2024-01-20",
    lastUpdate: "2024-01-22",
    assignedTo: "HR Team",
    description: "My leave balance doesn't match my records"
  },
  {
    id: 3,
    title: "Performance review form not saving",
    status: "resolved",
    priority: "low",
    category: "Performance",
    createdDate: "2024-01-18",
    lastUpdate: "2024-01-21",
    assignedTo: "IT Support",
    description: "Form data is lost when I try to save"
  }
]

const helpCategories = [
  { name: "Getting Started", icon: Lightbulb, count: 12, description: "Basic setup and first steps" },
  { name: "Account Management", icon: User, count: 8, description: "Profile, settings, and security" },
  { name: "Time & Attendance", icon: Clock, count: 15, description: "Clock in/out, timesheets, schedules" },
  { name: "Leave Management", icon: Calendar, count: 10, description: "Requesting and managing leave" },
  { name: "Payroll", icon: BarChart3, count: 7, description: "Pay stubs, tax forms, benefits" },
  { name: "Performance", icon: Star, count: 9, description: "Reviews, goals, feedback" },
  { name: "Technical Support", icon: Settings, count: 18, description: "Troubleshooting and technical issues" },
  { name: "Compliance", icon: Shield, count: 6, description: "Policies, procedures, regulations" }
]

const quickLinks = [
  { title: "User Guide", icon: Book, description: "Complete user documentation", link: "#" },
  { title: "Video Tutorials", icon: Video, description: "Step-by-step video guides", link: "#" },
  { title: "System Status", icon: Globe, description: "Check current system status", link: "#" },
  { title: "Feature Requests", icon: Lightbulb, description: "Suggest new features", link: "#" },
  { title: "Contact Support", icon: Headphones, description: "Get help from our team", link: "#" },
  { title: "Community Forum", icon: Users, description: "Connect with other users", link: "#" }
]

const helpStats = {
  totalArticles: 89,
  totalTickets: supportTickets.length + 45,
  avgResponseTime: "2.4 hours",
  satisfactionScore: 4.7,
  resolvedTickets: supportTickets.filter(t => t.status === 'resolved').length + 38,
  openTickets: supportTickets.filter(t => t.status === 'open').length + 7
}

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [viewMode, setViewMode] = useState<"overview" | "faq" | "tickets" | "contact">("overview")

  const filteredFAQs = faqItems.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All" || item.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "resolved": return "success"
      case "in-progress": return "default"
      case "open": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "resolved": return <CheckCircle className="h-4 w-4" />
      case "in-progress": return <Clock className="h-4 w-4" />
      case "open": return <AlertCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive"
      case "medium": return "default"
      case "low": return "secondary"
      default: return "secondary"
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Help & Support"
        subtitle={`${helpStats.totalArticles} help articles • ${helpStats.avgResponseTime} avg response time`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <MessageCircle className="w-4 h-4 mr-2" />
              Live Chat
            </Button>
            <Button size="sm">
              <Phone className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </div>
        }
      />

      {/* Help Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Book className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.totalArticles}</p>
                <p className="text-sm text-muted-foreground">Articles</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.totalTickets}</p>
                <p className="text-sm text-muted-foreground">Total Tickets</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.resolvedTickets}</p>
                <p className="text-sm text-muted-foreground">Resolved</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.openTickets}</p>
                <p className="text-sm text-muted-foreground">Open</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.avgResponseTime}</p>
                <p className="text-sm text-muted-foreground">Avg Response</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Star className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{helpStats.satisfactionScore}</p>
                <p className="text-sm text-muted-foreground">Satisfaction</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "overview" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("overview")}
        >
          <HelpCircle className="w-4 h-4 mr-2" />
          Overview
        </Button>
        <Button
          variant={viewMode === "faq" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("faq")}
        >
          <Book className="w-4 h-4 mr-2" />
          FAQ
        </Button>
        <Button
          variant={viewMode === "tickets" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("tickets")}
        >
          <MessageCircle className="w-4 h-4 mr-2" />
          Support Tickets
        </Button>
        <Button
          variant={viewMode === "contact" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("contact")}
        >
          <Phone className="w-4 h-4 mr-2" />
          Contact
        </Button>
      </div>

      {viewMode === "overview" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Quick Search */}
          <Card>
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <h2 className="text-2xl font-bold">How can we help you?</h2>
                <div className="max-w-md mx-auto">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    <Input
                      placeholder="Search for help articles, guides, or FAQs..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 h-12 text-lg"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Links */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Links</CardTitle>
              <CardDescription>Popular resources and tools</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {quickLinks.map((link, index) => {
                  const Icon = link.icon
                  return (
                    <motion.div
                      key={link.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <Icon className="h-8 w-8 text-primary" />
                            <div className="flex-1">
                              <h3 className="font-medium">{link.title}</h3>
                              <p className="text-sm text-muted-foreground">{link.description}</p>
                            </div>
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Help Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Browse by Category</CardTitle>
              <CardDescription>Find help articles organized by topic</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {helpCategories.map((category, index) => {
                  const Icon = category.icon
                  return (
                    <motion.div
                      key={category.name}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                        <CardContent className="p-4 text-center">
                          <Icon className="h-8 w-8 text-primary mx-auto mb-2" />
                          <h3 className="font-medium mb-1">{category.name}</h3>
                          <p className="text-xs text-muted-foreground mb-2">{category.description}</p>
                          <Badge variant="outline">{category.count} articles</Badge>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {viewMode === "faq" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* FAQ Search and Filter */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search frequently asked questions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="All">All Categories</option>
                  <option value="Account">Account</option>
                  <option value="Leave Management">Leave Management</option>
                  <option value="Payroll">Payroll</option>
                  <option value="Profile">Profile</option>
                  <option value="Time Tracking">Time Tracking</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* FAQ Items */}
          <div className="space-y-4">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                          <p className="text-muted-foreground">{faq.answer}</p>
                        </div>
                        <Badge variant="outline">{faq.category}</Badge>
                      </div>
                      <div className="flex items-center justify-between pt-4 border-t">
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>{faq.views} views</span>
                          <span>{faq.helpful} found helpful</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Star className="h-4 w-4 mr-1" />
                            Helpful
                          </Button>
                          <Button variant="ghost" size="sm">
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {viewMode === "tickets" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Create New Ticket */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Need more help?</h3>
                  <p className="text-muted-foreground">Create a support ticket and our team will assist you</p>
                </div>
                <Button>
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Create Ticket
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Support Tickets */}
          <Card>
            <CardHeader>
              <CardTitle>Your Support Tickets</CardTitle>
              <CardDescription>Track the status of your support requests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supportTickets.map((ticket, index) => (
                  <motion.div
                    key={ticket.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(ticket.status)}
                            <Badge variant={getStatusColor(ticket.status) as any}>
                              {ticket.status}
                            </Badge>
                          </div>
                          <Badge variant={getPriorityColor(ticket.priority) as any}>
                            {ticket.priority}
                          </Badge>
                          <Badge variant="outline">{ticket.category}</Badge>
                        </div>

                        <h4 className="font-semibold">{ticket.title}</h4>
                        <p className="text-sm text-muted-foreground">{ticket.description}</p>

                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>Created: {new Date(ticket.createdDate).toLocaleDateString()}</span>
                          <span>Updated: {new Date(ticket.lastUpdate).toLocaleDateString()}</span>
                          <span>Assigned to: {ticket.assignedTo}</span>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {viewMode === "contact" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Contact Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6 text-center">
                <Phone className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Phone Support</h3>
                <p className="text-muted-foreground mb-4">Speak directly with our support team</p>
                <p className="font-medium">+1 (555) 123-HELP</p>
                <p className="text-sm text-muted-foreground">Mon-Fri, 9AM-6PM EST</p>
                <Button className="mt-4" variant="outline">
                  <Phone className="h-4 w-4 mr-2" />
                  Call Now
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Live Chat</h3>
                <p className="text-muted-foreground mb-4">Get instant help from our chat support</p>
                <p className="font-medium">Average response: 2 minutes</p>
                <p className="text-sm text-muted-foreground">Available 24/7</p>
                <Button className="mt-4" variant="outline">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Start Chat
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Mail className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Email Support</h3>
                <p className="text-muted-foreground mb-4">Send us a detailed message</p>
                <p className="font-medium"><EMAIL></p>
                <p className="text-sm text-muted-foreground">Response within 24 hours</p>
                <Button className="mt-4" variant="outline">
                  <Mail className="h-4 w-4 mr-2" />
                  Send Email
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle>Send us a Message</CardTitle>
              <CardDescription>Fill out the form below and we'll get back to you soon</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Name</label>
                  <Input placeholder="Your full name" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input placeholder="<EMAIL>" type="email" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Subject</label>
                <Input placeholder="Brief description of your issue" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <select className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                  <option value="">Select a category</option>
                  <option value="technical">Technical Issue</option>
                  <option value="account">Account Problem</option>
                  <option value="billing">Billing Question</option>
                  <option value="feature">Feature Request</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={6}
                  placeholder="Please describe your issue in detail..."
                />
              </div>
              <Button>
                <Mail className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
