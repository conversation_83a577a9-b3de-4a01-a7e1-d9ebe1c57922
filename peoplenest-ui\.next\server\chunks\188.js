exports.id=188,exports.ids=[188],exports.modules={15487:(e,s,a)=>{Promise.resolve().then(a.bind(a,60008))},32584:(e,s,a)=>{"use strict";a.d(s,{BK:()=>o,eu:()=>d,q5:()=>c});var t=a(60687),r=a(43210),i=a(11096),n=a(4780);let l=(0,a(24224).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),d=r.forwardRef(({className:e,size:s,src:a,alt:r,fallback:d,status:m,...x},h)=>(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(i.bL,{ref:h,className:(0,n.cn)(l({size:s}),e),...x,children:[(0,t.jsx)(o,{src:a,alt:r}),(0,t.jsx)(c,{children:d})]}),m&&(0,t.jsx)("div",{className:(0,n.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===m,"bg-muted-foreground":"offline"===m,"bg-yellow-500":"away"===m,"bg-red-500":"busy"===m})})]}));d.displayName=i.bL.displayName;let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i._V,{ref:a,className:(0,n.cn)("aspect-square h-full w-full",e),...s}));o.displayName=i._V.displayName;let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i.H4,{ref:a,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",e),...s}));c.displayName=i.H4.displayName},60008:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>K});var t=a(60687),r=a(43210),i=a(16189),n=a(85814),l=a.n(n),d=a(50371),o=a(88920),c=a(49625),m=a(53411),x=a(41312),h=a(58869),u=a(86561),f=a(23928),p=a(25541),g=a(48730),b=a(40228),j=a(58887),N=a(10022),v=a(84027),y=a(99891),w=a(44709),A=a(17313),P=a(14952),k=a(47033),C=a(40083),S=a(4780),z=a(29523),D=a(32584),R=a(96834);let $=[{title:"Overview",items:[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Analytics",icon:m.A,href:"/dashboard/analytics",badge:null}]},{title:"People Management",items:[{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"},{name:"Recruitment",icon:h.A,href:"/dashboard/recruitment",badge:"12"},{name:"Onboarding",icon:u.A,href:"/dashboard/onboarding",badge:"3"}]},{title:"Operations",items:[{name:"Payroll",icon:f.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:p.A,href:"/dashboard/performance",badge:"5"},{name:"Time & Attendance",icon:g.A,href:"/dashboard/attendance",badge:"2"},{name:"Leave Management",icon:b.A,href:"/dashboard/leave",badge:"8"}]},{title:"Communication",items:[{name:"Announcements",icon:j.A,href:"/dashboard/announcements",badge:"2"},{name:"Documents",icon:N.A,href:"/dashboard/documents",badge:null}]},{title:"System",items:[{name:"Settings",icon:v.A,href:"/dashboard/settings",badge:null},{name:"Compliance",icon:y.A,href:"/dashboard/compliance",badge:null},{name:"Help & Support",icon:w.A,href:"/dashboard/help",badge:null}]}];function E({className:e}){let[s,a]=(0,r.useState)(!1),n=(0,i.usePathname)();return(0,t.jsxs)(d.P.div,{initial:!1,animate:{width:s?80:280},transition:{duration:.3,ease:"easeInOut"},className:(0,S.cn)("relative flex flex-col bg-white border-r border-gray-200 shadow-sm",e),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,t.jsx)(o.N,{mode:"wait",children:!s&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-primary rounded-lg",children:(0,t.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"HRMS Platform"})]})]})}),(0,t.jsx)(z.$,{variant:"ghost",size:"icon",onClick:()=>a(!s),className:"h-8 w-8 text-muted-foreground hover:text-foreground",children:s?(0,t.jsx)(P.A,{className:"h-4 w-4"}):(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto py-4",children:(0,t.jsx)("nav",{className:"space-y-6 px-3",children:$.map((e,a)=>(0,t.jsxs)("div",{children:[(0,t.jsx)(o.N,{children:!s&&(0,t.jsx)(d.P.h3,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:e.title})}),(0,t.jsx)("ul",{className:"space-y-1",children:e.items.map(e=>{let a=n===e.href;return(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:e.href,className:(0,S.cn)("w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",a?"bg-primary text-primary-foreground shadow-sm":"text-foreground hover:bg-muted hover:text-foreground"),children:[(0,t.jsx)(e.icon,{className:(0,S.cn)("flex-shrink-0 w-5 h-5",s?"mx-auto":"mr-3")}),(0,t.jsx)(o.N,{children:!s&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex items-center justify-between flex-1 min-w-0",children:[(0,t.jsx)("span",{className:"truncate",children:e.name}),e.badge&&(0,t.jsx)(R.E,{variant:a?"secondary":"outline",className:"ml-2 text-xs",children:e.badge})]})})]})},e.name)})})]},e.title))})}),(0,t.jsx)("div",{className:"border-t border-gray-200 p-4",children:(0,t.jsxs)("div",{className:(0,S.cn)("flex items-center",s?"justify-center":"space-x-3"),children:[(0,t.jsxs)(D.eu,{className:"h-8 w-8",children:[(0,t.jsx)(D.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,t.jsx)(D.q5,{children:"JD"})]}),(0,t.jsx)(o.N,{children:!s&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:"HR Manager"})]})}),(0,t.jsx)(o.N,{children:!s&&(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,t.jsx)(z.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})})})]})})]})}var M=a(99270),H=a(97051),q=a(12941),J=a(32192),L=a(11860);let B=[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"},{name:"Payroll",icon:f.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:p.A,href:"/dashboard/performance",badge:"5"},{name:"Leave",icon:b.A,href:"/dashboard/leave",badge:"8"},{name:"Settings",icon:v.A,href:"/dashboard/settings",badge:null}],O=[{name:"Search",icon:M.A,action:"search"},{name:"Notifications",icon:H.A,action:"notifications",badge:"3"},{name:"Profile",icon:h.A,action:"profile"}];function F({className:e}){let[s,a]=(0,r.useState)(!1),[n,c]=(0,r.useState)("/dashboard");(0,i.usePathname)();let m=e=>{switch(e){case"search":console.log("Search triggered");break;case"notifications":console.log("Notifications triggered");break;case"profile":console.log("Profile triggered")}a(!1)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(z.$,{variant:"ghost",size:"icon",onClick:()=>a(!0),className:"h-10 w-10",children:(0,t.jsx)(q.A,{className:"h-6 w-6"})}),(0,t.jsx)("div",{children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"})})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:O.map(e=>(0,t.jsxs)(z.$,{variant:"ghost",size:"icon",onClick:()=>m(e.action),className:"h-10 w-10 relative",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),e.badge&&(0,t.jsx)(R.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge})]},e.name))})]})}),(0,t.jsx)(o.N,{children:s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>a(!1),className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"}),(0,t.jsx)(d.P.div,{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"},transition:{type:"spring",damping:30,stiffness:300},className:"lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-white shadow-xl",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-primary rounded-lg flex items-center justify-center",children:(0,t.jsx)(J.A,{className:"h-6 w-6 text-primary-foreground"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"HRMS Dashboard"})]})]}),(0,t.jsx)(z.$,{variant:"ghost",size:"icon",onClick:()=>a(!1),className:"h-10 w-10",children:(0,t.jsx)(L.A,{className:"h-6 w-6"})})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto py-6",children:(0,t.jsx)("nav",{className:"px-6 space-y-2",children:B.map((e,s)=>{let a=n===e.href;return(0,t.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*s},children:(0,t.jsxs)(l(),{href:e.href,onClick:()=>{c(e.href)},className:`
                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                              ${a?"bg-primary/10 text-primary border border-primary/20":"text-muted-foreground hover:bg-muted hover:text-foreground"}
                            `,children:[(0,t.jsx)(e.icon,{className:`h-5 w-5 ${a?"text-primary":"text-muted-foreground"}`}),(0,t.jsx)("span",{className:"font-medium",children:e.name}),e.badge&&(0,t.jsx)(R.E,{variant:a?"default":"secondary",className:"ml-auto",children:e.badge})]})},e.name)})})}),(0,t.jsx)("div",{className:"p-6 border-t border-border",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"HR Manager"})]})]})})]})})]})}),(0,t.jsx)("div",{className:"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2",children:(0,t.jsx)("div",{className:"flex items-center justify-around",children:B.slice(0,4).map(e=>{let s=n===e.href;return(0,t.jsxs)(l(),{href:e.href,onClick:()=>{console.log("Bottom nav link clicked:",e.href),c(e.href)},className:`
                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative
                  ${s?"text-primary":"text-muted-foreground hover:text-foreground"}
                `,children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:e.name}),e.badge&&(0,t.jsx)(R.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge}),s&&(0,t.jsx)(d.P.div,{layoutId:"activeTab",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"})]},e.name)})})})]})}function K({children:e}){return(0,t.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,t.jsx)(E,{className:"hidden lg:flex"}),(0,t.jsx)(F,{}),(0,t.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,t.jsx)("main",{className:"flex-1 overflow-y-auto pt-16 lg:pt-0 pb-16 lg:pb-0",children:e})})]})}},63144:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx","default")},74456:(e,s,a)=>{"use strict";a.d(s,{Y:()=>k});var t=a(60687),r=a(43210),i=a(88920),n=a(50371),l=a(96474),d=a(31158),o=a(78122),c=a(80462),m=a(99270),x=a(97051),h=a(58887),u=a(78272),f=a(58869),p=a(84027),g=a(363),b=a(11437),j=a(40083),N=a(29523),v=a(89667),y=a(32584),w=a(96834),A=a(44493),P=a(4780);function k({title:e,subtitle:s,actions:a,className:k}){let[C,S]=(0,r.useState)(!1),[z,D]=(0,r.useState)(!1),[R,$]=(0,r.useState)(""),E=[{id:1,title:"New Employee Onboarding",message:"Sarah Johnson has completed her onboarding checklist",time:"2 minutes ago",unread:!0,type:"success"},{id:2,title:"Leave Request Pending",message:"Mike Chen has requested 3 days of annual leave",time:"1 hour ago",unread:!0,type:"warning"},{id:3,title:"Performance Review Due",message:"5 performance reviews are due this week",time:"3 hours ago",unread:!1,type:"info"}],M=[{name:"Add Employee",icon:l.A,action:()=>{}},{name:"Export Data",icon:d.A,action:()=>{}},{name:"Refresh",icon:o.A,action:()=>{}},{name:"Filter",icon:c.A,action:()=>{}}];return(0,t.jsx)("header",{className:(0,P.cn)("sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm",k),children:(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{children:[e&&(0,t.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:e}),s&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:s})]})}),(0,t.jsx)("div",{className:"flex-1 max-w-md mx-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(v.p,{type:"text",placeholder:"Search employees, documents, or anything...",value:R,onChange:e=>$(e.target.value),className:"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[a&&(0,t.jsx)("div",{className:"flex items-center space-x-2 mr-4",children:a}),(0,t.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:M.map(e=>(0,t.jsx)(N.$,{variant:"ghost",size:"icon",onClick:e.action,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:e.name,children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})},e.name))}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(N.$,{variant:"ghost",size:"icon",onClick:()=>S(!C),className:"h-9 w-9 text-muted-foreground hover:text-foreground relative",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),E.some(e=>e.unread)&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"})]}),(0,t.jsx)(i.N,{children:C&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-80 z-50",children:(0,t.jsx)(A.Zp,{className:"shadow-lg border-border",children:(0,t.jsxs)(A.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold text-foreground",children:"Notifications"}),(0,t.jsxs)(w.E,{variant:"secondary",className:"text-xs",children:[E.filter(e=>e.unread).length," new"]})]})}),(0,t.jsx)("div",{className:"max-h-80 overflow-y-auto",children:E.map(e=>(0,t.jsx)("div",{className:(0,P.cn)("p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors",e.unread&&"bg-primary/5"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:(0,P.cn)("w-2 h-2 rounded-full mt-2 flex-shrink-0","success"===e.type&&"bg-green-500","warning"===e.type&&"bg-yellow-500","info"===e.type&&"bg-primary")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-2",children:e.time})]})]})},e.id))}),(0,t.jsx)("div",{className:"p-3 border-t border-border",children:(0,t.jsx)(N.$,{variant:"ghost",className:"w-full text-sm",children:"View all notifications"})})]})})})})]}),(0,t.jsx)(N.$,{variant:"ghost",size:"icon",className:"h-9 w-9 text-muted-foreground hover:text-foreground",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(N.$,{variant:"ghost",onClick:()=>D(!z),className:"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80",children:[(0,t.jsxs)(y.eu,{className:"h-7 w-7",children:[(0,t.jsx)(y.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,t.jsx)(y.q5,{children:"JD"})]}),(0,t.jsx)(u.A,{className:"h-3 w-3"})]}),(0,t.jsx)(i.N,{children:z&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-56 z-50",children:(0,t.jsx)(A.Zp,{className:"shadow-lg border-gray-200",children:(0,t.jsxs)(A.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(y.eu,{className:"h-10 w-10",children:[(0,t.jsx)(y.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,t.jsx)(y.q5,{children:"JD"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"John Doe"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"HR Manager"})]})]})}),(0,t.jsxs)("div",{className:"py-2",children:[[{icon:f.A,label:"Profile",href:"/dashboard"},{icon:p.A,label:"Settings",href:"/dashboard"},{icon:g.A,label:"Dark Mode",href:"#"},{icon:b.A,label:"Language",href:"#"}].map(e=>(0,t.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:[(0,t.jsx)(e.icon,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.label})]},e.label)),(0,t.jsx)("hr",{className:"my-2"}),(0,t.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Sign out"})]})]})]})})})})]})]})]})})}},85223:(e,s,a)=>{Promise.resolve().then(a.bind(a,63144))}};