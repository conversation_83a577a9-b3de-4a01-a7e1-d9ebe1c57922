"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  FileText,
  Folder,
  Download,
  Upload,
  Share2,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  Plus,
  Star,
  Clock,
  Users,
  Lock,
  Unlock,
  Image,
  Video,
  Music,
  Archive,
  File,
  FolderOpen,
  Calendar,
  User
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock documents data
const documents = [
  {
    id: 1,
    name: "Employee Handbook 2024",
    type: "pdf",
    size: "2.4 MB",
    folder: "HR Policies",
    uploadedBy: "<PERSON>",
    uploadedDate: "2024-01-15",
    lastModified: "2024-01-20",
    downloads: 156,
    isStarred: true,
    isShared: true,
    permissions: "read-only",
    tags: ["handbook", "policies", "hr"],
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    name: "Q4 Financial Report",
    type: "xlsx",
    size: "1.8 MB",
    folder: "Finance",
    uploadedBy: "Mike Chen",
    uploadedDate: "2024-01-10",
    lastModified: "2024-01-18",
    downloads: 89,
    isStarred: false,
    isShared: false,
    permissions: "restricted",
    tags: ["finance", "quarterly", "report"],
    avatar: "/avatars/mike.jpg"
  },
  {
    id: 3,
    name: "Project Timeline Template",
    type: "pptx",
    size: "3.2 MB",
    folder: "Templates",
    uploadedBy: "Emily Davis",
    uploadedDate: "2024-01-12",
    lastModified: "2024-01-12",
    downloads: 234,
    isStarred: true,
    isShared: true,
    permissions: "edit",
    tags: ["template", "project", "timeline"],
    avatar: "/avatars/emily.jpg"
  },
  {
    id: 4,
    name: "Security Guidelines",
    type: "docx",
    size: "856 KB",
    folder: "IT Security",
    uploadedBy: "David Wilson",
    uploadedDate: "2024-01-08",
    lastModified: "2024-01-16",
    downloads: 67,
    isStarred: false,
    isShared: true,
    permissions: "read-only",
    tags: ["security", "guidelines", "it"],
    avatar: "/avatars/david.jpg"
  },
  {
    id: 5,
    name: "Team Photo 2024",
    type: "jpg",
    size: "4.1 MB",
    folder: "Company Events",
    uploadedBy: "Lisa Park",
    uploadedDate: "2024-01-22",
    lastModified: "2024-01-22",
    downloads: 45,
    isStarred: false,
    isShared: true,
    permissions: "read-only",
    tags: ["photo", "team", "event"],
    avatar: "/avatars/lisa.jpg"
  }
]

const folders = [
  { name: "HR Policies", count: 12, lastModified: "2024-01-20" },
  { name: "Finance", count: 8, lastModified: "2024-01-18" },
  { name: "Templates", count: 15, lastModified: "2024-01-16" },
  { name: "IT Security", count: 6, lastModified: "2024-01-16" },
  { name: "Company Events", count: 23, lastModified: "2024-01-22" },
  { name: "Training Materials", count: 18, lastModified: "2024-01-14" }
]

const documentStats = {
  totalDocuments: documents.length + 45, // Additional documents in folders
  totalFolders: folders.length,
  totalSize: "156.7 GB",
  sharedDocuments: documents.filter(d => d.isShared).length + 28,
  starredDocuments: documents.filter(d => d.isStarred).length + 12,
  recentUploads: documents.filter(d => new Date(d.uploadedDate) > new Date('2024-01-15')).length + 8
}

export default function DocumentsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFolder, setSelectedFolder] = useState("All")
  const [selectedType, setSelectedType] = useState("All")
  const [selectedPermission, setSelectedPermission] = useState("All")
  const [viewMode, setViewMode] = useState<"list" | "grid" | "folders">("folders")
  const [showStarredOnly, setShowStarredOnly] = useState(false)

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
                         doc.uploadedBy.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFolder = selectedFolder === "All" || doc.folder === selectedFolder
    const matchesType = selectedType === "All" || doc.type === selectedType
    const matchesPermission = selectedPermission === "All" || doc.permissions === selectedPermission
    const matchesStarred = !showStarredOnly || doc.isStarred
    
    return matchesSearch && matchesFolder && matchesType && matchesPermission && matchesStarred
  })

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf": return <FileText className="h-5 w-5 text-red-600" />
      case "docx": case "doc": return <FileText className="h-5 w-5 text-blue-600" />
      case "xlsx": case "xls": return <FileText className="h-5 w-5 text-green-600" />
      case "pptx": case "ppt": return <FileText className="h-5 w-5 text-orange-600" />
      case "jpg": case "jpeg": case "png": case "gif": return <Image className="h-5 w-5 text-purple-600" />
      case "mp4": case "avi": case "mov": return <Video className="h-5 w-5 text-pink-600" />
      case "mp3": case "wav": return <Music className="h-5 w-5 text-indigo-600" />
      case "zip": case "rar": return <Archive className="h-5 w-5 text-gray-600" />
      default: return <File className="h-5 w-5 text-gray-600" />
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "edit": return "success"
      case "read-only": return "secondary"
      case "restricted": return "destructive"
      default: return "secondary"
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "edit": return <Unlock className="h-3 w-3" />
      case "read-only": return <Eye className="h-3 w-3" />
      case "restricted": return <Lock className="h-3 w-3" />
      default: return <Eye className="h-3 w-3" />
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Document Management"
        subtitle={`${documentStats.totalDocuments} documents across ${documentStats.totalFolders} folders`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Bulk Download
            </Button>
            <Button size="sm">
              <Upload className="w-4 h-4 mr-2" />
              Upload Files
            </Button>
          </div>
        }
      />

      {/* Document Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.totalDocuments}</p>
                <p className="text-sm text-muted-foreground">Total Documents</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Folder className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.totalFolders}</p>
                <p className="text-sm text-muted-foreground">Folders</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Share2 className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.sharedDocuments}</p>
                <p className="text-sm text-muted-foreground">Shared</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Star className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.starredDocuments}</p>
                <p className="text-sm text-muted-foreground">Starred</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.recentUploads}</p>
                <p className="text-sm text-muted-foreground">Recent</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Archive className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{documentStats.totalSize}</p>
                <p className="text-sm text-muted-foreground">Total Size</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "folders" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("folders")}
          >
            <Folder className="w-4 h-4 mr-2" />
            Folders
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            List View
          </Button>
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            Grid View
          </Button>
        </div>
        <Button
          variant={showStarredOnly ? "default" : "outline"}
          size="sm"
          onClick={() => setShowStarredOnly(!showStarredOnly)}
        >
          <Star className="w-4 h-4 mr-2" />
          Starred Only
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents by name, tags, or uploader..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Folders</option>
                {folders.map(folder => (
                  <option key={folder.name} value={folder.name}>{folder.name}</option>
                ))}
              </select>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Types</option>
                <option value="pdf">PDF</option>
                <option value="docx">Word</option>
                <option value="xlsx">Excel</option>
                <option value="pptx">PowerPoint</option>
                <option value="jpg">Image</option>
              </select>
              <select
                value={selectedPermission}
                onChange={(e) => setSelectedPermission(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Permissions</option>
                <option value="edit">Edit</option>
                <option value="read-only">Read Only</option>
                <option value="restricted">Restricted</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content based on view mode */}
      {viewMode === "folders" ? (
        /* Folders View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {folders.map((folder, index) => (
            <motion.div
              key={folder.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <FolderOpen className="h-12 w-12 text-blue-600" />
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{folder.name}</h3>
                      <p className="text-sm text-muted-foreground">{folder.count} documents</p>
                      <p className="text-xs text-muted-foreground">Modified {new Date(folder.lastModified).toLocaleDateString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : viewMode === "list" ? (
        /* List View */
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/50 border-b border-border">
                  <tr>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Name</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Type</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Size</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Folder</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Uploaded By</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Modified</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Permissions</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDocuments.map((doc, index) => (
                    <motion.tr
                      key={doc.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="border-b border-border hover:bg-muted/50"
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          {getFileIcon(doc.type)}
                          <div>
                            <p className="font-medium text-foreground flex items-center space-x-2">
                              <span>{doc.name}</span>
                              {doc.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                              {doc.isShared && <Share2 className="h-4 w-4 text-blue-500" />}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {doc.tags.map(tag => (
                                <Badge key={tag} variant="outline" className="text-xs">{tag}</Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-muted-foreground uppercase text-sm">{doc.type}</td>
                      <td className="py-4 px-6 text-muted-foreground">{doc.size}</td>
                      <td className="py-4 px-6 text-muted-foreground">{doc.folder}</td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={doc.avatar} alt={doc.uploadedBy} />
                            <AvatarFallback className="text-xs">
                              {doc.uploadedBy.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{doc.uploadedBy}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-muted-foreground text-sm">
                        {new Date(doc.lastModified).toLocaleDateString()}
                      </td>
                      <td className="py-4 px-6">
                        <Badge variant={getPermissionColor(doc.permissions) as any} className="flex items-center space-x-1 w-fit">
                          {getPermissionIcon(doc.permissions)}
                          <span className="ml-1">{doc.permissions}</span>
                        </Badge>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Share2 className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Grid View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredDocuments.map((doc, index) => (
            <motion.div
              key={doc.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getFileIcon(doc.type)}
                      <span className="text-xs text-muted-foreground uppercase">{doc.type}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {doc.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                      {doc.isShared && <Share2 className="h-4 w-4 text-blue-500" />}
                    </div>
                  </div>
                  <h3 className="font-medium text-sm mb-2 line-clamp-2">{doc.name}</h3>
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex items-center justify-between">
                      <span>Size:</span>
                      <span>{doc.size}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Downloads:</span>
                      <span>{doc.downloads}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Avatar className="h-4 w-4">
                        <AvatarImage src={doc.avatar} alt={doc.uploadedBy} />
                        <AvatarFallback className="text-xs">
                          {doc.uploadedBy.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate">{doc.uploadedBy}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {doc.tags.slice(0, 2).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">{tag}</Badge>
                    ))}
                    {doc.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">+{doc.tags.length - 2}</Badge>
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-3 pt-3 border-t">
                    <Badge variant={getPermissionColor(doc.permissions) as any} className="text-xs">
                      {doc.permissions}
                    </Badge>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Share2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}
