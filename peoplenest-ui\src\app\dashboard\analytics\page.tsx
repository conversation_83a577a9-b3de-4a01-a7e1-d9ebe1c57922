"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Bar<PERSON>hart3,
  TrendingUp,
  DollarSign,
  Award,
  Clock,
  Download,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
// import { Badge } from "@/components/ui/badge"
import { Head<PERSON> } from "@/components/layout/header"
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

// Mock analytics data
const employeeGrowthData = [
  { month: 'Jan', employees: 1150, newHires: 25, departures: 8 },
  { month: 'Feb', employees: 1167, newHires: 22, departures: 5 },
  { month: 'Mar', employees: 1184, newHires: 28, departures: 11 },
  { month: 'Apr', employees: 1201, newHires: 30, departures: 13 },
  { month: 'May', employees: 1218, newHires: 27, departures: 10 },
  { month: 'Jun', employees: 1235, newHires: 32, departures: 15 }
]

const departmentData = [
  { name: 'Engineering', employees: 485, percentage: 39.3, color: '#0088FE' },
  { name: 'Sales', employees: 247, percentage: 20.0, color: '#00C49F' },
  { name: 'Marketing', employees: 148, percentage: 12.0, color: '#FFBB28' },
  { name: 'Operations', employees: 123, percentage: 10.0, color: '#FF8042' },
  { name: 'HR', employees: 86, percentage: 7.0, color: '#8884D8' },
  { name: 'Finance', employees: 74, percentage: 6.0, color: '#82CA9D' },
  { name: 'Other', employees: 72, percentage: 5.7, color: '#FFC658' }
]

const performanceMetrics = [
  { metric: 'Employee Satisfaction', current: 4.6, previous: 4.4, trend: 'up' },
  { metric: 'Retention Rate', current: 94.2, previous: 92.8, trend: 'up' },
  { metric: 'Average Performance', current: 4.3, previous: 4.1, trend: 'up' },
  { metric: 'Training Completion', current: 87.5, previous: 89.2, trend: 'down' }
]

const payrollTrends = [
  { month: 'Jan', totalPayroll: 8450000, avgSalary: 7348 },
  { month: 'Feb', totalPayroll: 8520000, avgSalary: 7305 },
  { month: 'Mar', totalPayroll: 8680000, avgSalary: 7329 },
  { month: 'Apr', totalPayroll: 8750000, avgSalary: 7287 },
  { month: 'May', totalPayroll: 8890000, avgSalary: 7301 },
  { month: 'Jun', totalPayroll: 9020000, avgSalary: 7306 }
]

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("6months")
  // const [selectedMetric, setSelectedMetric] = useState("all")

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? (
      <ArrowUpRight className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownRight className="h-4 w-4 text-red-600" />
    )
  }

  const getTrendColor = (trend: string) => {
    return trend === 'up' ? 'text-green-600' : 'text-red-600'
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Analytics Dashboard"
        subtitle="Comprehensive insights into your organization's performance and trends"
        actions={
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="1month">Last Month</option>
              <option value="3months">Last 3 Months</option>
              <option value="6months">Last 6 Months</option>
              <option value="1year">Last Year</option>
            </select>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>
        }
      />

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {performanceMetrics.map((metric, index) => (
          <motion.div
            key={metric.metric}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{metric.metric}</p>
                    <p className="text-2xl font-bold text-foreground">
                      {metric.metric.includes('Rate') || metric.metric.includes('Completion') 
                        ? `${metric.current}%` 
                        : metric.current}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(metric.trend)}
                    <span className={`text-sm font-medium ${getTrendColor(metric.trend)}`}>
                      {Math.abs(metric.current - metric.previous).toFixed(1)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Employee Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Employee Growth Trends</span>
            </CardTitle>
            <CardDescription>Monthly employee count, new hires, and departures</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={employeeGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="employees" stroke="#8884d8" strokeWidth={2} name="Total Employees" />
                <Line type="monotone" dataKey="newHires" stroke="#82ca9d" strokeWidth={2} name="New Hires" />
                <Line type="monotone" dataKey="departures" stroke="#ffc658" strokeWidth={2} name="Departures" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Department Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Department Distribution</span>
            </CardTitle>
            <CardDescription>Employee distribution across departments</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={departmentData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="employees"
                >
                  {departmentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Payroll Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Payroll Analytics</span>
            </CardTitle>
            <CardDescription>Total payroll and average salary trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={payrollTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Area yAxisId="left" type="monotone" dataKey="totalPayroll" stackId="1" stroke="#8884d8" fill="#8884d8" name="Total Payroll" />
                <Line yAxisId="right" type="monotone" dataKey="avgSalary" stroke="#82ca9d" strokeWidth={2} name="Avg Salary" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Department Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="h-5 w-5" />
              <span>Department Performance</span>
            </CardTitle>
            <CardDescription>Performance metrics by department</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={departmentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="employees" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights</CardTitle>
          <CardDescription>AI-powered insights from your data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <TrendingUp className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="font-medium text-foreground">Employee Growth Acceleration</p>
                <p className="text-sm text-muted-foreground">Your hiring rate has increased by 23% compared to last quarter, with Engineering leading the growth.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <Award className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-foreground">Improved Retention</p>
                <p className="text-sm text-muted-foreground">Employee retention has improved by 1.4% this quarter, indicating better job satisfaction.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
              <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="font-medium text-foreground">Training Completion Decline</p>
                <p className="text-sm text-muted-foreground">Training completion rates have decreased by 1.7%. Consider reviewing training programs.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
